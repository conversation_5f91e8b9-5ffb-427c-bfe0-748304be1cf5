"""
Package Operations Service

Consolidated service that handles package simulation, download, and creation operations.
This replaces the over-engineered separate services for better maintainability.
"""
import logging
from typing import List, Tuple

from django.http import HttpResponse
from pydantic import ValidationError as PydanticValidationError

from documents.models import Document
from extract.models import ExtractionBatch
from packager.models import DocumentData, Package, Packager
from packager.models import PackageEntry
from packager.pydantic_classes import SimulatePackageInput, NamingConfig, PackageRenderResult, RenderedPackageItem
from packager.utils.file_utils import create_package_zip

logger = logging.getLogger(__name__)


class PackageOperationsService:
    """
    Consolidated service for package operations including simulation, download, and creation.
    
    This service handles:
    - Package simulation with override configurations
    - Package downloads (existing and simulated)
    - Package creation and reuse logic
    - Document filtering and fetching
    """

    def __init__(self, packager: Packager):
        """
        Initialize the service with a packager instance.
        
        Args:
            packager: The Packager instance
        """
        self.packager = packager

    def simulate_package(self, input_data: SimulatePackageInput) -> Tuple[PackageRenderResult, object]:
        """
        Main entry point for package simulation.
        
        Args:
            input_data: The simulation input parameters
            
        Returns:
            Tuple of (PackageRenderResult, effective_config)
        """
        logger.info(f"simulation.start packager_id={self.packager.obfuscated_id}")

        try:
            temp_package, effective_config, document_data_list = self._create_simulated_package_instance(input_data)

            result = temp_package.render_package_data(
                document_data_queryset=document_data_list,
                include_document_content=effective_config.include_source_files_in_delivery
            )

            if effective_config.include_source_files_in_delivery:
                self._adjust_source_file_paths(result)

            logger.info(
                f"simulation.success packager_id={self.packager.obfuscated_id} "
                f"items_count={len(result.rendered_items)} errors_count={len(result.errors)}")

            return result, effective_config

        except Exception as e:
            logger.error(f"simulation.error packager_id={self.packager.obfuscated_id} error={str(e)}")
            raise

    def get_documents_for_simulation(self, input_data: SimulatePackageInput) -> List[DocumentData]:
        """Fetch DocumentData instances based on filters for simulation."""
        logger.debug(f"simulation.fetch_documents packager_id={self.packager.obfuscated_id}")

        queryset = DocumentData.objects.filter(
            data_pool=self.packager.data_pool
        ).select_related('document').prefetch_related('document__extractions')

        if input_data.document_id_filter:
            document_ids = self._resolve_document_ids(input_data.document_id_filter)
            if document_ids:
                queryset = queryset.filter(document_id__in=document_ids)

        if input_data.document_status_filter:
            queryset = queryset.filter(status__in=input_data.document_status_filter)

        if input_data.eb_id_filter:
            eb_ids = self._resolve_extraction_batch_ids(input_data.eb_id_filter)
            if eb_ids:
                queryset = queryset.filter(
                    document__extraction__extraction_batch_id__in=eb_ids
                ).distinct()

        document_count = queryset.count()
        logger.info(
            f"simulation.documents_found packager_id={self.packager.obfuscated_id} count={document_count}")

        return list(queryset)

    def download_package(self, request_data: dict) -> HttpResponse:
        """
        Main entry point for package download operations.
        
        Args:
            request_data: The request data containing download parameters
            
        Returns:
            HttpResponse with ZIP file
        """
        package_id = request_data.get('package_id')

        if package_id:
            return self._download_existing_package(request_data, package_id)
        else:
            return self._download_simulated_package(request_data)

    def _download_existing_package(self, request_data: dict, package_id: str) -> HttpResponse:
        """Handle downloading an existing package."""
        logger.info(f"download.existing.start packager_id={self.packager.obfuscated_id} package_id={package_id}")

        package = self._get_package_by_id(package_id)

        render_result = self._get_rendered_package_result(package, request_data)

        zip_filename = f"package_{package.obfuscated_id}.zip"
        zip_kwargs = self._prepare_zip_kwargs(request_data, render_result)

        response = self._create_zip_response(render_result.rendered_items, zip_filename, **zip_kwargs)

        logger.info(
            f"download.existing.success packager_id={self.packager.obfuscated_id} "
            f"package_id={package_id} filename={zip_filename}")

        return response

    def _download_simulated_package(self, request_data: dict) -> HttpResponse:
        """Handle downloading a simulated package."""
        logger.info(f"download.simulated.start packager_id={self.packager.obfuscated_id}")

        input_data = SimulatePackageInput(**request_data)

        if 'include_relationship_details_in_data_export' in request_data:
            include_relationship_details = str(
                request_data['include_relationship_details_in_data_export']).lower() in ['true', '1', 'yes']
            input_data.include_relationship_details_in_data_export = include_relationship_details

        result, _ = self.simulate_package(input_data)

        zip_kwargs = self._prepare_zip_kwargs(request_data, result)
        response = self._create_zip_response(result.rendered_items, 'simulated_package.zip', **zip_kwargs)

        logger.info(
            f"download.simulated.success packager_id={self.packager.obfuscated_id} "
            f"items_count={len(result.rendered_items)}")

        return response

    def get_working_package(self):
        """Get or create the appropriate package for processing based on the packager's schedule type."""
        from glynt_schemas.packager.packager_attributes import PackagerSchedules
        from glynt_schemas.packager.package_attributes import PackageStatus

        logger.info(
            f"packager.workflow.package_selection packager_id={self.packager.obfuscated_id} "
            f"schedule_type={self.packager.packager_schedule_type} action=get_working_package")

        if self.packager.packager_schedule_type == PackagerSchedules.STREAM.name:
            package = Package.objects.create(packager=self.packager, data_pool=self.packager.data_pool)
            logger.info(
                f"packager.workflow.package_created packager_id={self.packager.obfuscated_id} "
                f"package_id={package.obfuscated_id} type=stream")
            return package, package.id

        non_reusable_states = [
            PackageStatus.TRANSMISSION_SUCCESSFUL.name,
            PackageStatus.TRANSMISSION_FAILED.name,
            PackageStatus.PROCESSING.name,
            PackageStatus.FROZEN.name,
            PackageStatus.VERIFIED.name
        ]

        package = (self.packager.packages
                   .exclude(status__in=non_reusable_states)
                   .order_by('-created_at')
                   .first())

        if not package:
            package = Package.objects.create(packager=self.packager, data_pool=self.packager.data_pool)
            logger.info(
                f"packager.workflow.package_created packager_id={self.packager.obfuscated_id} "
                f"package_id={package.obfuscated_id} type=scheduled reason=no_reusable_package")
        elif self._has_config_changed_since_package(package):
            package = Package.objects.create(packager=self.packager, data_pool=self.packager.data_pool)
            logger.info(
                f"packager.workflow.package_created packager_id={self.packager.obfuscated_id} "
                f"package_id={package.obfuscated_id} type=scheduled reason=config_changed")
        else:
            logger.info(
                f"packager.workflow.package_reused packager_id={self.packager.obfuscated_id} "
                f"package_id={package.obfuscated_id} status={package.status}")

        return package, package.id

    def create_package(self, document_data_list: List[DocumentData]):
        """Create a new package with the provided document data."""
        package, package_id = self.get_working_package()

        for doc_data in document_data_list:
            PackageEntry.objects.get_or_create(
                package=package,
                document_data=doc_data,
                defaults={'data_pool': self.packager.data_pool}
            )

        return package

    def _resolve_document_ids(self, obfuscated_ids: List[str]) -> List[int]:
        """Resolve obfuscated document IDs to internal IDs."""
        document_ids = []
        for obfuscated_id in obfuscated_ids:
            try:
                document_id = Document.get_unobfuscated_id(obfuscated_id)
                document_ids.append(document_id)
            except Exception as e:
                logger.warning(
                    f"simulation.doc_id_error packager_id={self.packager.obfuscated_id} "
                    f"obfuscated_id={obfuscated_id} error={str(e)}")
        return document_ids

    def _resolve_extraction_batch_ids(self, obfuscated_ids: List[str]) -> List[int]:
        """Resolve obfuscated extraction batch IDs to internal IDs."""
        eb_ids = []
        for obfuscated_id in obfuscated_ids:
            try:
                eb_id = ExtractionBatch.get_unobfuscated_id(obfuscated_id)
                eb_ids.append(eb_id)
            except Exception as e:
                logger.warning(
                    f"simulation.eb_id_error packager_id={self.packager.obfuscated_id} "
                    f"obfuscated_id={obfuscated_id} error={str(e)}")
        return eb_ids

    def _get_package_by_id(self, package_id_obfuscated: str) -> Package:
        """Get package by obfuscated ID, raises exception if not found."""
        unobfuscated_id = Package.get_unobfuscated_id(package_id_obfuscated)
        return Package.objects.get(id=unobfuscated_id, packager=self.packager)

    def _get_rendered_package_result(self, package: Package, request_data: dict) -> PackageRenderResult:
        """Get rendered package result with configuration overrides."""
        # Determine include_source_files setting
        include_source_files_req = request_data.get('include_source_files')
        if include_source_files_req is not None:
            include_document_content = str(include_source_files_req).lower() in ['true', '1', 'yes']
        else:
            include_document_content = (
                package.packager.include_source_files_in_delivery if package.packager else False
            )

        include_relationship_details_req = request_data.get('include_relationship_details_in_data_export')
        if include_relationship_details_req is not None:
            effective_include_rel_details = str(include_relationship_details_req).lower() in ['true', '1', 'yes']
        elif package.packager:
            effective_include_rel_details = package.packager.include_relationship_details_in_data_export
        else:
            effective_include_rel_details = False

        original_config_backup = package._packager_config
        config_was_overridden = False
        current_config = package.get_config()

        if (include_relationship_details_req is not None or
                current_config.include_relationship_details_in_data_export != effective_include_rel_details):
            temp_config = current_config.copy(deep=True)
            temp_config.include_relationship_details_in_data_export = effective_include_rel_details
            package._packager_config = temp_config
            config_was_overridden = True

        try:
            return package.render_package_data(include_document_content=include_document_content)
        finally:
            if config_was_overridden:
                package._packager_config = original_config_backup

    def _prepare_zip_kwargs(self, request_data: dict, result: PackageRenderResult) -> dict:
        """Prepare keyword arguments for ZIP file creation."""
        zip_kwargs = {}

        if result.errors:
            zip_kwargs['errors_list'] = [
                e.dict() if hasattr(e, 'dict') and callable(e.dict) else e
                for e in result.errors
            ]

        include_duplicates = str(request_data.get('include_duplicate_fields', 'false')).lower() in [
            'true', '1', 'yes'
        ]

        if include_duplicates and hasattr(result, 'duplicate_findings') and result.duplicate_findings:
            zip_kwargs['duplicate_findings_list'] = [df.dict() for df in result.duplicate_findings]

        return zip_kwargs

    def _create_zip_response(self, items: List[RenderedPackageItem], filename: str, **kwargs) -> HttpResponse:
        """Create a ZIP file response from the rendered package items."""
        return create_package_zip(
            items,
            filename=filename,
            use_profiler_response=True,
            **kwargs
        )

    def _create_simulated_package_instance(self, input_data: SimulatePackageInput):
        """Create a temporary Package instance with override configurations for simulation."""
        base_config = self.packager.get_config()

        # Build override data
        allowed_keys = set(base_config.__dict__.keys()) if base_config else set()
        if base_config and hasattr(base_config.__class__, '__fields__'):
            allowed_keys.update(base_config.__class__.__fields__.keys())
        allowed_keys.update(
            ['source_file_naming_config', 'processed_data_naming_config', 'exclude_duplicates_from_delivery'])

        override_data = {
            key: value for key, value in input_data.dict(
                exclude_none=True,
                exclude={'document_id_filter', 'eb_id_filter', 'document_status_filter'}
            ).items()
            if key in allowed_keys
        }

        self._process_naming_config_overrides(input_data, override_data)

        sim_config = base_config.copy(update=override_data)

        if input_data.include_source_files_in_delivery is not None:
            sim_config.include_source_files_in_delivery = input_data.include_source_files_in_delivery
        if input_data.include_relationship_details_in_data_export is not None:
            sim_config.include_relationship_details_in_data_export = input_data.include_relationship_details_in_data_export
        if input_data.append_coc_fields_to_data is not None:
            sim_config.append_coc_fields_to_data = input_data.append_coc_fields_to_data
        if input_data.coc_field_config is not None:
            sim_config.coc_field_config = input_data.coc_field_config

        document_data_list = self.get_documents_for_simulation(input_data)

        if sim_config.source_file_naming_config:
            for doc_data in document_data_list:
                doc_data._temp_source_naming_config = sim_config.source_file_naming_config

        temp_package = Package(packager_config=sim_config, data_pool=self.packager.data_pool)

        return temp_package, sim_config, document_data_list

    def _process_naming_config_overrides(self, input_data: SimulatePackageInput, override_data: dict):
        """Process naming configuration overrides."""
        for config_name in ['source_file_naming_config', 'processed_data_naming_config']:
            current_value = override_data.get(config_name)
            input_value = getattr(input_data, config_name, None)

            if isinstance(current_value, dict):
                try:
                    override_data[config_name] = NamingConfig(**current_value)
                except PydanticValidationError as e:
                    logger.error(f"simulation.naming_config.parse_error packager_id={self.packager.obfuscated_id} "
                                 f"config_name={config_name} error={str(e)}")
                    del override_data[config_name]
            elif isinstance(input_value, NamingConfig):
                override_data[config_name] = input_value
            elif config_name in override_data and isinstance(current_value, dict):
                del override_data[config_name]

    def _adjust_source_file_paths(self, result: PackageRenderResult):
        """Adjust source file paths by adding 'source/' prefix."""
        for item in result.rendered_items:
            if item.source_files:
                for source_file in item.source_files:
                    source_file.filename = f"source/{source_file.filename}"

    def _has_config_changed_since_package(self, package):
        """Check if the packager configuration has changed since the package was created."""
        try:
            if not package.created_at:
                return True
            if self.packager.updated_at and package.created_at < self.packager.updated_at:
                return True
            return False
        except Exception as e:
            logger.error(f"packager.config_check.error packager_id={self.packager.obfuscated_id} error={str(e)}")
            return False
