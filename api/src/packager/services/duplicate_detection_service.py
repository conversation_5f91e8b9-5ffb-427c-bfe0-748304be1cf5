import logging
from typing import List

from django.db import transaction
from django.utils import timezone

from packager.models import DocumentData, DocumentRelationship, DocumentRelationshipType, Packager

logger = logging.getLogger(__name__)


class DuplicateDetectionService:
    """
    Service for performing duplicate detection checks on DocumentData instances
    and managing DocumentRelationship creation.
    """

    def __init__(self, document_data_pk: int):
        """
        Initialize the service with a DocumentData instance.
        """
        try:
            self.document_data = DocumentData.objects.select_related('document', 'data_pool').get(pk=document_data_pk)
        except DocumentData.DoesNotExist:
            logger.exception(f"DocumentData with pk {document_data_pk} not found.")
            self.document_data = None
        except Exception as e:
            logger.exception(f"Error initializing DuplicateDetectionService for pk {document_data_pk}: {str(e)}")
            self.document_data = None

    def _conditionally_set_relationship(self, attribute_name_on_doc: str, attribute_value_to_check,
                                        relationship_type_label: str):
        """
        Helper to find duplicates based on an attribute and set relationship
        if self.document_data is newer than the oldest other duplicate.
        """
        if not attribute_value_to_check:
            logger.debug(
                f"Skipping {relationship_type_label} check for {self.document_data.obfuscated_id}: attribute {attribute_name_on_doc} is not set.")
            return

        logger.info(
            f"Checking {relationship_type_label} for DocumentData {self.document_data.obfuscated_id} "
            f"({attribute_name_on_doc}: {attribute_value_to_check})"
        )

        potential_duplicates_qs = DocumentData.objects.filter(
            **{attribute_name_on_doc: attribute_value_to_check},
            data_pool=self.document_data.data_pool
        ).exclude(
            pk=self.document_data.pk
        ).order_by('received_at', 'id')

        if potential_duplicates_qs.exists():
            oldest_among_others = potential_duplicates_qs.first()

            is_self_document_newer = False
            if self.document_data.received_at > oldest_among_others.received_at:
                is_self_document_newer = True
            elif self.document_data.received_at == oldest_among_others.received_at:
                if self.document_data.id > oldest_among_others.id:  # Higher ID is "newer"
                    is_self_document_newer = True

            if is_self_document_newer:
                logger.info(
                    f"Found {relationship_type_label}: Original {oldest_among_others.obfuscated_id}, "
                    f"Duplicate {self.document_data.obfuscated_id} (self is newer/higher ID)."
                )
                self._create_or_update_relationship(
                    original_doc_data=oldest_among_others,
                    relationship_type_name=relationship_type_label
                )
            else:
                logger.info(
                    f"{self.document_data.obfuscated_id} is canonical for {relationship_type_label} "
                    f"check against {oldest_among_others.obfuscated_id}. No relationship set on self."
                )
        else:
            logger.info(
                f"No other documents found with the same {attribute_name_on_doc} for {self.document_data.obfuscated_id} to check for {relationship_type_label}.")

    def _check_md5_duplicates(self):
        if self.document_data.is_md5_check_completed:
            return

        with transaction.atomic():
            self._conditionally_set_relationship(
                attribute_name_on_doc='source_file_md5',
                attribute_value_to_check=self.document_data.source_file_md5,
                relationship_type_label='MD5_DUPLICATE'
            )

        self.document_data.is_md5_check_completed = True

    def _check_label_duplicates(self):
        if self.document_data.is_label_check_completed:
            return

        with transaction.atomic():
            self._conditionally_set_relationship(
                attribute_name_on_doc='source_file_name',
                attribute_value_to_check=self.document_data.source_file_name,
                relationship_type_label='LABEL_DUPLICATE'
            )

        self.document_data.is_label_check_completed = True

    def _check_content_duplicates(self):
        if self.document_data.is_content_check_completed:
            return

        if not self.document_data.document or self.document_data.document.status != 'VERIFIED':
            logger.info(f"Skipping content duplicate check for {self.document_data.obfuscated_id}:"
                        " document not VERIFIED.")
            self.document_data.is_content_check_completed = True
            return

        if not self.document_data.content_hash:
            deduplication_fields = None
            try:
                packager = Packager.objects.filter(data_pool=self.document_data.data_pool).first()
                deduplication_fields = packager.deduplication_content_fields if packager else None
            except Exception as e:
                logger.warning(f"Error getting packager deduplication fields for content check: {str(e)}")

            calculated_hash = self.document_data.calculate_content_hash(deduplication_fields=deduplication_fields)
            if calculated_hash:
                self.document_data.content_hash = calculated_hash
                self.document_data.save(update_fields=['content_hash'])
                self.document_data.refresh_from_db()
            else:
                logger.warning(f"Content hash could not be calculated for {self.document_data.obfuscated_id}. "
                               f"Skipping content duplicate check.")
                self.document_data.is_content_check_completed = True
                return

        if not self.document_data.content_hash:
            logger.info(f"Skipping content duplicate check for {self.document_data.obfuscated_id}: "
                        f"no content hash after calculation attempt.")
            self.document_data.is_content_check_completed = True
            return

        with transaction.atomic():
            self._conditionally_set_relationship(
                attribute_name_on_doc='content_hash',
                attribute_value_to_check=self.document_data.content_hash,
                relationship_type_label='CONTENT_DUPLICATE'
            )

        self.document_data.is_content_check_completed = True

    def run_checks(self, check_types: List[str]):
        if not self.document_data:
            logger.warning("Cannot run checks: DocumentData instance is not loaded.")
            return

        logger.info(f"Running duplicate checks {check_types} for DocumentData {self.document_data.obfuscated_id}")
        # TODO replace with enum
        if 'MD5' in check_types:
            self._check_md5_duplicates()
        if 'LABEL' in check_types:
            self._check_label_duplicates()
        if 'CONTENT' in check_types:
            self._check_content_duplicates()

        update_fields = ['last_check_triggered_at']
        if self.document_data.is_md5_check_completed:
            update_fields.append('is_md5_check_completed')
        if self.document_data.is_label_check_completed:
            update_fields.append('is_label_check_completed')
        if self.document_data.is_content_check_completed:
            update_fields.append('is_content_check_completed')

        self.document_data.last_check_triggered_at = timezone.now()

        final_update_fields = list(set(update_fields))  # Ensure unique fields

        if final_update_fields:
            try:
                self.document_data.save(update_fields=final_update_fields)
            except Exception as e:
                logger.exception(f"Error saving completion flags for {self.document_data.obfuscated_id}: {str(e)}")
        else:
            logger.debug(f"No fields to save for {self.document_data.obfuscated_id} after checks.")

    def _create_or_update_relationship(self, original_doc_data: DocumentData, relationship_type_name: str):
        """
        Create or update a DocumentRelationship between the original and the current document data.
        Also updates the parent_relationship field on the target DocumentData (self.document_data).
        """
        if not original_doc_data or not self.document_data:
            logger.error("Cannot create/update relationship: original_doc_data or self.document_data is missing.")
            return

        logger.info(
            f"Creating/updating relationship {relationship_type_name} between {original_doc_data.obfuscated_id} ("
            f"source) and {self.document_data.obfuscated_id} (target)")

        relationship_type, _ = DocumentRelationshipType.objects.get_or_create(
            label=relationship_type_name,
            data_pool=self.document_data.data_pool
        )

        DocumentRelationship.objects.update_or_create(
            source_document_data=original_doc_data,
            target_document_data=self.document_data,
            relationship_type=relationship_type,
            data_pool=self.document_data.data_pool,
            defaults={
                'metadata': {
                    "detection_type": relationship_type_name.lower(),
                    "detected_at": timezone.now().isoformat()
                }
            }
        )

        self.document_data.relationship_to_parent = relationship_type_name
        self.document_data.parent_file_id = str(original_doc_data.obfuscated_id)

        update_fields_for_target = ['relationship_to_parent', 'parent_file_id']
        self.document_data.save(update_fields=update_fields_for_target)

        self.document_data.refresh_from_db()
