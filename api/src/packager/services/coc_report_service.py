import csv
import io
import json
import logging
from datetime import date, datetime
from typing import Dict, Any, Optional

from django.db.models import Q
from django.utils import timezone

from organizations.models import DataPool
from packager.models import PackageEntry, DocumentData
from packager.pydantic_classes import PackageEntryStatus

logger = logging.getLogger(__name__)


class CoCReportService:
    """
    Service for generating Chain of Custody reports.
    """

    def __init__(self, data_pool: DataPool, report_start_date: Optional[date], report_end_date: Optional[date],
                 packager=None):
        """
        Initialize the service with data pool and report date range.
        Inherits date range from packager if not explicitly provided.
        """
        self.data_pool = data_pool
        self.packager = packager

        self.report_start_date, self.report_end_date = self._get_effective_date_range(
            packager, report_start_date, report_end_date
        )

    def _get_effective_date_range(self, packager, start_date: Optional[date], end_date: Optional[date]):
        """
        Determine the effective date range for the report, inheriting from packager if available.

        Args:
            packager: Packager instance that may have default date constraints
            start_date: Explicitly provided start date
            end_date: Explicitly provided end date

        Returns:
            tuple: (effective_start_date, effective_end_date)
        """
        effective_start_date = start_date
        effective_end_date = end_date if end_date is not None else timezone.now().date()

        if packager:
            if not effective_start_date and packager.coc_report_start_date:
                effective_start_date = packager.coc_report_start_date

            if packager.coc_report_end_date and (not end_date or packager.coc_report_end_date < effective_end_date):
                effective_end_date = packager.coc_report_end_date

        if effective_start_date and effective_end_date and effective_start_date > effective_end_date:
            raise ValueError(f"Start date {effective_start_date} cannot be after end date {effective_end_date}")

        return effective_start_date, effective_end_date

    def generate_report_data(self):
        """
        Generate the report data by collecting document information from package entries
        and standalone documents within the specified date range.
        """
        processed_doc_data_ids_for_report = set()
        report_data = []

        package_entries_qs = PackageEntry.objects.filter(package__data_pool=self.data_pool)
        if self.report_start_date:
            package_entries_qs = package_entries_qs.filter(
                Q(delivered_at__date__gte=self.report_start_date) |
                Q(document_data__received_at__date__gte=self.report_start_date)
            )
        if self.report_end_date:
            package_entries_qs = package_entries_qs.filter(
                Q(delivered_at__date__lte=self.report_end_date) |
                Q(document_data__received_at__date__lte=self.report_end_date)
            )

        package_entries_qs = package_entries_qs.select_related(
            'package', 'document_data__document',
            'document_data__canonical_document_data__document',  # For parent info
        ).order_by('document_data__received_at', 'delivered_at')

        for entry in package_entries_qs:
            is_delivered_in_period = entry.delivered_at and \
                                     (
                                             not self.report_start_date or entry.delivered_at.date() >=
                                             self.report_start_date) and \
                                     (not self.report_end_date or entry.delivered_at.date() <= self.report_end_date)

            is_received_in_period_not_delivered = not entry.delivered_at and \
                                                  entry.document_data.received_at and \
                                                  (
                                                          not self.report_start_date or
                                                          entry.document_data.received_at.date() >=
                                                          self.report_start_date) and \
                                                  (
                                                          not self.report_end_date or
                                                          entry.document_data.received_at.date() <=
                                                          self.report_end_date)

            if is_delivered_in_period or is_received_in_period_not_delivered:
                report_data.append(self.create_report_entry(entry.document_data, entry))
                processed_doc_data_ids_for_report.add(entry.document_data_id)

        docs_not_in_package_entries_qs = DocumentData.objects.filter(data_pool=self.data_pool)
        if self.report_start_date:
            docs_not_in_package_entries_qs = docs_not_in_package_entries_qs.filter(
                received_at__date__gte=self.report_start_date)
        if self.report_end_date:
            docs_not_in_package_entries_qs = docs_not_in_package_entries_qs.filter(
                received_at__date__lte=self.report_end_date)

        docs_not_in_package_entries_qs = docs_not_in_package_entries_qs.exclude(
            id__in=processed_doc_data_ids_for_report
        ).select_related(
            'document', 'canonical_document_data__document'
        ).order_by('received_at')

        docs_not_in_package_entries = list(docs_not_in_package_entries_qs)

        for doc_data_only in docs_not_in_package_entries:
            report_data.append(self.create_report_entry(doc_data_only, None))
        return report_data

    def generate_json_report(self):
        """
        Generates the CoC report as a JSON-serializable list of dicts.
        """
        report_data = self.generate_report_data()
        return report_data

    def generate_csv_report(self):
        """
        Generates the CoC report as a CSV string.
        """
        report_data = self.generate_report_data()
        if not report_data:
            logger.warning("No data to generate CSV report.")
            return ""

        fieldnames = self.get_field_names()

        output = io.StringIO()
        writer = csv.DictWriter(output, fieldnames=fieldnames)

        writer.writeheader()
        for row in report_data:
            cleaned_row = {}
            for field in fieldnames:
                value = row.get(field)
                if isinstance(value, (datetime, date)):
                    cleaned_row[field] = value.isoformat()
                elif isinstance(value, list):
                    cleaned_row[field] = json.dumps(value) if value else ''
                else:
                    cleaned_row[field] = value if value is not None else ''

            writer.writerow(cleaned_row)

        csv_string = output.getvalue()
        return csv_string

    def create_report_entry(self, doc_data: DocumentData, package_entry: Optional[PackageEntry] = None) -> Dict[
        str, Any]:
        """
        Create a report entry for a document, with or without a package entry.
        
        Args:
            doc_data: The DocumentData instance
            package_entry: The optional PackageEntry instance, may be None
        
        Returns:
            Dictionary with report fields
        """
        child_doc_data_ids = []
        if doc_data and doc_data.id:
            try:
                child_ids = DocumentData.objects.filter(
                    canonical_document_data_id=doc_data.id
                ).values_list('document__id', flat=True)
                child_doc_data_ids = [str(id) for id in child_ids]
            except (TypeError, ValueError):
                logger.warning(
                    f"Failed to get child documents for doc_data {doc_data.id if doc_data.id else 'unknown'}")
                child_doc_data_ids = []

        report_entry = {
            'ChainOfCustodyStartDate': self.report_start_date,
            'ChainOfCustodyEndDate': self.report_end_date,
            'ReportStartDate': self.report_start_date,
            'ReportEndDate': self.report_end_date,
            'SourceFileReceivedAt': doc_data.received_at,
            'LocationID': doc_data.location_id,
            'SourceFileID': doc_data.source_file_id,
            'SourceFileOriginalName': doc_data.source_file_name,
            'SourceFileHash': doc_data.source_file_md5,
            'SourceFileOriginalPath': doc_data.document.inbound_path if doc_data.document else None,
            'SourceFileFinalName': package_entry.filename_in_package if package_entry else None,
            'SourceFileStatus': self._map_status_for_report(doc_data, package_entry),
            'status_in_package': package_entry.status_in_package if package_entry else None,
            'ExclusionReason': package_entry.exclusion_reason if package_entry else doc_data.exclusion_reason,
            'SourceFileNumPages': doc_data.source_file_num_pages,
            'SourceFileDetectedLanguage': doc_data.source_file_detected_language,
            'SourceFileSize': doc_data.source_file_size,
            'ParentFileID': doc_data.parent_file_id,
            'ParentFileName': doc_data.canonical_document_data.source_file_name if doc_data.canonical_document_data else None,
            'RelationshipToParent': doc_data.relationship_to_parent,
            'ChildFileIDs': child_doc_data_ids,
            'PackageID': package_entry.package.obfuscated_id if package_entry else None,
            'DataOutputFileName': package_entry.filename_in_package if package_entry else None,
            'DataOutputFileTimestamp': package_entry.delivered_at if package_entry else None,
            'DeliveredAt': package_entry.delivered_at if package_entry else None,
            'OpenIssueTicketID': doc_data.open_issue_ticket_id,
            'OpenIssueSubject': doc_data.open_issue_subject,
        }
        return report_entry

    def _map_status_for_report(self, doc_data: DocumentData, package_entry: Optional[PackageEntry] = None) -> str:
        """
        Maps the document status to a standardized status for the report.
        
        Args:
            doc_data: The DocumentData instance
            package_entry: The optional PackageEntry instance
            
        Returns:
            A standardized status string: "delivered", "excluded", "research", or "pending"
        """
        if package_entry:
            if package_entry.status_in_package == PackageEntryStatus.TRANSMISSION_SUCCESSFUL.value:
                return "delivered"
            elif package_entry.status_in_package in [
                PackageEntryStatus.EXCLUDED.value,
                PackageEntryStatus.EXCLUDED_AS_DUPLICATE.value,
                PackageEntryStatus.EXCLUDED_BY_STATUS.value,
                PackageEntryStatus.EXCLUDED_BY_CONFIG.value
            ]:
                return "excluded"

        if doc_data.status == "PENDING_REVIEW":
            return "research"

        # Default for other statuses including VERIFIED
        return "pending"

    def get_field_names(self):
        """
        Returns the field names for the CSV report in the correct order.
        # TODO move to schemas 
        """
        return [
            'ChainOfCustodyStartDate', 'ChainOfCustodyEndDate', 'ReportStartDate', 'ReportEndDate',
            'ReceiptTimestamp', 'LocationID',
            'SourceFileID', 'SourceFileName', 'SourceFileHash', 'SourceFileOriginalPath', 'SourceFileFinalName',
            'SourceFileStatus', 'status_in_package', 'ExclusionReason', 'SourceFileNumPages',
            'SourceFileDetectedLanguage', 'SourceFileSize',
            'ParentFileID', 'ParentFileName', 'RelationshipToParent', 'ChildFileIDs',
            'PackageID', 'DataOutputFileName', 'DataOutputFileTimestamp', 'DeliveredAt', 'OpenIssueTicketID',
            'OpenIssueSubject'
        ]
