"""
Stream Package Service

This service extracts the business logic for stream-based package processing
from signal handlers to improve maintainability and testability.
"""
import logging

from django.db import transaction
from glynt_schemas.packager.packager_attributes import PackagerSchedules

from packager.models import DocumentData
from packager.tasks import prepare_packages
from packager.tasks import trigger_document_data_checks_task

logger = logging.getLogger(__name__)


class StreamPackageService:
    """
    Service responsible for handling stream-based package processing.
    
    This service encapsulates the logic for:
    - Processing documents for STREAM packagers
    - Triggering package preparation tasks
    - Managing stream-specific business rules
    """

    def __init__(self, data_pool):
        """
        Initialize the service with a data pool.
        
        Args:
            data_pool: The DataPool instance to process documents for
        """
        self.data_pool = data_pool

    def process_document_for_stream_packagers(self, document):
        """
        Process a newly created document for all STREAM packagers in the data pool.
        
        This method:
        1. Finds all STREAM packagers for the document's data pool
        2. Checks if the document matches each packager's filters
        3. Triggers package preparation for matching packagers
        
        Args:
            document: The Document instance that was created
        """
        try:
            stream_packagers = self._get_stream_packagers()

            if not stream_packagers.exists():
                logger.debug(f"stream.process.no_packagers data_pool_id={self.data_pool.obfuscated_id}")
                return

            logger.info(f"stream.process.found_packagers count={stream_packagers.count()} "
                        f"data_pool_id={self.data_pool.obfuscated_id}")

            for packager in stream_packagers:
                self._process_document_for_packager(document, packager)

        except Exception as e:
            logger.error(
                f"stream.process.error document_id={document.obfuscated_id} "
                f"data_pool_id={self.data_pool.obfuscated_id} error={str(e)}")

    def _get_stream_packagers(self):
        """Get all STREAM packagers for the data pool."""
        from packager.models import Packager

        return Packager.objects.filter(
            data_pool=self.data_pool,
            packager_schedule_type=PackagerSchedules.STREAM.name
        )

    def _process_document_for_packager(self, document, packager):
        """
        Process a document for a specific STREAM packager.
        
        Args:
            document: The Document instance
            packager: The Packager instance
        """
        logger.debug(
            f"stream.packager.check document_id={document.obfuscated_id} "
            f"packager_id={packager.obfuscated_id}")

        try:
            if self._document_matches_packager_filters(document, packager):
                self._trigger_package_preparation(document, packager)
            else:
                logger.debug(
                    f"stream.packager.no_match document_id={document.obfuscated_id} "
                    f"packager_id={packager.obfuscated_id}")

        except Exception as e:
            logger.error(
                f"stream.packager.error document_id={document.obfuscated_id} "
                f"packager_id={packager.obfuscated_id} error={str(e)}")

    def _document_matches_packager_filters(self, document, packager):
        """
        Check if a document matches the packager's document status filter.
        
        Args:
            document: The Document instance
            packager: The Packager instance
            
        Returns:
            bool: True if the document matches the filters
        """
        doc_status_filter = packager.document_status_filter

        if not doc_status_filter:
            logger.debug(
                f"stream.filter.no_filter packager_id={packager.obfuscated_id}")
            return True

        if document.status in doc_status_filter:
            logger.debug(
                f"stream.filter.match document_id={document.obfuscated_id} "
                f"packager_id={packager.obfuscated_id} status={document.status}")
            return True

        logger.debug(
            f"stream.filter.no_match document_id={document.obfuscated_id} "
            f"packager_id={packager.obfuscated_id} status={document.status} "
            f"filter={doc_status_filter}")
        return False

    def _trigger_package_preparation(self, document, packager):
        """
        Trigger package preparation for a document and packager.
        
        Args:
            document: The Document instance
            packager: The Packager instance
        """
        logger.info(
            f"stream.trigger.prepare_packages document_id={document.obfuscated_id} "
            f"packager_id={packager.obfuscated_id}")

        try:
            transaction.on_commit(
                lambda: prepare_packages.delay(
                    packager_id=packager.id,
                    document_id=document.id
                )
            )

            logger.info(
                f"stream.trigger.success document_id={document.obfuscated_id} "
                f"packager_id={packager.obfuscated_id}")

        except Exception as e:
            logger.error(
                f"stream.trigger.error document_id={document.obfuscated_id} "
                f"packager_id={packager.obfuscated_id} error={str(e)}")

    def process_document_data_creation(self, document_data):
        """
        Process DocumentData creation for stream packagers.
        
        This method handles the creation of DocumentData instances and
        triggers initial duplicate checks.
        
        Args:
            document_data: The DocumentData instance that was created
        """
        logger.debug(
            f"stream.document_data.created doc_data_id={document_data.obfuscated_id}")

        try:
            self._schedule_initial_checks(document_data)

            self._create_document_data_coc_event(document_data)

        except Exception as e:
            logger.error(
                f"stream.document_data.error doc_data_id={document_data.obfuscated_id} "
                f"error={str(e)}")

    def _schedule_initial_checks(self, document_data):
        """Schedule initial duplicate checks for DocumentData."""
        try:

            trigger_document_data_checks_task.delay(
                document_data_pk=document_data.pk,
                check_types=['MD5', 'LABEL']
            )

            logger.debug(
                f"stream.checks.scheduled doc_data_id={document_data.obfuscated_id}")

        except Exception as e:
            logger.error(
                f"stream.checks.error doc_data_id={document_data.obfuscated_id} "
                f"error={str(e)}")

    def _create_document_data_coc_event(self, document_data):
        """Create a Chain of Custody event for DocumentData creation."""
        try:
            from packager.utils.coc_utils import create_coc_event

            create_coc_event(
                event_type_name="DOCUMENT_DATA_CREATED",
                data_pool_id=document_data.data_pool_id,
                document=document_data.document,
                details={"document_data_id": document_data.id}
            )

            logger.debug(
                f"stream.coc.created doc_data_id={document_data.obfuscated_id}")

        except Exception as e:
            logger.error(
                f"stream.coc.error doc_data_id={document_data.obfuscated_id} "
                f"error={str(e)}")

    def process_extraction_update(self, extraction):
        """
        Process extraction updates for content-based duplicate detection.
        
        Args:
            extraction: The Extraction instance that was updated
        """
        if not extraction.document:
            logger.debug("stream.extraction.no_document")
            return False

        if extraction.document.status != 'VERIFIED':
            logger.debug(
                f"stream.extraction.not_verified document_id={extraction.document.obfuscated_id}")
            return False

        try:
            # Get or create DocumentData for the document
            document_data, created = DocumentData.objects.get_or_create(
                document=extraction.document,
                data_pool=extraction.document.data_pool,
                defaults={'status': 'CREATED'}
            )

            if created:
                logger.info(
                    f"stream.extraction.document_data_created "
                    f"doc_data_id={document_data.obfuscated_id}")

            self._trigger_content_check(document_data)

            return True

        except Exception as e:
            logger.error(
                f"stream.extraction.error extraction_id={extraction.obfuscated_id} "
                f"error={str(e)}")
            return False

    def _trigger_content_check(self, document_data):
        """Trigger content-based duplicate check for DocumentData."""
        try:
            trigger_document_data_checks_task.delay(
                document_data_pk=document_data.pk,
                check_types=['CONTENT']
            )

            logger.debug(
                f"stream.content_check.scheduled doc_data_id={document_data.obfuscated_id}")

        except Exception as e:
            logger.error(
                f"stream.content_check.error doc_data_id={document_data.obfuscated_id} "
                f"error={str(e)}")
