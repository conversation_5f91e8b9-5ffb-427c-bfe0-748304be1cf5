"""
Package Rendering Service

This service extracts the complex package rendering logic from the Package model
to improve maintainability and separation of concerns.
"""
import csv
import io
import json
import logging
from typing import List

from django.apps import apps
from django.utils import timezone
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import PackagerGroupingOptions, PackagerDataOutputFormats

from packager.pydantic_classes import PackageRenderResult, DocumentError, RenderedPackageItem
from packager.utils.duplicate_utils import get_document_relationships
from packager.utils.grouping_utils import group_documents
from packager.utils.naming_utils import render_grouped_documents

logger = logging.getLogger(__name__)


class PackageRenderingService:
    """
    Service responsible for rendering package data according to different grouping strategies.
    
    This service encapsulates the complex logic for:
    - Rendering packages with different grouping strategies
    - Handling document data formatting
    - Managing errors during rendering
    - Applying relationship details and duplicate flags
    """

    def __init__(self, package_instance):
        """
        Initialize the service with a package instance.
        
        Args:
            package_instance: The Package instance to render data for
        """
        self.package = package_instance
        self.config = package_instance.get_config()
        self.data_pool = package_instance.data_pool

    def render_package_data(self, document_data_queryset=None, include_document_content=False):
        """
        Main entry point for rendering package data.
        
        Args:
            document_data_queryset: Optional queryset of DocumentData to render
            include_document_content: Whether to include source files in the package
            
        Returns:
            PackageRenderResult: Contains rendered items and any errors
        """
        logger.info(f"package.render.start package_id={self.package.obfuscated_id if self.package.pk else 'simulated'}")

        if self.package.pk:
            self.package.last_viewed = timezone.now()
            self.package.save(update_fields=['last_viewed', 'updated_at'])

        try:
            queryset_to_use = self._get_document_queryset(document_data_queryset)
            queryset_to_use = self._filter_by_data_pool(queryset_to_use)

            include_document_content = include_document_content or self.config.include_source_files_in_delivery

            return self._render_by_strategy(queryset_to_use, include_document_content)

        except Exception as e:
            logger.exception(
                f"package.render.failed package_id={self.package.obfuscated_id if self.package.pk else 'simulated'} "
                f"error={str(e)}")
            return PackageRenderResult(
                rendered_items=[],
                errors=[DocumentError(
                    document_id="PACKAGE_RENDER_ERROR",
                    error=f"Error rendering package data: {str(e)}"
                )]
            )

    def _get_document_queryset(self, document_data_queryset):
        """Get the appropriate document queryset to use for rendering."""
        if document_data_queryset is not None:
            return document_data_queryset

        DocumentData = apps.get_model('packager', 'DocumentData')

        return DocumentData.objects.filter(
            package_entries__package=self.package,
            data_pool=self.data_pool
        ).distinct()

    def _filter_by_data_pool(self, queryset_to_use):
        """Filter queryset by data pool if needed."""
        try:
            package_data_pool_pk = self.data_pool.id if self.data_pool else None
        except AttributeError:
            package_data_pool_pk = None

        if package_data_pool_pk is None:
            return queryset_to_use

        if isinstance(queryset_to_use, list):
            return [item for item in queryset_to_use if item.data_pool_id == package_data_pool_pk]
        else:
            return queryset_to_use.filter(data_pool_id=package_data_pool_pk)

    def _render_by_strategy(self, queryset_to_use, include_document_content):
        """Render documents according to the configured grouping strategy."""
        grouping_strategy = self.config.delivery_data_grouping_strategy
        metadata_key = self.config.delivery_data_grouping_metadata_key
        output_format = self.config.output_format

        errors = []
        rendered_items = []

        if grouping_strategy == PackagerGroupingOptions.SPLIT_BY_DOC.name:
            rendered_items, doc_errors = self._render_split_by_doc(
                queryset_to_use, output_format, include_document_content
            )
            errors.extend(doc_errors)
        elif grouping_strategy == PackagerGroupingOptions.ALL_IN_ONE.name:
            rendered_items, doc_errors = self._render_all_in_one(
                queryset_to_use, output_format, include_document_content
            )
            errors.extend(doc_errors)
        elif grouping_strategy == PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name:
            rendered_items, doc_errors = self._render_metadata_match_and_merge(
                queryset_to_use, output_format, metadata_key, include_document_content
            )
            errors.extend(doc_errors)
        else:
            error_msg = f"Unsupported grouping strategy: {grouping_strategy}"
            logger.error(
                f"package.render.error package_id={self.package.obfuscated_id if self.package.pk else 'simulated'} "
                f"error={error_msg}")
            raise ValueError(error_msg)

        return PackageRenderResult(
            rendered_items=rendered_items,
            errors=errors if isinstance(errors, list) else [errors] if errors else []
        )

    def _render_split_by_doc(self, queryset_to_use, output_format, include_document_content):
        """Render documents individually using SPLIT_BY_DOC strategy."""
        errors = []
        rendered_items = []

        try:
            groups = group_documents(queryset_to_use, PackagerGroupingOptions.SPLIT_BY_DOC.name)
            if not groups:
                return [], []

            for doc_data in queryset_to_use:
                try:
                    rendered_item = self._render_single_document(doc_data, output_format)
                    if rendered_item:
                        rendered_items.append(rendered_item)
                except Exception as e:
                    logger.error(
                        f"package.render.split_by_doc.doc_error package_id="
                        f"{self.package.obfuscated_id if self.package.pk else 'simulated'} "
                        f"doc_id={doc_data.obfuscated_id} error={str(e)}")
                    errors.append(DocumentError(
                        document_id=doc_data.obfuscated_id,
                        error=f"Error rendering document data: {str(e)}"
                    ))

            return rendered_items, errors

        except Exception as e:
            logger.error(
                f"package.render.split_by_doc.failed package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(
                document_id="SPLIT_BY_DOC_PROCESSING_ERROR",
                error=f"Error processing SPLIT_BY_DOC: {str(e)}"
            ))
            return rendered_items, errors

    def _render_single_document(self, doc_data, output_format):
        """Render a single document for SPLIT_BY_DOC strategy."""
        rendered_data, updated_filename, result_msg = doc_data.render_document_data()

        if result_msg != PackageStatus.RENDER_SUCCESSFUL.name:
            raise ValueError(f"Document data rendering failed: {result_msg}")

        data = self._process_rendered_data(rendered_data)

        if self.config.include_relationship_details_in_data_export:
            self._add_relationship_details(data, doc_data)

        content_bytes = self._format_data_content(data, output_format)

        return RenderedPackageItem(
            filename=updated_filename,
            content=content_bytes
        )

    def _process_rendered_data(self, rendered_data):
        """Process rendered data into a dictionary format."""
        data = {}
        rendered_data_list = rendered_data[0] if isinstance(rendered_data, tuple) else rendered_data

        if isinstance(rendered_data_list, list):
            for item in rendered_data_list:
                if isinstance(item, dict):
                    data.update(item)
        elif isinstance(rendered_data_list, dict):
            data = rendered_data_list

        if not isinstance(data, dict):
            raise ValueError(f"Rendered data is not a dictionary after merging (type: {type(data)})")

        return data

    def _add_relationship_details(self, data, doc_data):
        """Add relationship details to the data dictionary."""
        relationships = get_document_relationships([doc_data.document.obfuscated_id], self.data_pool.id)

        is_duplicate = any(
            rel['target_document_id'] == doc_data.document.obfuscated_id for rel in relationships
        )
        data['is_duplicate'] = str(is_duplicate)

        if is_duplicate:
            canonical_rel = next((
                rel for rel in relationships
                if rel['target_document_id'] == doc_data.document.obfuscated_id
            ), None)
            if canonical_rel:
                data['duplicate_of'] = canonical_rel['source_document_id']
            else:
                data['duplicate_of'] = None

        has_duplicates = any(
            rel['source_document_id'] == doc_data.document.obfuscated_id for rel in relationships
        )
        data['has_duplicates'] = str(has_duplicates)

        if has_duplicates:
            duplicate_ids = [
                rel['target_document_id'] for rel in relationships
                if rel['source_document_id'] == doc_data.document.obfuscated_id
            ]
            data['duplicate_doc_ids'] = ",".join(duplicate_ids)
        else:
            data['duplicate_doc_ids'] = None

    def _format_data_content(self, data, output_format):
        """Format data content according to the specified output format."""
        if output_format == PackagerDataOutputFormats.CSV.name:
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=list(data.keys()))
            writer.writeheader()
            writer.writerow(data)
            return output.getvalue().encode('utf-8')
        else:
            return json.dumps(data).encode('utf-8')

    def _render_all_in_one(self, queryset_to_use, output_format, include_document_content):
        """Render all documents together using ALL_IN_ONE strategy."""
        errors = []
        rendered_items = []

        try:
            groups = group_documents(queryset_to_use, PackagerGroupingOptions.ALL_IN_ONE.name)
            valid_doc_datas = groups.get('all', [])

            if not valid_doc_datas:
                return [], []

            if not self.config.exclude_duplicates_from_delivery and self.config.include_relationship_details_in_data_export:
                try:
                    doc_ids = [dd.document.obfuscated_id for dd in valid_doc_datas if
                               dd.document and dd.document.obfuscated_id]
                    import packager.utils.duplicate_utils as duplicate_utils
                    duplicate_utils.get_document_relationships(doc_ids, self.data_pool.id)
                except Exception:
                    logger.debug("Failed to fetch document relationships for ALL_IN_ONE preview", exc_info=True)

            result = render_grouped_documents(groups, self.package, include_document_content)
            rendered_items.extend(result.rendered_items)
            errors.extend(result.errors)

        except ValueError as e:
            logger.error(
                f"package.render.all_in_one.group_error package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(document_id="ALL_IN_ONE_GROUPING_ERROR", error=f"Processing error: {str(e)}"))
        except Exception as e:
            logger.error(
                f"package.render.all_in_one.failed package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(
                document_id="ALL_IN_ONE_RENDERING_ERROR",
                error=f"Error processing ALL_IN_ONE: {str(e)}"
            ))

        return rendered_items, errors

    def _render_metadata_match_and_merge(self, queryset_to_use, output_format, metadata_key, include_document_content):
        """Render documents grouped by metadata using METADATA_MATCH_AND_MERGE strategy."""
        errors = []
        rendered_items: List[RenderedPackageItem] = []

        try:
            groups = group_documents(
                queryset_to_use,
                PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name,
                metadata_key
            )
        except ValueError as e:
            logger.error(
                f"package.render.metadata_merge.group_error package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={e}")
            errors.append(DocumentError(document_id="METADATA_GROUPING_ERROR", error=str(e)))
            return [], errors
        except Exception as e:
            logger.error(
                f"package.render.metadata_merge.unexpected_error package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={e}")
            errors.append(DocumentError(
                document_id="METADATA_UNEXPECTED_GROUPING_ERROR",
                error=f"Unexpected grouping error: {str(e)}"
            ))
            return [], errors

        if not groups:
            return [], []

        try:
            result = render_grouped_documents(groups, self.package, include_document_content)
            rendered_items.extend(result.rendered_items)
            errors.extend(result.errors)
            return rendered_items, errors
        except Exception as e:
            logger.error(
                f"package.render.metadata_merge.failed package_id="
                f"{self.package.obfuscated_id if self.package.pk else 'simulated'} error={str(e)}")
            errors.append(DocumentError(
                document_id="METADATA_RENDERING_ERROR",
                error=f"Error in metadata match and merge rendering: {str(e)}"
            ))
            return rendered_items, errors
