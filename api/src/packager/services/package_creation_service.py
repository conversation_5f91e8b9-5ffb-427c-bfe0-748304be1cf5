"""
Package Creation Service

Provides a unified interface for package creation across different contexts
with consistent validation and business rule enforcement.
"""
import logging
from typing import List, Optional, Dict, Any

from django.db import transaction
from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.packager.packager_attributes import PackagerSchedules

from packager.models import Package, DocumentData, Packager, PackageEntry
from packager.pydantic_classes import PackageEntryStatus

logger = logging.getLogger(__name__)


class PackageCreationService:
    """
    Unified service for package creation that ensures consistent business rule enforcement
    across all package creation scenarios.

    This service encapsulates the complex logic for:
    - Determining when to create new packages vs reuse existing ones
    - Handling different schedule types (STREAM, CRON, MANUAL)
    - Managing package state transitions
    - Applying business rules for package reuse
    """

    def __init__(self, packager: Packager):
        """
        Initialize the service with a packager instance.

        Args:
            packager: The packager instance that will own the created packages
        """
        self.packager = packager
        self.config = self._get_packager_config()

    def _get_packager_config(self):
        """Get the packager configuration with fallback handling."""
        try:
            return self.packager.get_config() if self.packager.get_config else None
        except (AttributeError, TypeError):
            logger.warning(f"Could not get config for packager {self.packager.obfuscated_id}")
            return None

    def get_working_package(self):
        """
        Get or create the appropriate package for processing based on the packager's schedule type.

        This method implements the business logic for package reuse and creation:
        - STREAM packages are always created new
        - Other schedule types reuse packages unless they're in terminal/locked states
        - New packages are created if configuration has changed

        Returns:
            tuple: (Package instance, package_id)
        """
        logger.info(
            f"packager.workflow.package_selection packager_id={self.packager.obfuscated_id} "
            f"schedule_type={self.packager.packager_schedule_type} action=get_working_package")

        if self.packager.packager_schedule_type == PackagerSchedules.STREAM.name:
            return self._create_stream_package()

        return self._get_or_create_scheduled_package()

    def _create_stream_package(self):
        """Create a new package for STREAM schedule type."""
        from packager.models import Package

        package = Package.objects.create(packager=self.packager, data_pool=self.packager.data_pool)
        logger.info(
            f"packager.workflow.package_created packager_id={self.packager.obfuscated_id} "
            f"package_id={package.obfuscated_id} type=stream")
        return package, package.id

    def _get_or_create_scheduled_package(self):
        """Get existing package or create new one for scheduled packagers."""
        from glynt_schemas.packager.package_attributes import PackageStatus

        # Define states where packages cannot be reused (terminal/locked states)
        non_reusable_states = [
            PackageStatus.TRANSMISSION_SUCCESSFUL.name,
            PackageStatus.TRANSMISSION_FAILED.name,
            PackageStatus.PROCESSING.name,
            PackageStatus.FROZEN.name,
            PackageStatus.VERIFIED.name
        ]

        # Get the most recently created package that can be reused
        package = (self.packager.packages
                   .exclude(status__in=non_reusable_states)
                   .order_by('-created_at')
                   .first())

        if not package:
            return self._create_scheduled_package("no_reusable_package")
        elif self.packager.has_config_changed_since_package(package):
            return self._create_scheduled_package("config_changed")
        else:
            logger.info(
                f"packager.workflow.package_reused packager_id={self.packager.obfuscated_id} "
                f"package_id={package.obfuscated_id} status={package.status}")
            return package, package.id

    def _create_scheduled_package(self, reason):
        """Create a new package for scheduled packagers."""
        from packager.models import Package

        package = Package.objects.create(packager=self.packager, data_pool=self.packager.data_pool)
        logger.info(
            f"packager.workflow.package_created packager_id={self.packager.obfuscated_id} "
            f"package_id={package.obfuscated_id} type=scheduled reason={reason}")
        return package, package.id

    def _has_config_changed_since_package(self, package):
        """
        Check if the packager configuration has changed since the package was created.

        This is a simplified version that can be enhanced with more sophisticated
        configuration change detection logic.
        """
        try:
            if not package.created_at:
                return True

            if self.packager.updated_at and package.created_at < self.packager.updated_at:
                return True

            return False
        except Exception as e:
            logger.error(f"packager.config_check.error packager_id={self.packager.obfuscated_id} error={str(e)}")
            return False

    def create_package(self,
                       document_data_list: List[DocumentData],
                       creation_context: str = "manual") -> Package:
        """
        Create a new package with consistent validation and business rules.
        
        Args:
            document_data_list: List of DocumentData objects to include in the package
            creation_context: Context in which the package is being created ('manual', 'scheduled', 'stream')

        Returns:
            Package: The created package instance
            
        Raises:
            ValueError: If validation fails or business rules are violated
        """
        if not document_data_list:
            raise ValueError("Cannot create package without documents")

        data_pool = self.packager.data_pool
        invalid_docs = [doc for doc in document_data_list if doc.document.data_pool != data_pool]
        if invalid_docs:
            raise ValueError(f"All documents must belong to data pool {data_pool.obfuscated_id}")

        self._validate_package_creation_rules(document_data_list, creation_context)

        with transaction.atomic():
            package = Package.objects.create(
                packager=self.packager,
                data_pool=data_pool
            )

            package_entries = []
            for doc_data in document_data_list:
                entry_status = self._determine_initial_entry_status(doc_data)

                package_entry = PackageEntry(
                    package=package,
                    document_data=doc_data,
                    data_pool=data_pool,
                    status_in_package=entry_status.value,
                    filename_in_package=self._generate_entry_filename(doc_data, package),
                    created_at=timezone.now()
                )
                package_entries.append(package_entry)

            PackageEntry.objects.bulk_create(package_entries)

            logger.info(f"Created package {package.obfuscated_id} with {len(document_data_list)} documents "
                        f"in context '{creation_context}'")

            return package

    def _validate_package_creation_rules(self, document_data_list: List[DocumentData],
                                         creation_context: str) -> None:
        """
        Validate business rules for package creation.
        
        Args:
            document_data_list: List of documents to validate
            creation_context: Context of package creation
            
        Raises:
            ValueError: If any business rules are violated
        """
        if self.config and self.config.document_status_filter:
            allowed_statuses = set(self.config.document_status_filter)
            invalid_docs = [
                doc for doc in document_data_list
                if doc.document.status not in allowed_statuses
            ]
            if invalid_docs:
                raise ValueError(f"Documents with invalid status found. Allowed: {allowed_statuses}")

        if self.config and self.config.exclude_duplicates_from_delivery:
            from packager.models import DocumentRelationship, DocumentRelationshipType
            duplicate_types = DocumentRelationshipType.get_duplicate_types()

            duplicate_docs = []
            for doc_data in document_data_list:
                is_duplicate = DocumentRelationship.objects.filter(
                    target_document_data_id=doc_data.id,
                    relationship_type__in=duplicate_types
                ).exists()
                if is_duplicate:
                    duplicate_docs.append(doc_data)

            if duplicate_docs:
                logger.warning(f"Found {len(duplicate_docs)} duplicate documents that will be excluded")

        incomplete_docs = [
            doc for doc in document_data_list
            if not self._is_document_ready_for_packaging(doc)
        ]
        if incomplete_docs and creation_context != "manual":
            raise ValueError(f"Found {len(incomplete_docs)} documents not ready for automated packaging")

    def _is_document_ready_for_packaging(self, doc_data: DocumentData) -> bool:
        """
        Check if a document is ready for packaging based on completion status.
        
        Args:
            doc_data: DocumentData to check
            
        Returns:
            bool: True if document is ready for packaging
        """
        if not doc_data.is_md5_check_completed:
            return False
        if not doc_data.is_label_check_completed:
            return False

        if (doc_data.document and doc_data.document.status == DocumentDataStatus.VERIFIED.name
                and not doc_data.is_content_check_completed):
            return False

        return True

    def _determine_initial_entry_status(self, doc_data: DocumentData) -> PackageEntryStatus:
        """
        Determine the initial status for a package entry based on document state.
        
        Args:
            doc_data: DocumentData to evaluate
            
        Returns:
            PackageEntryStatus: The appropriate initial status
        """
        if self.config and self.config.exclude_duplicates_from_delivery:
            from packager.models import DocumentRelationship, DocumentRelationshipType
            duplicate_types = DocumentRelationshipType.get_duplicate_types()

            is_duplicate = DocumentRelationship.objects.filter(
                target_document_data_id=doc_data.id,
                relationship_type__in=duplicate_types
            ).exists()

            if is_duplicate:
                return PackageEntryStatus.EXCLUDED_AS_DUPLICATE

        if self.config and self.config.document_status_filter:
            if doc_data.document.status not in self.config.document_status_filter:
                return PackageEntryStatus.EXCLUDED_BY_STATUS

        if not self._is_document_ready_for_packaging(doc_data):
            return PackageEntryStatus.PENDING

        return PackageEntryStatus.INCLUDED

    def _generate_entry_filename(self, doc_data: DocumentData, package: Package) -> str:
        """
        Generate the filename for a document entry in the package.

        Args:
            doc_data: DocumentData for the entry
            package: Package containing the entry

        Returns:
            str: Generated filename
        """
        try:
            from packager.utils.naming_utils import generate_filename

            config = self.config.source_file_naming_config if self.config else None
            if config:
                generated_filename = generate_filename(
                    config=config,
                    extension="pdf",
                    source_obj=doc_data.document,
                    fallback_name=doc_data.source_file_name or "document",
                    is_processed_data_config=False,
                    append_unique_id=False
                )
                # Check if generated filename is empty or just whitespace
                if generated_filename and generated_filename.strip():
                    return generated_filename
        except Exception as e:
            logger.warning(f"Error generating filename for doc {doc_data.id}: {e}")

        # Fallback to source filename
        return doc_data.source_file_name or f"document_{doc_data.id}.pdf"

    def update_package_entries_status(self, package: Package,
                                      new_status: PackageEntryStatus,
                                      entry_filter: Optional[Dict[str, Any]] = None) -> int:
        """
        Update the status of package entries with optional filtering.
        
        Args:
            package: Package to update
            new_status: New status to apply
            entry_filter: Optional filter criteria for entries to update
            
        Returns:
            int: Number of entries updated
        """
        queryset = PackageEntry.objects.filter(package=package)

        if entry_filter:
            queryset = queryset.filter(**entry_filter)

        updated_count = queryset.update(
            status_in_package=new_status.value,
            updated_at=timezone.now()
        )

        logger.info(f"Updated {updated_count} package entries to status {new_status.value}")
        return updated_count
