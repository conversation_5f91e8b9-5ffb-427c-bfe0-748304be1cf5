#!/usr/bin/env python3
"""
Script to identify and add missing tests from backup files.
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def find_test_function_content(file_path, test_name):
    """Find a test function and return its full content."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            # Find the specific test function
            match = re.match(r'(\s*)def\s+' + re.escape(test_name) + r'\s*\(', line)
            if match:
                indent = match.group(1)
                start_line = i
                
                # Find the end of the function
                end_line = len(lines)
                for j in range(i + 1, len(lines)):
                    next_line = lines[j]
                    # Look for next function/class at same or lower indentation
                    if (re.match(r'\s*def\s+', next_line) or 
                        re.match(r'\s*class\s+', next_line) or
                        re.match(r'^[a-zA-Z@]', next_line) or
                        (next_line.strip() and not next_line.startswith(indent + ' ') and not next_line.startswith(indent + '\t'))):
                        end_line = j
                        break
                
                # Return the function content
                return ''.join(lines[start_line:end_line])
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return None

def get_all_tests(directory):
    """Get all test functions from a directory."""
    test_dir = Path(directory)
    all_tests = set()
    test_to_file = {}
    total_count = 0

    for test_file in test_dir.glob('test_*.py'):
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                pattern = r'def\s+(test_[a-zA-Z0-9_]+)\s*\('
                matches = re.findall(pattern, content)
                total_count += len(matches)
                for test_name in matches:
                    all_tests.add(test_name)
                    test_to_file[test_name] = test_file.name
        except Exception as e:
            print(f"Error reading {test_file}: {e}")

    return all_tests, test_to_file, total_count

def find_missing_tests():
    """Find tests that are in backup but not in current."""
    print("=== FINDING MISSING TESTS ===")

    # Get current tests
    current_tests, current_test_to_file, current_total = get_all_tests("tests")
    print(f"Current tests: {len(current_tests)} unique ({current_total} total)")

    # Get backup tests
    backup_tests, backup_test_to_file, backup_total = get_all_tests("backup_original")
    print(f"Backup tests: {len(backup_tests)} unique ({backup_total} total)")

    # Find missing tests
    missing_tests = backup_tests - current_tests
    print(f"Missing tests: {len(missing_tests)}")

    if missing_tests:
        print("\nMissing tests:")
        for test in sorted(missing_tests):
            backup_file = backup_test_to_file[test]
            print(f"  - {test} (from {backup_file})")

    return missing_tests, backup_test_to_file

def add_missing_tests():
    """Add missing tests from backup files to current files."""
    missing_tests, backup_test_to_file = find_missing_tests()
    
    if not missing_tests:
        print("No missing tests to add!")
        return
    
    print(f"\n=== ADDING {len(missing_tests)} MISSING TESTS ===")
    
    # Group missing tests by their backup file
    tests_by_file = defaultdict(list)
    for test in missing_tests:
        backup_file = backup_test_to_file[test]
        tests_by_file[backup_file].append(test)
    
    for backup_file, tests in tests_by_file.items():
        print(f"\nProcessing {backup_file} ({len(tests)} tests):")
        
        backup_path = Path("backup_original") / backup_file
        current_path = Path("tests") / backup_file
        
        if not backup_path.exists():
            print(f"  Backup file not found: {backup_path}")
            continue
        
        # If current file doesn't exist, add to test_services.py (consolidation target)
        if not current_path.exists():
            print(f"  Current file doesn't exist: {current_path}")
            if backup_file in ['test_package_creation_service.py', 'test_package_tasks.py']:
                print(f"  Adding to test_services.py (consolidation target)")
                current_path = Path("tests") / "test_services.py"
            else:
                print(f"  Skipping - no target file identified")
                continue
        
        # Read current file
        try:
            with open(current_path, 'r', encoding='utf-8') as f:
                current_content = f.read()
        except Exception as e:
            print(f"  Error reading current file: {e}")
            continue
        
        # Get test content from backup and add to current
        tests_added = 0
        for test_name in tests:
            print(f"    Adding {test_name}...")
            
            test_content = find_test_function_content(backup_path, test_name)
            if test_content:
                # Add the test to the end of the file (before the last line if it's empty)
                if current_content.endswith('\n\n'):
                    current_content = current_content.rstrip('\n') + '\n\n' + test_content + '\n'
                else:
                    current_content += '\n' + test_content + '\n'
                tests_added += 1
                print(f"      ✓ Added {test_name}")
            else:
                print(f"      ✗ Could not find {test_name} in backup")
        
        # Write back to current file
        if tests_added > 0:
            try:
                with open(current_path, 'w', encoding='utf-8') as f:
                    f.write(current_content)
                print(f"  ✓ Updated {current_path} with {tests_added} tests")
            except Exception as e:
                print(f"  ✗ Error writing to {current_path}: {e}")

def verify_addition():
    """Verify that tests have been added correctly."""
    print("\n=== VERIFICATION ===")

    # Get current tests after addition
    current_tests, _, current_total = get_all_tests("tests")
    backup_tests, _, backup_total = get_all_tests("backup_original")

    print(f"Current tests: {len(current_tests)} unique ({current_total} total)")
    print(f"Backup tests: {len(backup_tests)} unique ({backup_total} total)")
    print(f"Target: {backup_total}")

    missing = backup_tests - current_tests
    if missing:
        print(f"Still missing: {len(missing)} tests")
        for test in sorted(missing):
            print(f"  - {test}")
    else:
        print("✓ All unique tests successfully added!")
        if current_total >= backup_total:
            print("✓ Total count target reached!")
        else:
            print(f"⚠ Total count: {current_total}/{backup_total} (need {backup_total - current_total} more)")

if __name__ == '__main__':
    add_missing_tests()
    verify_addition()
