#!/usr/bin/env python3
"""
Script to compare current local tests with GitHub branch tests.
"""

import re
import requests
import base64
from pathlib import Path

def get_github_file_content(file_path, branch="GL-6455-coc-model-and-dupe-relation"):
    """Get file content from GitHub API."""
    url = f"https://api.github.com/repos/PaceWebApp/glynt-api/contents/{file_path}"
    params = {"ref": branch}
    
    try:
        response = requests.get(url, params=params)
        if response.status_code == 200:
            data = response.json()
            # Decode base64 content
            content = base64.b64decode(data['content']).decode('utf-8')
            return content
        else:
            print(f"Failed to get {file_path}: {response.status_code}")
            return None
    except Exception as e:
        print(f"Error getting {file_path}: {e}")
        return None

def extract_test_functions(content):
    """Extract test function names from file content."""
    if not content:
        return []
    
    pattern = r'def\s+(test_[a-zA-Z0-9_]+)\s*\('
    matches = re.findall(pattern, content)
    return matches

def get_github_tests():
    """Get all test functions from GitHub branch."""
    # List of test files from GitHub API response
    test_files = [
        "test_additive_filtering.py",
        "test_append_coc_fields.py", 
        "test_coc_report_service.py",
        "test_coc_utils.py",
        "test_content_hash_and_duplicates.py",
        "test_content_hashing.py",
        "test_document_data_content_hash.py",
        "test_duplicate_detection_content_hash.py",
        "test_duplicate_detection_service.py",
        "test_duplicate_utils.py",
        "test_formatting_utils.py",
        "test_models.py",
        "test_naming_utils.py",
        "test_package_creation_service.py",
        "test_package_tasks.py",
        "test_packager_coc_fields.py",
        "test_performance_optimizations.py",
        "test_pydantic_classes.py",
        "test_relationship.py",
        "test_signals.py",
        "test_tasks.py",
        "test_utils.py",
        "test_views.py"
    ]
    
    github_tests = set()
    github_test_to_file = {}
    total_github_tests = 0
    
    print("Getting tests from GitHub branch...")
    for test_file in test_files:
        file_path = f"api/src/packager/tests/{test_file}"
        content = get_github_file_content(file_path)
        
        if content:
            tests = extract_test_functions(content)
            total_github_tests += len(tests)
            print(f"  {test_file}: {len(tests)} tests")
            
            for test in tests:
                github_tests.add(test)
                github_test_to_file[test] = test_file
        else:
            print(f"  {test_file}: Failed to get content")
    
    print(f"\nGitHub branch total: {total_github_tests} tests")
    print(f"GitHub branch unique: {len(github_tests)} tests")
    
    return github_tests, github_test_to_file, total_github_tests

def get_local_tests():
    """Get all test functions from local files."""
    tests_dir = Path("tests")
    local_tests = set()
    local_test_to_file = {}
    total_local_tests = 0
    
    print("\nGetting tests from local files...")
    for test_file in tests_dir.glob('test_*.py'):
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                tests = extract_test_functions(content)
                total_local_tests += len(tests)
                print(f"  {test_file.name}: {len(tests)} tests")
                
                for test in tests:
                    local_tests.add(test)
                    local_test_to_file[test] = test_file.name
        except Exception as e:
            print(f"  Error reading {test_file}: {e}")
    
    print(f"\nLocal total: {total_local_tests} tests")
    print(f"Local unique: {len(local_tests)} tests")
    
    return local_tests, local_test_to_file, total_local_tests

def main():
    """Main comparison function."""
    print("=== COMPARING LOCAL TESTS WITH GITHUB BRANCH ===")
    
    # Get tests from both sources
    github_tests, github_test_to_file, github_total = get_github_tests()
    local_tests, local_test_to_file, local_total = get_local_tests()
    
    # Find differences
    missing_from_local = github_tests - local_tests
    extra_in_local = local_tests - github_tests
    
    print(f"\n=== COMPARISON RESULTS ===")
    print(f"GitHub branch: {len(github_tests)} unique ({github_total} total)")
    print(f"Local: {len(local_tests)} unique ({local_total} total)")
    print(f"Missing from local: {len(missing_from_local)}")
    print(f"Extra in local: {len(extra_in_local)}")
    
    if missing_from_local:
        print(f"\n=== MISSING FROM LOCAL ({len(missing_from_local)} tests) ===")
        for test in sorted(missing_from_local):
            github_file = github_test_to_file.get(test, "unknown")
            print(f"  - {test} (from {github_file})")
    
    if extra_in_local:
        print(f"\n=== EXTRA IN LOCAL ({len(extra_in_local)} tests) ===")
        print("These are the 'mysterious' extra tests:")
        for test in sorted(extra_in_local):
            local_file = local_test_to_file.get(test, "unknown")
            print(f"  - {test} (in {local_file})")

if __name__ == '__main__':
    main()
