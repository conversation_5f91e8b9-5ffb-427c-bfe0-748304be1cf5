#!/usr/bin/env python3
"""
Script to remove duplicate tests from packager test files.
"""

import os
import re
from pathlib import Path
from collections import defaultdict

def find_test_function_locations(file_path):
    """Find test function names and their line ranges in a file."""
    test_functions = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        for i, line in enumerate(lines):
            # Find test function definitions
            match = re.match(r'\s*def\s+(test_[a-zA-Z0-9_]+)\s*\(', line)
            if match:
                test_name = match.group(1)
                start_line = i + 1  # 1-based line numbers
                
                # Find the end of the function by looking for the next function or class
                end_line = len(lines)
                for j in range(i + 1, len(lines)):
                    next_line = lines[j]
                    # Look for next function/class at same or lower indentation
                    if (re.match(r'\s*def\s+', next_line) or 
                        re.match(r'\s*class\s+', next_line) or
                        re.match(r'^[a-zA-Z@]', next_line)):  # Top-level code
                        end_line = j
                        break
                
                test_functions[test_name] = (start_line, end_line)
                
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return test_functions

def remove_duplicate_tests():
    """Remove duplicate tests from files."""
    tests_dir = Path("tests")
    
    # Known duplicates from our analysis
    duplicates_to_remove = [
        ("test_models.py", "test_document_data_creation"),  # Remove one copy
        ("test_utilities.py", "test_flatten_dict"),  # Remove one copy
        ("test_utilities.py", "test_flatten_dict_with_nested_arrays"),
        ("test_utilities.py", "test_format_csv"),
        ("test_utilities.py", "test_format_csv_mixed_key_types"),
        ("test_utilities.py", "test_format_data"),
        ("test_utilities.py", "test_format_data_unsupported_format"),
        ("test_utilities.py", "test_format_json"),
        ("test_content_and_duplicates.py", "test_preview_package_include_relationship_details_override"),  # Remove from this file, keep in test_views.py
    ]
    
    for file_name, test_name in duplicates_to_remove:
        file_path = tests_dir / file_name
        
        if not file_path.exists():
            print(f"File not found: {file_path}")
            continue
            
        print(f"Removing {test_name} from {file_name}")
        
        # Get test locations
        test_locations = find_test_function_locations(file_path)
        
        if test_name not in test_locations:
            print(f"  Test {test_name} not found in {file_name}")
            continue
            
        start_line, end_line = test_locations[test_name]
        print(f"  Found at lines {start_line}-{end_line}")
        
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Remove the test function (convert to 0-based indexing)
        new_lines = lines[:start_line-1] + lines[end_line:]
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"  Removed {test_name} from {file_name}")

def verify_removal():
    """Verify that duplicates have been removed."""
    tests_dir = Path("tests")
    test_to_files = defaultdict(list)
    
    # Find all test files
    test_files = list(tests_dir.glob('test_*.py'))
    
    # Analyze each test file
    for test_file in test_files:
        test_locations = find_test_function_locations(test_file)
        for test_name in test_locations.keys():
            test_to_files[test_name].append(test_file.name)
    
    # Find remaining duplicates
    duplicates = {test: files for test, files in test_to_files.items() if len(files) > 1}
    
    print(f"\n=== VERIFICATION ===")
    if duplicates:
        print(f"Remaining duplicates: {len(duplicates)}")
        for test_name, files in duplicates.items():
            print(f"  {test_name}: {files}")
    else:
        print("No duplicates found! ✓")
    
    # Count total tests
    total_tests = sum(len(test_locations) for test_locations in [find_test_function_locations(f) for f in test_files])
    unique_tests = len(test_to_files)
    
    print(f"Total tests: {total_tests}")
    print(f"Unique tests: {unique_tests}")
    print(f"Expected after removal: 295")

if __name__ == '__main__':
    print("Removing duplicate tests...")
    remove_duplicate_tests()
    verify_removal()
