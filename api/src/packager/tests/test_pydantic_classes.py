from datetime import datetime

from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats, PackagerGroupingOptions

from packager.pydantic_classes import PackagerConfig, SimulatePackageInput
from packager.tests.base import FastPackagerTestCase


class PackagerConfigPydanticTests(FastPackagerTestCase):

    def test_packager_config_coc_fields(self):
        """Test that the PackagerConfig Pydantic model handles CoC fields correctly."""
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 12, 31)

        # Create a PackagerConfig with CoC fields
        config = PackagerConfig(
            output_format=PackagerDataOutputFormats.JSON.name,
            delivery_data_grouping_strategy=PackagerGroupingOptions.ALL_IN_ONE.name,
            exclude_duplicates_from_delivery=True,
            coc_report_start_date=start_date,
            coc_report_end_date=end_date,
            coc_report_fields_config=["field1", "field2"]
        )

        # Verify fields are set correctly
        self.assertTrue(config.exclude_duplicates_from_delivery)
        self.assertEqual(config.coc_report_start_date, start_date)
        self.assertEqual(config.coc_report_end_date, end_date)
        self.assertEqual(config.coc_report_fields_config, ["field1", "field2"])

    def test_packager_config_defaults(self):
        """Test that the PackagerConfig Pydantic model sets defaults correctly."""
        # Create a minimal PackagerConfig
        config = PackagerConfig(
            output_format=PackagerDataOutputFormats.JSON.name,
            delivery_data_grouping_strategy=PackagerGroupingOptions.ALL_IN_ONE.name
        )

        # Verify defaults
        self.assertFalse(config.exclude_duplicates_from_delivery)
        self.assertIsNone(config.coc_report_start_date)
        self.assertIsNone(config.coc_report_end_date)
        self.assertEqual(config.coc_report_fields_config, [])

    def test_simulate_package_input_exclude_duplicates(self):
        """Test that the SimulatePackageInput Pydantic model handles exclude_duplicates_from_delivery."""
        # Create SimulatePackageInput with exclude_duplicates_from_delivery
        simulate_input = SimulatePackageInput(
            exclude_duplicates_from_delivery=True
        )

        # Verify field is set correctly
        self.assertTrue(simulate_input.exclude_duplicates_from_delivery)

        # Create minimal SimulatePackageInput
        simulate_input_minimal = SimulatePackageInput()

        # Verify field default is None
        self.assertIsNone(simulate_input_minimal.exclude_duplicates_from_delivery)

    def test_simulate_package_input_append_coc_fields(self):
        """Test that SimulatePackageInput handles append_coc_fields_to_data."""
        simulate_input = SimulatePackageInput(
            append_coc_fields_to_data=True
        )
        self.assertTrue(simulate_input.append_coc_fields_to_data)

        simulate_input_minimal = SimulatePackageInput()
        self.assertIsNone(simulate_input_minimal.append_coc_fields_to_data)

    def test_simulate_package_input_coc_field_config(self):
        """Test that SimulatePackageInput handles coc_field_config."""
        simulate_input = SimulatePackageInput(
            coc_field_config=["field1", "field2"]
        )
        self.assertEqual(simulate_input.coc_field_config, ["field1", "field2"])

        simulate_input_minimal = SimulatePackageInput()
        self.assertIsNone(simulate_input_minimal.coc_field_config)
