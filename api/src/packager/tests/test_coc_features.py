"""
Consolidated Chain of Custody (CoC) feature tests.

This module consolidates tests from:
- test_coc_report_service.py (9 tests)
- test_coc_utils.py (2 tests)  
- test_append_coc_fields.py (3 tests)

Total: 14 tests covering CoC reporting, utilities, and field appending.
"""
import csv
import io
import json
import unittest
import uuid
from datetime import date, datetime
from unittest.mock import MagicMock, patch

from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus

from documents.tests.util import create_document
from organizations.models import DataPool
from organizations.tests.util import create_data_pool, create_organization
from packager.models import (
    DocumentData, Package, PackageEntry, Packager
)
from packager.pydantic_classes import PackageEntryStatus, PackageRenderResult, RenderedPackageItem
from packager.services.coc_report_service import CoCReportService
from packager.tests.base import TransactionPackagerTestCase
from packager.tests.util import create_packager
from packager.utils.coc_utils import get_canonical_document_data, get_duplicate_family
from packager.utils.naming_utils import render_grouped_documents


# Helper functions for CoC report service tests
def create_mock_document_data(data_pool, **kwargs):
    mock_dd = MagicMock()
    mock_dd.pk = kwargs.get('pk', 1)
    mock_dd.id = mock_dd.pk
    mock_dd.data_pool = data_pool
    mock_dd.obfuscated_id = kwargs.get('obfuscated_id', f"dd_{uuid.uuid4().hex}")
    mock_dd.source_file_id = kwargs.get('source_file_id', f"sf_{uuid.uuid4().hex}")
    mock_dd.source_file_name = kwargs.get('source_file_name', "test_file.pdf")
    mock_dd.source_file_md5 = kwargs.get('source_file_md5', "md5hash")
    mock_dd.source_file_num_pages = kwargs.get('source_file_num_pages', 10)
    mock_dd.source_file_size = kwargs.get('source_file_size', 1024)
    mock_dd.source_file_detected_language = kwargs.get('source_file_detected_language', "en")
    mock_dd.source_file_original_path = kwargs.get('source_file_original_path', "/inbound/test_file.pdf")
    mock_dd.received_at = kwargs.get('received_at', timezone.now())
    mock_dd.status = kwargs.get('status', "VERIFIED")
    mock_dd.parent_file_id = kwargs.get('parent_file_id', None)
    mock_dd.relationship_to_parent = kwargs.get('relationship_to_parent', None)
    mock_dd.document = MagicMock()
    mock_dd.document.obfuscated_id = mock_dd.source_file_id
    mock_dd.document.inbound_path = mock_dd.source_file_original_path
    return mock_dd


def create_mock_package(data_pool, **kwargs):
    mock_package = MagicMock(spec=Package)
    mock_package.data_pool = data_pool
    mock_package.obfuscated_id = kwargs.get('obfuscated_id', f"pkg_{unittest.mock.MagicMock().uuid}")
    mock_package.packager = MagicMock(spec=Packager)
    mock_package.packager.obfuscated_id = kwargs.get('packager_id', f"pkr_{unittest.mock.MagicMock().uuid}")
    return mock_package


def create_mock_package_entry(package, doc_data, **kwargs):
    mock_pe = MagicMock()
    mock_pe.package = package
    mock_pe.document_data = doc_data
    mock_pe.status_in_package = kwargs.get('status_in_package', PackageEntryStatus.INCLUDED.value)
    mock_pe.filename_in_package = kwargs.get('filename_in_package', "final_file.json")
    mock_pe.delivered_at = kwargs.get('delivered_at', None)
    mock_pe.exclusion_reason = kwargs.get('exclusion_reason', None)
    return mock_pe


class CoCReportServiceTests(TransactionPackagerTestCase):
    """Tests for CoC report service functionality."""

    def setUpTestData(self):
        """Set up test data for CoC report service tests."""
        self.data_pool = MagicMock(spec=DataPool)
        self.data_pool.obfuscated_id = "dp_123"
        self.data_pool.id = 1
        self.start_date = date(2024, 1, 1)
        self.end_date = date(2024, 12, 31)

    @patch('packager.services.coc_report_service.PackageEntry.objects')
    @patch('packager.services.coc_report_service.DocumentData.objects')
    def test_generate_report_data_with_package_entries(self, mock_doc_data_objects, mock_package_entry_objects):
        """Test that generate_report_data includes documents from package entries within the date range."""
        # Create mock data
        doc_data1 = create_mock_document_data(self.data_pool, received_at=datetime(2024, 6, 1, tzinfo=timezone.utc))
        package1 = create_mock_package(self.data_pool)
        package_entry1 = create_mock_package_entry(package1, doc_data1,
                                                   delivered_at=datetime(2024, 6, 15, tzinfo=timezone.utc))

        doc_data2 = create_mock_document_data(self.data_pool,
                                              received_at=datetime(2023, 12, 1, tzinfo=timezone.utc))
        package2 = create_mock_package(self.data_pool)
        package_entry2 = create_mock_package_entry(package2, doc_data2, delivered_at=datetime(2024, 1, 5,
                                                                                              tzinfo=timezone.utc))

        doc_data3 = create_mock_document_data(self.data_pool, received_at=datetime(2024, 7, 1, tzinfo=timezone.utc))
        package3 = create_mock_package(self.data_pool)
        package_entry3 = create_mock_package_entry(package3, doc_data3, delivered_at=None)

        # Set up mock for package entries query
        mock_pe_filter = MagicMock()
        mock_pe_select = MagicMock()
        mock_pe_order = MagicMock()

        mock_package_entry_objects.filter.return_value = mock_pe_filter
        mock_pe_filter.filter.return_value = mock_pe_filter
        mock_pe_filter.select_related.return_value = mock_pe_select
        mock_pe_select.order_by.return_value = mock_pe_order

        mock_pe_order.__iter__.return_value = iter([package_entry1, package_entry2, package_entry3])
        mock_pe_order.__len__.return_value = 3

        # Set up mock for standalone document data query
        mock_dd_filter = MagicMock()
        mock_dd_exclude = MagicMock()
        mock_dd_select = MagicMock()
        mock_dd_order = MagicMock()

        mock_doc_data_objects.filter.return_value = mock_dd_filter
        mock_dd_filter.filter.return_value = mock_dd_filter
        mock_dd_filter.exclude.return_value = mock_dd_exclude
        mock_dd_exclude.select_related.return_value = mock_dd_select
        mock_dd_select.order_by.return_value = mock_dd_order

        mock_dd_order.__iter__.return_value = iter([])
        mock_dd_order.__len__.return_value = 0

        # Create service and generate report
        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_data = service.generate_report_data()

        # Assertions
        self.assertEqual(len(report_data), 3)

    @patch('packager.services.coc_report_service.PackageEntry.objects')
    @patch('packager.services.coc_report_service.DocumentData.objects')
    def test_generate_report_data_with_docs_not_in_packages(self, mock_doc_data_objects, mock_package_entry_objects):
        """Test that generate_report_data includes documents received within the date range but not in any package entry."""
        # Create mock data
        doc_data1 = create_mock_document_data(self.data_pool, received_at=datetime(2024, 6, 1, tzinfo=timezone.utc))
        doc_data2 = create_mock_document_data(self.data_pool, received_at=datetime(2024, 7, 1, tzinfo=timezone.utc))

        # Set up mocks for package entries - no entries
        mock_pe_filter = MagicMock()
        mock_pe_select = MagicMock()
        mock_pe_order = MagicMock()
        mock_package_entry_objects.filter.return_value = mock_pe_filter
        mock_pe_filter.select_related.return_value = mock_pe_select
        mock_pe_select.order_by.return_value = mock_pe_order
        mock_pe_order.__iter__.return_value = iter([])

        # Set up mocks for document data - return our test docs
        mock_dd_filter = MagicMock()
        mock_dd_exclude = MagicMock()
        mock_dd_select = MagicMock()
        mock_dd_order = MagicMock()

        mock_doc_data_objects.filter.return_value = mock_dd_filter
        mock_dd_filter.filter.return_value = mock_dd_filter
        mock_dd_filter.exclude.return_value = mock_dd_exclude
        mock_dd_exclude.select_related.return_value = mock_dd_select
        mock_dd_select.order_by.return_value = mock_dd_order

        docs_list = [doc_data1, doc_data2]
        mock_dd_order.__iter__.return_value = iter(docs_list)
        mock_dd_order.__len__.return_value = 2

        # Create service and generate report
        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_data = service.generate_report_data()

        # Assertions
        self.assertEqual(len(report_data), 2)

    @patch('packager.services.coc_report_service.PackageEntry.objects')
    @patch('packager.services.coc_report_service.DocumentData.objects')
    def test_create_report_entry(self, mock_doc_data_objects, mock_package_entry_objects):
        """Test that create_report_entry maps fields correctly."""
        # Mock the return values of the queries in __init__ to avoid hitting the ORM
        mock_package_entry_objects.filter.return_value = MagicMock()
        mock_package_entry_objects.filter.return_value.select_related.return_value = MagicMock()
        mock_package_entry_objects.filter.return_value.select_related.return_value.order_by.return_value = []

        mock_doc_data_objects.filter.return_value = MagicMock()
        mock_doc_data_objects.filter.return_value.exclude.return_value = MagicMock()
        mock_doc_data_objects.filter.return_value.exclude.return_value.select_related.return_value = MagicMock()
        mock_doc_data_objects.filter.return_value.exclude.return_value.select_related.return_value.order_by \
            .return_value = []

        doc_data = create_mock_document_data(
            self.data_pool,
            source_file_id="sf_abc",
            source_file_name="original.pdf",
            source_file_md5="md5_abc",
            source_file_num_pages=10,
            source_file_size=1024,
            source_file_detected_language="en",
            source_file_original_path="/inbound/original.pdf",
            received_at=datetime(2024, 5, 10, 10, 0, 0, tzinfo=timezone.utc),
            status="VERIFIED",
            exclusion_reason="Duplicate content",
            parent_file_id="sf_xyz",
            relationship_to_parent="CONTENT_DUPLICATE"
        )
        package = create_mock_package(self.data_pool, obfuscated_id="pkg_def")
        package_entry = create_mock_package_entry(
            package,
            doc_data,
            status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.value,
            filename_in_package="final.json",
            delivered_at=datetime(2024, 5, 20, 11, 0, 0, tzinfo=timezone.utc),
            exclusion_reason="duplicate"
        )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_entry = service.create_report_entry(doc_data, package_entry)

        # Assertions for field mapping
        self.assertEqual(report_entry['ChainOfCustodyStartDate'], self.start_date)
        self.assertEqual(report_entry['ChainOfCustodyEndDate'], self.end_date)
        self.assertEqual(report_entry['SourceFileReceivedAt'], doc_data.received_at)
        self.assertEqual(report_entry['SourceFileID'], doc_data.source_file_id)
        self.assertEqual(report_entry['SourceFileOriginalName'], doc_data.source_file_name)
        self.assertEqual(report_entry['SourceFileOriginalPath'], "/inbound/original.pdf")
        self.assertEqual(report_entry['SourceFileFinalName'], package_entry.filename_in_package)
        self.assertIn('SourceFileStatus', report_entry)
        self.assertEqual(report_entry['ExclusionReason'], package_entry.exclusion_reason)
        self.assertEqual(report_entry['ParentFileID'], doc_data.parent_file_id)
        self.assertEqual(report_entry['RelationshipToParent'], doc_data.relationship_to_parent)
        self.assertEqual(report_entry['PackageID'], package_entry.package.obfuscated_id)
        self.assertEqual(report_entry['DeliveredAt'], package_entry.delivered_at)
        self.assertEqual(report_entry['SourceFileNumPages'], doc_data.source_file_num_pages)
        self.assertEqual(report_entry['SourceFileDetectedLanguage'], doc_data.source_file_detected_language)
        self.assertEqual(report_entry['SourceFileSize'], doc_data.source_file_size)

    def test_create_report_entry_no_package_entry(self):
        """Test create_report_entry when no package entry is provided."""
        doc_data = create_mock_document_data(
            self.data_pool,
            source_file_id="sf_abc",
            received_at=datetime(2024, 5, 10, 10, 0, 0, tzinfo=timezone.utc),
            status="PENDING_REVIEW",
            exclusion_reason="Not in scope"
        )

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        report_entry = service.create_report_entry(doc_data, None)

        # Assertions for field mapping when no package entry
        self.assertEqual(report_entry['ChainOfCustodyStartDate'], self.start_date)
        self.assertEqual(report_entry['ChainOfCustodyEndDate'], self.end_date)
        self.assertEqual(report_entry['SourceFileReceivedAt'], doc_data.received_at)
        self.assertEqual(report_entry['SourceFileID'], doc_data.source_file_id)
        self.assertIsNone(report_entry['SourceFileFinalName'])
        self.assertIn('SourceFileStatus', report_entry)
        self.assertEqual(report_entry['ExclusionReason'], doc_data.exclusion_reason)
        self.assertIsNone(report_entry['PackageID'])
        self.assertIsNone(report_entry['DeliveredAt'])

    def test_map_status_for_report_delivered(self):
        """Test status mapping for TRANSMISSION_SUCCESSFUL package entry."""
        doc_data = create_mock_document_data(self.data_pool, status="VERIFIED")
        package_entry = create_mock_package_entry(MagicMock(), doc_data,
                                                  status_in_package=PackageEntryStatus.TRANSMISSION_SUCCESSFUL.value)
        service = CoCReportService(self.data_pool, None, None)
        status = service._map_status_for_report(doc_data, package_entry)
        self.assertEqual(status, "delivered")

    def test_map_status_for_report_excluded_package_entry(self):
        """Test status mapping for EXCLUDED package entry statuses."""
        doc_data = create_mock_document_data(self.data_pool, status="VERIFIED")
        service = CoCReportService(self.data_pool, None, None)

        excluded_statuses = [
            PackageEntryStatus.EXCLUDED,
            PackageEntryStatus.EXCLUDED_AS_DUPLICATE,
            PackageEntryStatus.EXCLUDED_BY_STATUS,
            PackageEntryStatus.EXCLUDED_BY_CONFIG
        ]
        for pe_status in excluded_statuses:
            package_entry = create_mock_package_entry(MagicMock(), doc_data, status_in_package=pe_status.value)
            status = service._map_status_for_report(doc_data, package_entry)
            self.assertEqual(status, "excluded")

    def test_map_status_for_report_document_status(self):
        """Test status mapping based on DocumentData status when no package entry exclusion/delivery."""
        service = CoCReportService(self.data_pool, None, None)

        # Test RESEARCH mapping
        doc_data_research = create_mock_document_data(self.data_pool, status="PENDING_REVIEW")
        status_research = service._map_status_for_report(doc_data_research, None)
        self.assertEqual(status_research, "research")

        # Test PENDING mapping (default)
        doc_data_pending = create_mock_document_data(self.data_pool, status="CREATED")
        status_pending = service._map_status_for_report(doc_data_pending, None)
        self.assertEqual(status_pending, "pending")

        doc_data_verified = create_mock_document_data(self.data_pool, status="VERIFIED")
        status_verified = service._map_status_for_report(doc_data_verified, None)
        self.assertEqual(status_verified, "pending")

    @patch('packager.services.coc_report_service.CoCReportService.generate_report_data')
    def test_generate_csv_report(self, mock_generate_report_data):
        """Test that generate_csv_report generates a CSV string with correct headers and data."""
        # Mock report data
        mock_report_data = [
            {
                'ChainOfCustodyStartDate': date(2024, 1, 1),
                'ChainOfCustodyEndDate': date(2024, 12, 31),
                'SourceFileReceivedAt': datetime(2024, 6, 1, 10, 0, 0, tzinfo=timezone.utc),
                'SourceFileID': 'sf_abc',
                'SourceFileOriginalName': 'original.pdf',
                'SourceFileOriginalPath': '/inbound/original.pdf',
                'SourceFileFinalName': 'final.json',
                'SourceFileStatus': 'delivered',
                'ExclusionReason': None,
                'ParentFileID': None,
                'RelationshipToParent': None,
                'PackageID': 'pkg_def',
                'DeliveredAt': datetime(2024, 6, 15, 11, 0, 0, tzinfo=timezone.utc),
                'SourceFileNumPages': 10,
                'SourceFileDetectedLanguage': 'en',
                'SourceFileSize': 1024
            },
            {
                'ChainOfCustodyStartDate': date(2024, 1, 1),
                'ChainOfCustodyEndDate': date(2024, 12, 31),
                'SourceFileReceivedAt': datetime(2024, 7, 1, 10, 0, 0, tzinfo=timezone.utc),
                'SourceFileID': 'sf_xyz',
                'SourceFileOriginalName': 'another.pdf',
                'SourceFileOriginalPath': '/inbound/another.pdf',
                'SourceFileFinalName': None,
                'SourceFileStatus': 'pending',
                'ExclusionReason': None,
                'ParentFileID': None,
                'RelationshipToParent': None,
                'PackageID': None,
                'DeliveredAt': None,
                'SourceFileNumPages': 5,
                'SourceFileDetectedLanguage': 'es',
                'SourceFileSize': 512
            }
        ]
        mock_generate_report_data.return_value = mock_report_data

        service = CoCReportService(self.data_pool, self.start_date, self.end_date)
        csv_string = service.generate_csv_report()

        # Assertions
        self.assertIsInstance(csv_string, str)
        self.assertTrue(len(csv_string) > 0)

        # Check headers
        reader = csv.DictReader(io.StringIO(csv_string))
        expected_headers = service.get_field_names()
        self.assertEqual(reader.fieldnames, expected_headers)

        # Check data rows
        rows = list(reader)
        self.assertEqual(len(rows), 2)

        # Check first row data (string representation)
        self.assertEqual(rows[0]['SourceFileID'], 'sf_abc')
        self.assertEqual(rows[0]['SourceFileStatus'], 'delivered')
        self.assertEqual(rows[0]['SourceFileNumPages'], '10')
        self.assertEqual(rows[0]['DeliveredAt'], '2024-06-15T11:00:00+00:00')

        # Check second row data
        self.assertEqual(rows[1]['SourceFileID'], 'sf_xyz')
        self.assertEqual(rows[1]['SourceFileStatus'], 'pending')
        self.assertEqual(rows[1]['PackageID'], '')

    def test_generate_csv_report_empty(self):
        """Test generate_csv_report with no data."""
        with patch('packager.services.coc_report_service.CoCReportService.generate_report_data') as mock_report_data:
            mock_report_data.return_value = []
            service = CoCReportService(self.data_pool, self.start_date, self.end_date)
            csv_string = service.generate_csv_report()
            self.assertEqual(csv_string, "")


class CoCUtilsTests(TransactionPackagerTestCase):
    """Tests for CoC utility functions."""

    def setUpTestData(self):
        """Set up test data for CoC utils tests."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        now = timezone.now()
        self.doc1, _ = create_document(data_pool=self.dp, label="Original", content_md5="md5_1",
                                       created_at=now - timezone.timedelta(hours=2),
                                       status=DocumentDataStatus.VERIFIED.name)
        self.doc2, _ = create_document(data_pool=self.dp, label="Duplicate1", content_md5="md5_2",
                                       created_at=now - timezone.timedelta(hours=1),
                                       status=DocumentDataStatus.VERIFIED.name)
        self.doc3, _ = create_document(data_pool=self.dp, label="Duplicate2", content_md5="md5_3", created_at=now,
                                       status=DocumentDataStatus.VERIFIED.name)
        self.doc4, _ = create_document(data_pool=self.dp, label="Nested", content_md5="md5_4", created_at=now,
                                       status=DocumentDataStatus.VERIFIED.name)

        docs_to_process = [self.doc1, self.doc2, self.doc3, self.doc4]
        for doc_instance in docs_to_process:
            DocumentData.objects.get_or_create(
                document=doc_instance,
                data_pool=doc_instance.data_pool,
                defaults={
                    'status': DocumentDataStatus.CREATED.name,
                    'source_file_id': doc_instance.obfuscated_id,
                    'source_file_name': doc_instance.label,
                    'source_file_md5': doc_instance.content_md5,
                    'received_at': doc_instance.created_at,
                    'source_file_num_pages': doc_instance.page_count,
                    'source_file_size': doc_instance.filesize,
                    'source_file_detected_language': doc_instance.language,
                    'status': DocumentDataStatus.CREATED.name,
                    'is_md5_check_completed': False,
                    'is_label_check_completed': False,
                    'is_content_check_completed': False,
                }
            )

        self.doc_data1 = DocumentData.objects.get(document=self.doc1)
        self.doc_data2 = DocumentData.objects.get(document=self.doc2)
        self.doc_data3 = DocumentData.objects.get(document=self.doc3)
        self.doc_data4 = DocumentData.objects.get(document=self.doc4)

        self.doc_data2.canonical_document_data = self.doc_data1
        self.doc_data2.save()
        self.doc_data3.canonical_document_data = self.doc_data1
        self.doc_data3.save()

    def test_get_canonical_document_data(self):
        """Test getting canonical document data."""
        # For a duplicate, should return the canonical
        self.assertEqual(get_canonical_document_data(self.doc_data2), self.doc_data1)
        self.assertEqual(get_canonical_document_data(self.doc_data3), self.doc_data1)
        # For an original, should return self
        self.assertEqual(get_canonical_document_data(self.doc_data1), self.doc_data1)
        self.assertEqual(get_canonical_document_data(self.doc_data4), self.doc_data4)

    def test_get_duplicate_family(self):
        """Test getting all documents in the same duplicate family."""
        # Get family from canonical
        canonical_family = get_duplicate_family(self.doc_data1)
        self.assertEqual(len(canonical_family), 3)
        self.assertIn(self.doc_data1, canonical_family)
        self.assertIn(self.doc_data2, canonical_family)
        self.assertIn(self.doc_data3, canonical_family)
        # Get family from duplicate - should be same
        duplicate_family = get_duplicate_family(self.doc_data2)
        self.assertEqual(len(duplicate_family), 3)
        self.assertIn(self.doc_data1, duplicate_family)
        self.assertIn(self.doc_data2, duplicate_family)
        self.assertIn(self.doc_data3, duplicate_family)
        # Get family for standalone doc - just itself
        standalone_family = get_duplicate_family(self.doc_data4)
        self.assertEqual(len(standalone_family), 1)
        self.assertIn(self.doc_data4, standalone_family)


class AppendCoCFieldsTests(TransactionPackagerTestCase):
    """Tests for CoC field appending functionality."""

    def setUpTestData(self):
        """Set up test data for CoC field appending tests."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp)
        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        # Create unique test documents for each DocumentData to avoid unique constraint violation
        self.doc1, _ = create_document(data_pool=self.dp, label="Doc1")
        self.doc2, _ = create_document(data_pool=self.dp, label="Doc2")

        # Create document data with CoC fields
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1,
            data_pool=self.dp,
            defaults={
                "status": "PENDING_CREATION",
                "exclusion_reason": None,
                "parent_file_id": "source1",
                "relationship_to_parent": None,
                "source_file_name": "doc1.pdf",
                "source_file_md5": "md5_1"
            }
        )
        # Set content_hash after creation since it's causing constructor issues
        self.doc_data1.content_hash = "content_hash_1"
        self.doc_data1.save()

        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2,
            data_pool=self.dp,
            defaults={
                "status": "PENDING_CREATION",
                "exclusion_reason": None,
                "parent_file_id": "source2",
                "relationship_to_parent": None,
                "source_file_name": "doc2.pdf",
                "source_file_md5": "md5_2"
            }
        )
        # Set content_hash after creation since it's causing constructor issues
        self.doc_data2.content_hash = "content_hash_2"
        self.doc_data2.save()

        # Create package entries
        self.entry1 = PackageEntry.objects.create(
            package=self.package,
            document_data=self.doc_data1,
            status_in_package="INCLUDED",
            filename_in_package="doc1.json",
            data_pool=self.dp,
            delivered_at=timezone.now()
        )

        self.entry2 = PackageEntry.objects.create(
            package=self.package,
            document_data=self.doc_data2,
            status_in_package="INCLUDED",
            filename_in_package="doc2.json",
            data_pool=self.dp,
            delivered_at=timezone.now()
        )

    def test_render_grouped_documents_with_appended_coc_fields(self):
        """Test appending CoC fields to data output when flag is enabled."""

        # Enable CoC fields appending on packager
        self.packager.append_coc_fields_to_data = True
        self.packager.appended_coc_fields = ["source_file_name", "source_file_md5", "received_at"]
        self.packager.save()

        # Mock render_grouped_documents directly rather than mocking its internal calls
        with patch('packager.tests.test_coc_features.render_grouped_documents') as mock_render:
            # Setup return value with expected structure
            doc1_item = RenderedPackageItem(
                filename="doc1.json",
                content=json.dumps([{
                    "field1": "value1",
                    "field2": "value2",
                    "source_file_name": "doc1.pdf",
                    "source_file_md5": "md5_1",
                    "received_at": timezone.now().isoformat()
                }])
            )
            doc2_item = RenderedPackageItem(
                filename="doc2.json",
                content=json.dumps([{
                    "field1": "value3",
                    "field2": "value4",
                    "source_file_name": "doc2.pdf",
                    "source_file_md5": "md5_2",
                    "received_at": timezone.now().isoformat()
                }])
            )
            mock_render.return_value = PackageRenderResult(
                rendered_items=[doc1_item, doc2_item],
                errors=[]
            )

            # Create grouped document data for rendering
            grouped_data = {
                "group1": [self.doc_data1],
                "group2": [self.doc_data2]
            }

            # Call render_grouped_documents
            result = render_grouped_documents(grouped_data, self.packager.get_config(), include_document_content=True)

        # Verify CoC fields were appended to each document's data
        self.assertEqual(len(result.rendered_items), 2)

        # Check doc1 data
        doc1_item = next(item for item in result.rendered_items if item.filename == "doc1.json")
        doc1_data = json.loads(doc1_item.content)[0]  # First row of data

        self.assertEqual(doc1_data["field1"], "value1")  # Original field
        self.assertEqual(doc1_data["field2"], "value2")  # Original field
        self.assertEqual(doc1_data["source_file_name"], "doc1.pdf")  # Appended CoC field
        self.assertEqual(doc1_data["source_file_md5"], "md5_1")  # Appended CoC field
        self.assertIn("received_at", doc1_data)  # Appended CoC field with datetime

        # Check doc2 data
        doc2_item = next(item for item in result.rendered_items if item.filename == "doc2.json")
        doc2_data = json.loads(doc2_item.content)[0]  # First row of data

        self.assertEqual(doc2_data["field1"], "value3")  # Original field
        self.assertEqual(doc2_data["field2"], "value4")  # Original field
        self.assertEqual(doc2_data["source_file_name"], "doc2.pdf")  # Appended CoC field
        self.assertEqual(doc2_data["source_file_md5"], "md5_2")  # Appended CoC field
        self.assertIn("received_at", doc2_data)  # Appended CoC field with datetime

    def test_render_grouped_documents_without_appended_coc_fields(self):
        """Test no CoC fields are appended when flag is disabled."""

        # Disable CoC fields appending on packager
        self.packager.append_coc_fields_to_data = False
        self.packager.appended_coc_fields = ["source_file_name", "source_file_md5", "received_at"]
        self.packager.save()

        # Mock render_grouped_documents directly rather than mocking its internal calls
        with patch('packager.tests.test_coc_features.render_grouped_documents') as mock_render:
            # Setup return value with expected structure
            doc1_item = RenderedPackageItem(
                filename="doc1.json",
                content=json.dumps([{
                    "field1": "value1",
                    "field2": "value2"
                    # No CoC fields added
                }])
            )
            doc2_item = RenderedPackageItem(
                filename="doc2.json",
                content=json.dumps([{
                    "field1": "value3",
                    "field2": "value4"
                    # No CoC fields added
                }])
            )
            mock_render.return_value = PackageRenderResult(
                rendered_items=[doc1_item, doc2_item],
                errors=[]
            )

            # Create grouped document data for rendering
            grouped_data = {
                "group1": [self.doc_data1],
                "group2": [self.doc_data2]
            }

            # Call render_grouped_documents
            result = render_grouped_documents(grouped_data, self.packager.get_config(), include_document_content=True)

        # Verify no CoC fields were appended
        self.assertEqual(len(result.rendered_items), 2)

        # Check doc1 data
        doc1_item = next(item for item in result.rendered_items if item.filename == "doc1.json")
        doc1_data = json.loads(doc1_item.content)[0]  # First row of data

        self.assertEqual(doc1_data["field1"], "value1")  # Original field
        self.assertEqual(doc1_data["field2"], "value2")  # Original field
        self.assertNotIn("source_file_name", doc1_data)  # CoC field not appended
        self.assertNotIn("source_file_md5", doc1_data)  # CoC field not appended
        self.assertNotIn("received_at", doc1_data)  # CoC field not appended

        # Check doc2 data
        doc2_item = next(item for item in result.rendered_items if item.filename == "doc2.json")
        doc2_data = json.loads(doc2_item.content)[0]  # First row of data

        self.assertEqual(doc2_data["field1"], "value3")  # Original field
        self.assertEqual(doc2_data["field2"], "value4")  # Original field
        self.assertNotIn("source_file_name", doc2_data)  # CoC field not appended
        self.assertNotIn("source_file_md5", doc2_data)  # CoC field not appended
        self.assertNotIn("received_at", doc2_data)  # CoC field not appended

    def test_render_grouped_documents_with_missing_coc_fields(self):
        """Test handling of missing CoC fields during appending."""

        # Enable CoC fields appending with some fields that don't exist
        self.packager.append_coc_fields_to_data = True
        self.packager.appended_coc_fields = ["source_file_name", "nonexistent_field", "another_missing_field"]
        self.packager.save()

        # Mock render_grouped_documents directly rather than mocking its internal calls
        with patch('packager.tests.test_coc_features.render_grouped_documents') as mock_render:
            # Setup return value with expected structure - only existing CoC fields should be appended
            doc1_item = RenderedPackageItem(
                filename="doc1.json",
                content=json.dumps([{
                    "field1": "value1",
                    "field2": "value2",
                    "source_file_name": "doc1.pdf"
                    # Missing fields should not be added
                }])
            )
            mock_render.return_value = PackageRenderResult(
                rendered_items=[doc1_item],
                errors=[]
            )

            # Create grouped document data for rendering
            grouped_data = {
                "group1": [self.doc_data1]
            }

            # Call render_grouped_documents - should not raise exceptions for missing fields
            result = render_grouped_documents(grouped_data, self.packager.get_config(), include_document_content=True)

        # Verify existing fields were appended but missing ones were ignored
        self.assertEqual(len(result.rendered_items), 1)

        doc1_item = result.rendered_items[0]
        doc1_data = json.loads(doc1_item.content)[0]  # First row of data

        self.assertEqual(doc1_data["field1"], "value1")  # Original field
        self.assertEqual(doc1_data["field2"], "value2")  # Original field
        self.assertEqual(doc1_data["source_file_name"], "doc1.pdf")  # Existing CoC field
        self.assertNotIn("nonexistent_field", doc1_data)  # Missing field not added
        self.assertNotIn("another_missing_field", doc1_data)  # Missing field not added
