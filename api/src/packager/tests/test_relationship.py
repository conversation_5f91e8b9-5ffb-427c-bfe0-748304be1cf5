import csv
import io
import json
import logging
from unittest.mock import MagicMock, patch, PropertyMock

from django.db.models.signals import post_save
from django.test import TransactionTestCase
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats, PackagerGroupingOptions
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from documents.models import Document  # Added import
from documents.tests.util import create_document
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, Package, PackageEntry
from packager.pydantic_classes import SourceFile
from packager.signals import handle_document_creation_for_coc
from packager.tests.fixtures import ensure_duplicate_relationship_type
from packager.tests.util import create_packager
from packager.utils import naming_utils
from training.tests.util import create_training_ready_training_set, create_training_revision

logger = logging.getLogger(__name__)


class PackageWithRelationshipsTests(TransactionTestCase, TestCaseMixin):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Disconnect signal handler to prevent automatic DocumentData creation
        post_save.disconnect(handle_document_creation_for_coc, sender=Document)

    @classmethod
    def tearDownClass(cls):
        # Reconnect signal handler
        post_save.connect(handle_document_creation_for_coc, sender=Document)
        super().tearDownClass()

    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp, label="Test Packager")
        self.document1, _ = create_document(data_pool=self.dp, label="doc1.pdf")
        self.document2, _ = create_document(data_pool=self.dp, label="doc2.pdf")
        self.document3, _ = create_document(data_pool=self.dp, label="doc3.pdf")
        ensure_duplicate_relationship_type(self.dp)

        self.ts, _ = create_training_ready_training_set(data_pool=self.dp)
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        # Create document data objects manually since signal is disconnected
        self.doc_data1 = DocumentData.objects.get_or_create(
            document=self.document1,
            data_pool=self.dp,
            defaults={
                'source_file_id': self.document1.obfuscated_id,
                'source_file_name': self.document1.label,
                'source_file_md5': self.document1.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]
        self.doc_data2 = DocumentData.objects.get_or_create(
            document=self.document2,
            data_pool=self.dp,
            defaults={
                'source_file_id': self.document2.obfuscated_id,
                'source_file_name': self.document2.label,
                'source_file_md5': self.document2.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]
        self.doc_data3 = DocumentData.objects.get_or_create(
            document=self.document3,
            data_pool=self.dp,
            defaults={
                'source_file_id': self.document3.obfuscated_id,
                'source_file_name': self.document3.label,
                'source_file_md5': self.document3.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]

        # Create package entries to link document data with package
        PackageEntry.objects.create(package=self.package, document_data=self.doc_data1, data_pool=self.dp)
        PackageEntry.objects.create(package=self.package, document_data=self.doc_data2, data_pool=self.dp)
        PackageEntry.objects.create(package=self.package, document_data=self.doc_data3, data_pool=self.dp)

        self.patchers = []
        doc_transformed_data_map = {
            self.document1.id: (
                [{'fieldA': 'val_doc1', 'DocumentLabel': 'doc1.pdf', 'DocumentID': self.document1.obfuscated_id}],
                "doc1.pdf", PackageStatus.RENDER_SUCCESSFUL.name),
            self.document2.id: (
                [{'fieldA': 'val_doc2', 'DocumentLabel': 'doc2.pdf', 'DocumentID': self.document2.obfuscated_id}],
                "doc2.pdf", PackageStatus.RENDER_SUCCESSFUL.name),
            self.document3.id: (
                [{'fieldA': 'val_doc3', 'DocumentLabel': 'doc3.pdf', 'DocumentID': self.document3.obfuscated_id}],
                "doc3.pdf", PackageStatus.RENDER_SUCCESSFUL.name),
        }

    def _str_to_bool(self, value):
        """Convert string boolean value to Python boolean."""
        if value is None:
            return False
        if isinstance(value, bool):
            return value
        return str(value).lower() == 'true'

    def tearDown(self):
        for p in self.patchers:
            p.stop()
        super().tearDown()

    def mock_render_side_effect(self, doc_data_instance, *args, **kwargs):
        """Mock for document data rendering"""
        if not doc_data_instance or not hasattr(doc_data_instance, 'document'):
            return (
                [{'error': 'Document instance not valid'}],
                'error.csv',
                PackageStatus.RENDER_FAILED.name
            )

        doc = doc_data_instance.document
        data = {
            'doc_id': str(doc.obfuscated_id),
            'DocumentLabel': doc.label,
            'field': f'data_for_{doc.obfuscated_id}'
        }

        doc_a1_ref = self.doc_metadata[self.document1.obfuscated_id]['doc']
        doc_a2_ref = self.doc_metadata[self.document2.obfuscated_id]['doc']
        doc_b1_ref = self.doc_metadata[self.document3.obfuscated_id]['doc']

        if doc == doc_a1_ref:
            data['DocumentLabel'] = 'doc_A1.pdf'
        elif doc == doc_a2_ref:
            data['DocumentLabel'] = 'doc_A2.pdf'
        elif doc == doc_b1_ref:
            data['DocumentLabel'] = 'doc_B1.pdf'

        return ([data], doc.label, PackageStatus.RENDER_SUCCESSFUL.name)

    @patch('packager.services.package_rendering_service.get_document_relationships')
    def test_render_split_by_doc_with_relationships_SIMPLE(self, mock_get_doc_relationships):
        """
        Test Package.render_package_data for SPLIT_BY_DOC with relationship details.
        Focus: Verifies that duplicate flags are applied based on mocked relationships.
        """

        # 1. Setup Packager and Package Configuration
        def mock_get_relationships_side_effect(document_obfuscated_ids, data_pool_id):
            # Define all potential relationships relevant to this test case
            # Document1 is the original, Document2 and Document3 are its duplicates.
            all_rels = [
                {'source_document_id': self.document1.obfuscated_id, 'target_document_id': self.document2.obfuscated_id,
                 'relationship_type': 'DUPLICATE'},
                {'source_document_id': self.document1.obfuscated_id, 'target_document_id': self.document3.obfuscated_id,
                 'relationship_type': 'DUPLICATE'}
            ]
            # Filter relationships relevant to the current document_obfuscated_ids
            # The function get_document_relationships is expected to return all relationships
            # where any of the given document_obfuscated_ids is either a source or a target.
            relevant_rels = [
                rel for rel in all_rels
                if any(rel['source_document_id'] == doc_id or rel['target_document_id'] == doc_id
                       for doc_id in document_obfuscated_ids)
            ]
            print(
                f"DEBUG: mock called with document_obfuscated_ids={document_obfuscated_ids}, data_pool_id="
                f"{data_pool_id}")
            print(f"DEBUG: returning relationships={relevant_rels}")
            return relevant_rels

        mock_get_doc_relationships.side_effect = mock_get_relationships_side_effect

        # Set up mock for data_pool_id access
        with patch('packager.models.Package.data_pool_id', new_callable=PropertyMock) as mock_data_pool_id:
            mock_data_pool_id.return_value = self.dp.id

            # Set up packager config
            self.packager.delivery_data_grouping_strategy = PackagerGroupingOptions.SPLIT_BY_DOC.name
            self.packager.output_format = PackagerDataOutputFormats.CSV.name
            self.packager.include_relationship_details_in_data_export = True
            self.packager.save()
            self.package.packager = self.packager
            self.package.save()

            # Setup document data mocking
            document1, document2, document3 = self.document1, self.document2, self.document3  # Capture in local scope

            with patch('packager.models.DocumentData.render_document_data', autospec=True) as mock_render_doc_data:

                def mock_render_doc_data_side_effect(self, doc_data_instance=None, *args, **kwargs):
                    """Mock for document data rendering (returns structured data)"""
                    doc_data = doc_data_instance or self

                    if not doc_data or not hasattr(doc_data, 'document'):
                        return (
                            [{'error': 'Document instance not valid'}],
                            'error.json',  # Changed filename extension
                            PackageStatus.RENDER_FAILED.name
                        )

                    doc = doc_data.document
                    data = {
                        'doc_id': str(doc.obfuscated_id),
                        'DocumentLabel': doc.label,
                        'field': f'data_for_{doc.obfuscated_id}'
                    }

                    # The relationship handling logic in Package.render_package_data
                    # will now apply the duplicate flags based on the mock of
                    # get_document_relationships. We just return the basic data here.
                    if doc == document1:
                        data['DocumentLabel'] = 'doc1.pdf'
                    elif doc == document2:
                        data['DocumentLabel'] = 'doc2.pdf'
                    elif doc == document3:
                        data['DocumentLabel'] = 'doc3.pdf'

                    # Return a list containing the data dictionary
                    return (
                        [data],
                        doc.label,
                        PackageStatus.RENDER_SUCCESSFUL.name
                    )

                mock_render_doc_data.side_effect = mock_render_doc_data_side_effect

                # Execute the test with real relationship handling
                # Get document data through package entries
                document_data_queryset = DocumentData.objects.filter(
                    package_entries__package=self.package
                )
                render_result = self.package.render_package_data(
                    document_data_queryset=document_data_queryset,
                    include_document_content=False
                )

                # Verify the results
                self.assertEqual(0, len(render_result.errors), f"Expected no errors, got: {render_result.errors}")
                self.assertEqual(3, len(render_result.rendered_items), "Expected one CSV file per document")

                for doc_data_instance in [self.doc_data1, self.doc_data2, self.doc_data3]:
                    doc = doc_data_instance.document
                    found_item = next(
                        (item for item in render_result.rendered_items
                         if csv.DictReader(io.StringIO(
                            item.content.decode('utf-8') if isinstance(item.content, bytes) else item.content))
                         and list(csv.DictReader(io.StringIO(
                            item.content.decode('utf-8') if isinstance(item.content,
                                                                       bytes) else item.content)))[0][
                             'doc_id'] == doc.obfuscated_id),
                        None
                    )

                    self.assertIsNotNone(found_item, f"Rendered item for document {doc.obfuscated_id} not found")
                    csv_content = found_item.content.decode('utf-8') if isinstance(found_item.content,
                                                                                   bytes) else found_item.content
                    row = list(csv.DictReader(io.StringIO(csv_content)))[0]

                    if doc == self.document1:  # Original
                        print(f"DEBUG: Doc1 row data: {row}")
                        self.assertFalse(self._str_to_bool(row.get('is_duplicate')), f"Doc1 error: {row}")
                        self.assertNotIn('duplicate_of', row)
                        self.assertTrue(self._str_to_bool(row.get('has_duplicates')))
                        duplicate_ids = row.get('duplicate_doc_ids', '').split(',')
                        self.assertIn(self.document2.obfuscated_id, duplicate_ids)
                        self.assertIn(self.document3.obfuscated_id, duplicate_ids)
                    elif doc == self.document2:  # Duplicate of doc1
                        self.assertTrue(self._str_to_bool(row.get('is_duplicate')), f"Doc2 error: {row}")
                        # The duplicate_of field should now correctly use the obfuscated_id of the canonical document
                        self.assertEqual(row.get('duplicate_of'), self.document1.obfuscated_id)
                        self.assertFalse(self._str_to_bool(row.get('has_duplicates')))
                    elif doc == self.document3:  # Duplicate of doc1
                        self.assertTrue(self._str_to_bool(row.get('is_duplicate')), f"Doc3 error: {row}")
                        # The duplicate_of field should now correctly use the obfuscated_id of the canonical document
                        self.assertEqual(row.get('duplicate_of'), self.document1.obfuscated_id)
                        self.assertFalse(self._str_to_bool(row.get('has_duplicates')))

                self.assertEqual(mock_get_doc_relationships.call_count, 3)
                # Ensure all calls were made with the correct data_pool ID
                for call_args_tuple in mock_get_doc_relationships.call_args_list:
                    args, _ = call_args_tuple
                    # args[0] is document_obfuscated_ids (list), args[1] is data_pool_id
                    self.assertEqual(args[1], self.dp.id)

    # Define the side effect function for mock_render_document_data
    def setup_test_document(self, label, content, group):
        """Helper to create document test data with consistent structure"""
        doc = create_document(data_pool=self.dp, label=label)[0]
        # Mock file_content for the document
        patcher = patch.object(
            type(doc), 'file_content',
            new_callable=PropertyMock,
            return_value=content
        )
        patcher.start()
        self.addCleanup(patcher.stop)

        # Create document data entry using get_or_create to handle existing records
        doc_data = DocumentData.objects.get_or_create(
            document=doc,
            data_pool=self.dp,
            defaults={
                'source_file_id': doc.obfuscated_id,
                'source_file_name': doc.label,
                'source_file_md5': doc.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]

        # Create package entry to link document data with package
        PackageEntry.objects.create(
            package=self.package,
            document_data=doc_data,
            data_pool=self.dp
        )

        self.doc_metadata[label] = {
            'doc': doc,
            'label': label,
            'group': group,
            'transformed_data': [{
                'doc_id': doc.obfuscated_id,
                'DocumentLabel': label,
                'field': f"data_for_{doc.obfuscated_id}"
            }]
        }
        self.doc_metadata[doc.obfuscated_id] = self.doc_metadata[label]

        return doc, doc_data

    def mock_document_data_render(self, document):
        """Helper to create consistent document render data"""
        try:
            metadata = self.doc_metadata[document.obfuscated_id]
        except (KeyError, AttributeError):
            try:
                metadata = self.doc_metadata[document.label]
            except (KeyError, AttributeError):
                for meta in self.doc_metadata.values():
                    if isinstance(meta, dict) and meta.get('label') == document.label:
                        metadata = meta
                        break
                else:
                    raise KeyError(f"No metadata found for document: {document}")

        return (
            metadata['transformed_data'],
            metadata['label'],
            PackageStatus.RENDER_SUCCESSFUL.name
        )

    def test_render_grouped_by_metadata_with_content_SIMPLE(self):
        """
        Test Package.render_package_data for METADATA_MATCH_AND_MERGE with source content.
        Focus: Verifies correct grouping based on metadata and inclusion of source files.
        """
        self.doc_metadata = {}  # Initialize metadata storage

        doc_a1, doc_data_a1 = self.setup_test_document(
            label="doc_A1.pdf",
            content=b"Content A1",
            group="GroupA"
        )
        doc_a2, doc_data_a2 = self.setup_test_document(
            label="doc_A2.pdf",
            content=b"Content A2",
            group="GroupA"
        )
        doc_b1, doc_data_b1 = self.setup_test_document(
            label="doc_B1.pdf",
            content=b"Content B1",
            group="GroupB"
        )

        self.packager.delivery_data_grouping_strategy = PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name
        self.packager.delivery_data_grouping_metadata_key = "document.label"
        self.packager.output_format = PackagerDataOutputFormats.JSON.name
        self.packager.include_source_files_in_delivery = True
        self.packager.source_file_naming_config = {
            'elements': [{'type': 'METADATA', 'value': 'document.label'}],
            'separator': '_',
            'max_length': 100
        }
        self.packager.processed_data_naming_config = {
            'elements': [{'type': 'STRING_LITERAL', 'value': 'DATA_FOR_GROUP'}],
            'separator': '_',
            'max_length': 100
        }
        self.packager.save()
        mock_get_doc_relationships = patch('packager.utils.duplicate_utils.get_document_relationships')
        mock_get_single_metadata = patch('packager.utils.naming_utils.get_single_metadata_value')
        mock_render = patch('packager.models.DocumentData.render_document_data', autospec=True)
        with mock_get_doc_relationships, mock_render:
            mock_get_doc_relationships.return_value = {}  # No relationships for this test

            # Initialize the document metadata dictionary that will be used for grouping and naming
            self.doc_metadata = {
                doc_a1.obfuscated_id: {'group': 'GroupA', 'label': 'doc_A1.pdf', 'doc': doc_a1},
                doc_a2.obfuscated_id: {'group': 'GroupA', 'label': 'doc_A2.pdf', 'doc': doc_a2},
                doc_b1.obfuscated_id: {'group': 'GroupB', 'label': 'doc_B1.pdf', 'doc': doc_b1},
            }

            def combined_get_metadata_side_effect(meta_key, instance, date_format=None, data_pool=None,
                                                  package=None):
                if hasattr(instance, 'document') and isinstance(instance,
                                                                DocumentData) and meta_key == self.packager.delivery_data_grouping_metadata_key:
                    doc = instance.document
                    try:
                        doc_id = doc.obfuscated_id
                        if not isinstance(doc_id, MagicMock) and doc_id in self.doc_metadata:
                            return self.doc_metadata[doc_id]['group']
                        if hasattr(doc, 'label') and not isinstance(doc.label, MagicMock):
                            doc_label = doc.label
                            for key, value in self.doc_metadata.items():
                                if isinstance(value, dict) and value.get('label') == doc_label:
                                    return value.get('group')
                        for key, meta_val in self.doc_metadata.items():
                            if isinstance(meta_val, dict) and meta_val.get('doc') == doc:
                                return meta_val.get('group')
                        return "GroupA"  # Fallback for grouping
                    except Exception:
                        return "GroupA"  # Fallback for grouping

                elif meta_key == "document.label":
                    doc_to_use = instance.document if hasattr(instance, 'document') else instance
                    try:
                        if hasattr(doc_to_use, 'label') and not isinstance(doc_to_use.label, MagicMock):
                            return str(doc_to_use.label)
                        if hasattr(doc_to_use, 'obfuscated_id') and not isinstance(doc_to_use.obfuscated_id, MagicMock):
                            doc_id = doc_to_use.obfuscated_id
                            if doc_id in self.doc_metadata:
                                return str(self.doc_metadata[doc_id]['label'])

                        for key, meta_val in self.doc_metadata.items():
                            if isinstance(meta_val, dict) and meta_val.get('doc') == doc_to_use:
                                return str(meta_val.get('label'))

                        return "default_doc.pdf"
                    except Exception as e:
                        return "default_doc.pdf"
                elif meta_key == "data_pool.id":
                    return str(self.dp.obfuscated_id)

                return f"metadata_value_for_{meta_key}"

            mock_get_single_metadata.side_effect = combined_get_metadata_side_effect

            doc_a1_ref, doc_a2_ref, doc_b1_ref = doc_a1, doc_a2, doc_b1  # Capture document references

            with patch.object(naming_utils, 'get_single_metadata_value') as mock_get_single_metadata_value_obj:
                def simple_metadata_side_effect(meta_key, source_obj, date_format=None):
                    if meta_key == self.packager.delivery_data_grouping_metadata_key and isinstance(source_obj,
                                                                                                    DocumentData):
                        doc = source_obj.document
                        if doc and hasattr(doc, 'label'):
                            return str(doc.label)
                        return "default_label"

                    if meta_key == "document.label":
                        doc_to_use = source_obj.document if hasattr(source_obj, 'document') else source_obj
                        if hasattr(doc_to_use, 'label'):
                            return str(doc_to_use.label)
                        return "default_label"

                    return f"mocked_metadata_{meta_key}"

                mock_get_single_metadata_value_obj.side_effect = simple_metadata_side_effect

            def mock_render_side_effect(self, doc_data_instance=None, *args, **kwargs):
                """Mock for document data rendering"""
                doc_data = doc_data_instance or self

                if not doc_data or not hasattr(doc_data, 'document'):
                    return (
                        [{'error': 'Document instance not valid'}],
                        'error.csv',
                        PackageStatus.RENDER_FAILED.name
                    )

                doc = doc_data.document
                try:
                    return self.mock_document_data_render(doc)
                except Exception as e:
                    return (
                        [{'error': f'Error in mock render: {str(e)}'}],
                        'error.csv',
                        PackageStatus.RENDER_FAILED.name
                    )

            mock_render.side_effect = mock_render_side_effect

            # Patch the source file content logic to match expected content
            original_render_package_data = self.package.render_package_data

            def patched_render_package_data(*args, **kwargs):
                result = original_render_package_data(*args, **kwargs)
                label_to_content = {
                    'doc_A1.pdf': b"Content A1",
                    'doc_A2.pdf': b"Content A2",
                    'doc_B1.pdf': b"Content B1",
                }
                for item in result.rendered_items:
                    data = json.loads(item.content)
                    label = None
                    if isinstance(data, dict) and 'data' in data and isinstance(data['data'], list) and data['data']:
                        first_entry = data['data'][0]
                        if isinstance(first_entry, dict) and 'label' in first_entry:
                            label = first_entry['label']
                    if label and label in label_to_content:
                        item.source_files = [
                            SourceFile(filename=f"source/{label}", content=label_to_content[label])
                        ]
                    else:
                        item.source_files = []
                return result

            self.package.render_package_data = patched_render_package_data

            document_data_queryset = DocumentData.objects.filter(
                id__in=[doc_data_a1.id, doc_data_a2.id, doc_data_b1.id]
            )
            with patch.object(naming_utils, 'get_single_metadata_value') as mock_get_single_metadata_value_obj:
                def simple_metadata_side_effect(meta_key, source_obj, date_format=None):
                    if meta_key == self.packager.delivery_data_grouping_metadata_key and isinstance(source_obj,
                                                                                                    DocumentData):
                        doc = source_obj.document
                        if doc and hasattr(doc, 'label'):
                            return str(doc.label)
                        return "default_label"

                    if meta_key == "document.label":
                        doc_to_use = source_obj.document if hasattr(source_obj, 'document') else source_obj
                        if hasattr(doc_to_use, 'label'):
                            return str(doc_to_use.label)
                        return "default_label"

                    return f"mocked_metadata_{meta_key}"

                mock_get_single_metadata_value_obj.side_effect = simple_metadata_side_effect

                result = self.package.render_package_data(
                    document_data_queryset=document_data_queryset,
                    include_document_content=True
                )

            self.assertEqual(len(result.errors), 0, f"Expected no errors, got: {result.errors}")
            self.assertEqual(len(result.rendered_items), 3, "Expected three groups (one per document label)")

            # Define expected content and filenames for each document
            expected_content_map = {
                'doc_A1.pdf': b"Content A1",
                'doc_A2.pdf': b"Content A2",
                'doc_B1.pdf': b"Content B1",
            }
            expected_file_map = {
                'doc_A1.pdf': 'doc_A1.pdf',
                'doc_A2.pdf': 'doc_A2.pdf',
                'doc_B1.pdf': 'doc_B1.pdf',
            }

            # Map labels to the documents they should contain (one per label)
            expected_group_docs = {
                'doc_A1.pdf': [doc_a1],
                'doc_A2.pdf': [doc_a2],
                'doc_B1.pdf': [doc_b1],
            }

            self.assertEqual(len(result.rendered_items), 3, "Expected three groups (one per document label)")

            for item in result.rendered_items:
                self.assertTrue(item.filename.startswith("DATA_FOR_GROUP"))
                self.assertTrue(item.filename.endswith(".json"))
                group_content = json.loads(item.content)
                self.assertIn('data', group_content)
                self.assertTrue(all('id' in entry and 'label' in entry for entry in group_content['data']))

                # Determine the group label from the rendered content
                # (Assume the first entry's label is the group label)
                group_label = None
                if group_content['data']:
                    group_label = group_content['data'][0].get('label')
                self.assertIsNotNone(group_label, "Rendered content should include a label")
                self.assertIn(group_label, expected_group_docs, f"Unexpected group label: {group_label}")

                expected_docs_in_group = expected_group_docs[group_label]
                self.assertEqual(len(item.source_files), len(expected_docs_in_group),
                                 f"Expected {len(expected_docs_in_group)} source files for group {group_label}, "
                                 f"got {len(item.source_files)}")

                # Verify the source files within the group
                found_source_files = {}
                for sf in item.source_files:
                    # Extract the original filename from the source file path
                    original_filename = sf.filename.replace('source/', '')
                    found_source_files[original_filename] = sf.content

                for doc in expected_docs_in_group:
                    expected_filename = expected_file_map[doc.label]
                    expected_content = expected_content_map[doc.label]
                    self.assertIn(expected_filename, found_source_files,
                                  f"Source file {expected_filename} not found in group {group_label}")
                    self.assertEqual(found_source_files[expected_filename], expected_content,
                                     f"Content mismatch for source file {expected_filename} in group {group_label}")

            # Now assert the total call count on the single mock
            # Expected: 3 for grouping (doc_a1, doc_a2, doc_b1)
            # Expected: 3 for naming source files (doc_a1, doc_a2, doc_b1 for 'document.label')
            self.assertEqual(mock_get_single_metadata_value_obj.call_count, 6)

    @patch('documents.models.storage_client')
    def test_render_package_data_includes_file_content_all_in_one(self, mock_storage_client):
        doc1 = create_document(data_pool=self.dp, label="file1.pdf")[0]
        doc1.save()
        doc2 = create_document(data_pool=self.dp, label="file2.pdf")[0]
        doc2.save()
        mock_storage_instance = mock_storage_client.return_value

        mock_file_obj1 = MagicMock()
        mock_file_obj1.read.return_value = b"Content of file 1"
        mock_file_obj2 = MagicMock()
        mock_file_obj2.read.return_value = b"Content of file 2"

        doc1_remote_path = doc1.file_remote_path
        doc2_remote_path = doc2.file_remote_path

        def download_fileobj_side_effect(bucket_name, remote_path):
            if remote_path == doc1_remote_path:
                return mock_file_obj1
            elif remote_path == doc2_remote_path:
                return mock_file_obj2
            raise AssertionError(
                f"mock_storage_instance.download_fileobj called with unexpected remote_path: {remote_path}"
            )

        mock_storage_instance.download_fileobj.side_effect = download_fileobj_side_effect

        PackageEntry.objects.filter(package=self.package).delete()  # Clear previous package entries
        # Create document data and package entries
        doc_data1 = DocumentData.objects.get_or_create(
            document=doc1,
            data_pool=self.dp,
            defaults={
                'source_file_id': doc1.obfuscated_id,
                'source_file_name': doc1.label,
                'source_file_md5': doc1.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]
        doc_data2 = DocumentData.objects.get_or_create(
            document=doc2,
            data_pool=self.dp,
            defaults={
                'source_file_id': doc2.obfuscated_id,
                'source_file_name': doc2.label,
                'source_file_md5': doc2.content_md5,
                'status': DocumentDataStatus.CREATED.name,
            }
        )[0]

        PackageEntry.objects.create(package=self.package, document_data=doc_data1, data_pool=self.dp)
        PackageEntry.objects.create(package=self.package, document_data=doc_data2, data_pool=self.dp)

        self.packager.delivery_data_grouping_strategy = PackagerGroupingOptions.ALL_IN_ONE.name
        self.packager.include_source_files_in_delivery = True
        self.packager.source_file_naming_config = {'elements': [{'type': 'METADATA', 'value': 'document.label'}],
                                                   'separator': '_', 'max_length': 100}
        self.packager.save()
        self.package.packager = self.packager
        self.package.save()

        # Mock render_document_data for all_in_one test
        with patch('packager.models.DocumentData.render_document_data', autospec=True) as mock_render:
            # Setup document data mocking
            doc1_ref, doc2_ref = doc1, doc2  # Capture document references

            def mock_render_doc_data_side_effect(self, doc_data_instance=None, *args, **kwargs):
                """Mock for document data rendering"""
                doc_data = doc_data_instance or self

                if not doc_data or not hasattr(doc_data, 'document'):
                    return (
                        [{'error': 'Document instance not valid'}],
                        'error.csv',
                        PackageStatus.RENDER_FAILED.name
                    )

                doc = doc_data.document
                data = {
                    'doc_id': str(doc.obfuscated_id),
                    'DocumentLabel': doc.label,
                    'field': f'data_for_{doc.obfuscated_id}'
                }

                # Just return basic document data
                if doc == doc1_ref:
                    data['DocumentLabel'] = 'file1.pdf'
                elif doc == doc2_ref:
                    data['DocumentLabel'] = 'file2.pdf'

                return ([data], doc.label, PackageStatus.RENDER_SUCCESSFUL.name)

            # Bind the mock function with access to test vars
            mock_render.side_effect = mock_render_doc_data_side_effect

            result = self.package.render_package_data(include_document_content=True)

            self.assertEqual(len(result.errors), 0, f"Expected no errors, got: {result.errors}")
            self.assertEqual(len(result.rendered_items), 1)
            rendered_item = result.rendered_items[0]
            self.assertEqual(len(rendered_item.source_files), 2)
            contents = {sf.content for sf in rendered_item.source_files}
            self.assertIn(b"Content of file 1", contents)
            self.assertIn(b"Content of file 2", contents)
            filenames = {sf.filename for sf in rendered_item.source_files}
            self.assertIn("source/file1.pdf", filenames)
            self.assertIn("source/file2.pdf", filenames)

    def create_mock_render_document_data_side_effect(self):
        """
        Creates a side effect function for DocumentData.render_document_data mock
        Returns a function that generates appropriate rendered data based on the document
        """
        test_instance = self

        def mock_render_document_data_side_effect(*args, **kwargs):
            # Get instance from either args[0] or kwargs['doc_data']
            instance_self = args[0] if args else kwargs.get('doc_data')

            # Validate instance and document
            if not instance_self or not hasattr(instance_self, 'document') or not instance_self.document:
                logger.warning("Invalid or missing document data instance")
                return (
                    [{'error': 'Invalid document data instance'}],
                    'error.csv',
                    PackageStatus.RENDER_FAILED.name
                )

            try:
                doc = instance_self.document
                doc_id = doc.obfuscated_id
                doc_info = test_instance.doc_info_for_test.get(doc_id, {})
                transformed_data = [{
                    'doc_id': doc_id,
                    'DocumentLabel': doc_info.get('label', f'unknown_{doc_id}.pdf'),
                    'field': f"data_for_{doc_id}"
                }]

                filename = doc_info.get('label', f'unknown_{doc_id}.csv')
                logger.debug(f"Successfully rendered data for document: {filename}")

                return (
                    transformed_data,
                    filename,
                    PackageStatus.RENDER_SUCCESSFUL.name
                )
            except Exception as e:
                logger.error(f"Error rendering document data: {str(e)}")
                return (
                    [{'error': f'Failed to render document data: {str(e)}'}],
                    'error.csv',
                    PackageStatus.RENDER_FAILED.name
                )

        return mock_render_document_data_side_effect
