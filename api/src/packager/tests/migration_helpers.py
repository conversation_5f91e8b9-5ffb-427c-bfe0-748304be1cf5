"""
Migration utilities for safely migrating packager tests to optimized patterns.

This module provides utilities to:
- Extract test methods from files
- Validate test migrations
- Compare test counts
- Ensure no tests are lost during consolidation
"""
import os
import re
import ast
from typing import List, Dict, Set, Tuple
from pathlib import Path


def extract_test_methods(file_path: str) -> List[Dict[str, str]]:
    """
    Parse a test file and extract all test methods.
    
    Returns:
        List of dicts with 'name', 'line_start', 'line_end', 'source'
    """
    if not os.path.exists(file_path):
        return []
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Parse the AST to get accurate method information
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return []
    
    test_methods = []
    lines = content.split('\n')
    
    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
            # Get the source code for this method
            start_line = node.lineno - 1  # AST is 1-indexed, we want 0-indexed
            
            # Find the end of the method by looking for the next method or class
            end_line = len(lines)
            for next_node in ast.walk(tree):
                if (isinstance(next_node, (ast.FunctionDef, ast.ClassDef)) and 
                    next_node.lineno > node.lineno):
                    end_line = min(end_line, next_node.lineno - 1)
            
            method_source = '\n'.join(lines[start_line:end_line])
            
            test_methods.append({
                'name': node.name,
                'line_start': start_line + 1,  # Convert back to 1-indexed for display
                'line_end': end_line,
                'source': method_source.strip()
            })
    
    return test_methods


def count_test_methods_in_file(file_path: str) -> int:
    """Count the number of test methods in a file."""
    test_methods = extract_test_methods(file_path)
    return len(test_methods)


def validate_test_migration(original_files: List[str], new_file: str) -> Dict[str, any]:
    """
    Validate that all tests from original files are present in the new file.
    
    Returns:
        Dict with validation results including missing tests, extra tests, etc.
    """
    original_tests = set()
    original_count = 0
    
    # Collect all test methods from original files
    for file_path in original_files:
        if os.path.exists(file_path):
            methods = extract_test_methods(file_path)
            for method in methods:
                original_tests.add(method['name'])
            original_count += len(methods)
    
    # Get test methods from new file
    new_tests = set()
    if os.path.exists(new_file):
        new_methods = extract_test_methods(new_file)
        for method in new_methods:
            new_tests.add(method['name'])
    
    # Compare
    missing_tests = original_tests - new_tests
    extra_tests = new_tests - original_tests
    
    return {
        'original_count': original_count,
        'new_count': len(new_tests),
        'missing_tests': list(missing_tests),
        'extra_tests': list(extra_tests),
        'migration_successful': len(missing_tests) == 0,
        'test_count_matches': original_count == len(new_tests)
    }


def compare_test_counts(before_files: List[str], after_files: List[str]) -> Dict[str, int]:
    """
    Compare test counts before and after consolidation.
    
    Returns:
        Dict with before/after counts and difference
    """
    before_count = 0
    after_count = 0
    
    for file_path in before_files:
        if os.path.exists(file_path):
            before_count += count_test_methods_in_file(file_path)
    
    for file_path in after_files:
        if os.path.exists(file_path):
            after_count += count_test_methods_in_file(file_path)
    
    return {
        'before_count': before_count,
        'after_count': after_count,
        'difference': after_count - before_count,
        'counts_match': before_count == after_count
    }


def extract_class_names(file_path: str) -> List[str]:
    """Extract all class names from a test file."""
    if not os.path.exists(file_path):
        return []
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError:
        return []
    
    class_names = []
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            class_names.append(node.name)
    
    return class_names


def extract_imports(file_path: str) -> List[str]:
    """Extract all import statements from a file."""
    if not os.path.exists(file_path):
        return []
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError:
        return []
    
    imports = []
    for node in ast.walk(tree):
        if isinstance(node, ast.Import):
            for alias in node.names:
                imports.append(f"import {alias.name}")
        elif isinstance(node, ast.ImportFrom):
            module = node.module or ''
            for alias in node.names:
                imports.append(f"from {module} import {alias.name}")
    
    return imports


def generate_migration_report(consolidation_plan: Dict[str, List[str]]) -> str:
    """
    Generate a detailed migration report for the consolidation plan.
    
    Args:
        consolidation_plan: Dict mapping new file names to lists of original files
    
    Returns:
        Formatted report string
    """
    report = ["# Test Migration Report\n"]
    
    total_original_files = 0
    total_new_files = len(consolidation_plan)
    total_original_tests = 0
    
    for new_file, original_files in consolidation_plan.items():
        report.append(f"## {new_file}")
        report.append(f"**Consolidates {len(original_files)} files:**")
        
        file_tests = 0
        for orig_file in original_files:
            if os.path.exists(orig_file):
                test_count = count_test_methods_in_file(orig_file)
                file_tests += test_count
                report.append(f"- {orig_file}: {test_count} tests")
            else:
                report.append(f"- {orig_file}: FILE NOT FOUND")
        
        report.append(f"**Total tests to migrate: {file_tests}**\n")
        
        total_original_files += len(original_files)
        total_original_tests += file_tests
    
    report.append("## Summary")
    report.append(f"- Original files: {total_original_files}")
    report.append(f"- New files: {total_new_files}")
    report.append(f"- File reduction: {total_original_files - total_new_files}")
    report.append(f"- Total tests to migrate: {total_original_tests}")
    
    return "\n".join(report)


def validate_file_exists(file_path: str) -> bool:
    """Check if a file exists and is readable."""
    return os.path.exists(file_path) and os.path.isfile(file_path)


def backup_files(files: List[str], backup_dir: str) -> List[str]:
    """
    Backup a list of files to a backup directory.
    
    Returns:
        List of successfully backed up files
    """
    os.makedirs(backup_dir, exist_ok=True)
    backed_up = []
    
    for file_path in files:
        if validate_file_exists(file_path):
            filename = os.path.basename(file_path)
            backup_path = os.path.join(backup_dir, filename)
            
            try:
                with open(file_path, 'r') as src, open(backup_path, 'w') as dst:
                    dst.write(src.read())
                backed_up.append(file_path)
            except Exception as e:
                print(f"Failed to backup {file_path}: {e}")
    
    return backed_up
