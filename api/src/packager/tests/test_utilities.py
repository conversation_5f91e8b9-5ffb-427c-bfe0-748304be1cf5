"""
Consolidated utility tests for packager module.

This module consolidates tests from:
- test_formatting_utils.py (9 tests)
- test_naming_utils.py (27 tests) 
- test_duplicate_utils.py (3 tests)
- test_utils.py (26 tests)

Total: 65 tests across 4 utility modules
"""
import csv
import io
import json
import logging
import string
import zipfile
from datetime import datetime
from io import BytesIO
from unittest.mock import patch, MagicMock

from django.db.models.signals import post_save
from django.test import TestCase
from django.utils import timezone
from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats, PackagerFileNamingElementTypes
from pydantic import ValidationError as PydanticValidationError

from documents.models import Document
from documents.tests.util import create_document
from extract.models import Extraction
from extract.tests.util import create_extraction
from organizations.tests.util import create_data_pool, create_organization
from packager.models import (DocumentData, Package, DocumentRelationship, DocumentRelationshipType)
from packager.pydantic_classes import (PackagerConfig, NamingConfig, NamingConfigElement,
                                       parse_metadata_field, RenderedPackageItem, SourceFile)
from packager.signals import handle_document_creation_for_coc
from packager.tests.base import FastPackagerTestCase, BulkDataPackagerTestCase
from packager.tests.util import create_packager
from packager.utils.duplicate_utils import apply_duplicate_flags, get_document_relationships, calculate_content_hash
from packager.utils.file_utils import to_bytes, create_package_zip, decode_api_content_for_zip
from packager.utils.formatting_utils import (DataFormattingError, flatten_dict, format_csv,
                                              format_data, format_json)
from packager.utils.id_utils import _generate_random_id
from packager.utils.naming_utils import (generate_filename, get_single_metadata_value,
                                         get_available_metadata_attributes, render_grouped_documents)
from training.tests.util import create_training_ready_training_set, create_training_revision

DUPLICATE_FIELD_ORDER = ['FieldsContentHash', 'FileDuplicate', 'ContentDuplicate', 'LabelDuplicate']

logger = logging.getLogger(__name__)


class FormattingUtilsTests(FastPackagerTestCase):
    """Tests for formatting utilities - from test_formatting_utils.py"""
    
    def setUp(self):
        # Clean up any existing data that might interfere with our tests
        DocumentData.objects.all().delete()
        Package.objects.all().delete()
        Document.objects.all().delete()

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.doc1, _ = create_document(data_pool=self.dp)
        self.doc2, _ = create_document(data_pool=self.dp)

        self.packager = create_packager(data_pool=self.dp, label="Test Packager", source_file_naming_config={
            'elements': [{'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'GLYNT'},
                         {'type': PackagerFileNamingElementTypes.METADATA.name, 'value': 'document.created_at',
                          'format': '%Y-%m-%d'}], 'separator': '-',
            'max_length': 100
        })
        self.ts, _ = create_training_ready_training_set(data_pool=self.dp)
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)
        self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

        # Create DocumentData instances with get_or_create to handle potential duplicates
        self.doc_data1, _ = DocumentData.objects.get_or_create(document=self.doc1, data_pool=self.dp)
        self.doc_data2, _ = DocumentData.objects.get_or_create(document=self.doc2, data_pool=self.dp)

    def tearDown(self):
        """Clean up any resources after each test"""
        DocumentData.objects.all().delete()
        Package.objects.all().delete()
        Document.objects.all().delete()
        super().tearDown()

    def test_format_data(self):
        """Test data formatting with different formats and data structures"""
        test_data = {
            'json_data': [{'field1': 'value1', 'nested': {'key': 'value'}}, {'field2': 'value2', 'list': [1, 2, 3]}]
        }

        json_result = format_data(test_data, 'JSON')
        self.assertIn('json_data', json_result)
        self.assertIn('field1', json_result)
        self.assertIn('nested', json_result)

        csv_result = format_data(test_data, 'CSV')
        for duplicate_field in DUPLICATE_FIELD_ORDER:
            self.assertNotIn(f'"{duplicate_field}"', csv_result.splitlines()[0])

        csv_result = format_data(test_data, 'CSV')
        self.assertIn('field1', csv_result)
        self.assertIn('field2', csv_result)
        self.assertIn('nested.key', csv_result)

        with self.assertRaises(DataFormattingError):
            format_data(test_data, 'INVALID')

        with self.assertRaises(DataFormattingError):
            format_data({'invalid': 'structure'}, 'CSV')

    def test_format_json(self):
        """Test JSON formatting specifically"""
        data = {'key': 'value', 'number': 123}
        result = format_json(data)
        self.assertIn('"key": "value"', result)
        self.assertIn('"number": 123', result)

        nested_data = {'parent': {'child': 'value'}}
        result = format_json(nested_data)
        self.assertIn('"parent":', result)
        self.assertIn('"child":', result)

        complex_data = {'date': timezone.now(), 'list': [1, 2, 3]}
        result = format_json(complex_data)
        self.assertIn('"date":', result)
        self.assertIsInstance(json.loads(result)['timestamp'], str)

        yesterday = timezone.now() - timezone.timedelta(days=1)
        nested_data = {
            'events': [{'timestamp': timezone.now(), 'type': 'current'}, {'timestamp': yesterday, 'type': 'past'}]
        }

        result = format_json(nested_data)
        parsed = json.loads(result)

        self.assertIsInstance(parsed['events'][0]['timestamp'], str)
        self.assertIsInstance(parsed['events'][1]['timestamp'], str)

    def test_format_csv(self):
        """Test CSV formatting with various data structures and explicit ordering."""
        data = {
            'json_data': [{'col1': 'val1', 'col2': 'val2'}, {'col1': 'val3', 'col2': 'val4'}]
        }
        result = format_csv(data, ["col1", "col2"])
        self.assertIn(r'"col1","col2"', result)
        self.assertIn(r'"val1","val2"', result)
        self.assertIn(r'"val3","val4"', result)

        explicit_order = ['col2', 'col1']
        result_ordered = format_csv(data, original_field_keys=explicit_order)
        self.assertIn(r'"col2","col1"', result_ordered.splitlines()[0])
        self.assertIn(r'"val2","val1"', result_ordered)
        self.assertIn(r'"val4","val3"', result_ordered)

        data_extra_keys = {
            'json_data': [{'col1': 'v1', 'col2': 'v2', 'col3': 'v3'}, {'col1': 'v4', 'col2': 'v5'}]
        }
        explicit_order_partial = ['col2', 'col1']
        result_extra = format_csv(data_extra_keys, original_field_keys=explicit_order_partial)
        self.assertIn(r'"col2","col1"', result_extra.splitlines()[0])
        self.assertIn(r'"v5","v4",""', result_extra)

        nested_data = {
            'json_data': [{'col1': {'nested': 'val1'}, 'col2': 'val2'}, {'col1': {'nested': 'val3'}, 'col2': 'val4'}]
        }
        result_nested = format_csv(nested_data, ["col1", "col2"])
        self.assertIn(r'"col1","col2"', result_nested)

        empty_data = {'json_data': []}
        self.assertEqual(format_csv(empty_data), '')

        with self.assertRaises(DataFormattingError):
            format_data({'test': 'data'}, 'INVALID')

        with self.assertRaises(DataFormattingError):
            format_data({'invalid': 'structure'}, 'CSV')

    def test_format_csv_mixed_key_types(self):
        """Test CSV formatting handles mixed key types (None, int, str) without TypeError."""
        data = {
            'json_data': [{'string_key': 'a', 123: 'b', None: 'c'}, {'string_key': 'd', 123: 'e', 'another_key': 'f'}]
        }
        try:
            result = format_csv(data)
            self.assertIn(r'"123","None","another_key","string_key"', result.splitlines()[0])
            self.assertIn(r'"e","","f","d"', result)
            self.assertIn(r'"b","c","","a"', result)

        except TypeError:
            self.fail("format_csv raised TypeError with mixed key types")
        except DataFormattingError as e:
            self.fail(f"format_csv raised DataFormattingError unexpectedly: {e}")

    def test_flatten_dict(self):
        """Test dictionary flattening with various structures"""
        nested_dict = {
            'level1': {
                'level2': {
                    'level3': 'value'
                }
            }, 'simple': 'value'
        }
        result = flatten_dict(nested_dict)
        self.assertEqual(result['level1__level2__level3'], 'value')
        self.assertEqual(result['simple'], 'value')

        dict_with_list = {
            'key': 'value', 'list': [1, 2, 3]
        }
        result = flatten_dict(dict_with_list)
        self.assertEqual(result['list'], '[1, 2, 3]')

        self.assertEqual(flatten_dict({}), {})

        result = flatten_dict(nested_dict, sep='_')
        self.assertEqual(result['level1_level2_level3'], 'value')

    def test_flatten_dict_with_nested_arrays(self):
        """Test flatten_dict with complex nested structure including arrays"""
        nested_dict = {
            "level1": {
                "level2": {
                    "array": [1, 2, 3], "object_array": [{"key": "value"}, {"key": "value2"}]
                }
            }, "simple": "value"
        }

        flattened = flatten_dict(nested_dict, preserve_arrays=True)

        self.assertEqual(flattened["level1__level2__array"], [1, 2, 3])
        self.assertEqual(flattened["level1__level2__object_array"], [{"key": "value"}, {"key": "value2"}])
        self.assertEqual(flattened["simple"], "value")

    def test_format_data_unsupported_format(self):
        """Test format_data with unsupported format"""
        test_data = {"key": "value"}
        with self.assertRaises(DataFormattingError):
            format_data(test_data, "UNSUPPORTED_FORMAT")


class DuplicateFieldsInOutputTests(FastPackagerTestCase):
    """Tests specifically for verifying duplicate fields in different output formats."""

    def test_duplicate_fields_in_both_json_and_csv(self):
        """Test that duplicate fields are properly included in both JSON and CSV outputs."""
        # Prepare test data with duplicate information
        data_with_duplicates = {
            'json_data': [
                {
                    'id': '1',
                    'field1': 'value1',
                    'field2': 'value2',
                    'FieldsContentHash': 'original',
                    'FileDuplicate': 'original for doc123',
                    'ContentDuplicate': 'original for doc456',
                    'LabelDuplicate': ''
                },
                {
                    'id': '2',
                    'field1': 'value3',
                    'field2': 'value4',
                    'FieldsContentHash': '3f7d7e',
                    'FileDuplicate': '',
                    'ContentDuplicate': 'duplicate of doc789',
                    'LabelDuplicate': ''
                }
            ]
        }

        # Test JSON output
        json_output = format_data(
            data_with_duplicates,
            'JSON',
        )

        # Parse JSON to verify structure
        parsed_json = json.loads(json_output)

        # Verify all duplicate fields are in the JSON
        for idx, item in enumerate(parsed_json['json_data']):
            for dup_field in DUPLICATE_FIELD_ORDER:
                self.assertIn(dup_field, item, f"Duplicate field {dup_field} not found in JSON item {idx}")

        # Test CSV output
        csv_output = format_data(
            data_with_duplicates,
            'CSV',
            original_field_keys=['id', 'field1', 'field2']
        )

        # Check that all header fields are in the CSV
        csv_lines = csv_output.strip().split('\n')
        header_line = csv_lines[0]
        csv_reader = csv.reader(io.StringIO(header_line))
        header_fields = next(csv_reader)

        for dup_field in DUPLICATE_FIELD_ORDER:
            self.assertIn(dup_field, header_fields, f"Duplicate field {dup_field} not found in CSV header")

        # Test that content values appear in the CSV data rows
        # Need to parse the data rows as well to get the unquoted values
        csv_data_reader = csv.reader(io.StringIO('\n'.join(csv_lines[1:])))
        data_rows = list(csv_data_reader)

        # Find the index of the duplicate fields in the header
        header_fields.index('FieldsContentHash')
        file_duplicate_index = header_fields.index('FileDuplicate')
        content_duplicate_index = header_fields.index('ContentDuplicate')
        header_fields.index('LabelDuplicate')

        self.assertEqual(data_rows[0][file_duplicate_index], 'original for doc123',
                         "Duplicate flag not found in first data row")
        self.assertEqual(data_rows[1][content_duplicate_index], 'duplicate of doc789',
                         "Duplicate flag not found in second data row")

    def test_package_render_with_duplicate_flags(self):
        """Test that duplicate flags are properly applied when rendering package data."""
        # Prepare test data with duplicate information
        data_with_duplicates = {
            'json_data': [
                {
                    'id': 'doc1',
                    'name': 'Document 1',
                    'FieldsContentHash': 'hash1',
                    'FileDuplicate': 'duplicate_of_doc2',
                    'ContentDuplicate': '',
                    'LabelDuplicate': ''
                },
                {
                    'id': 'doc2',
                    'name': 'Document 2',
                    'FieldsContentHash': 'hash2',
                    'FileDuplicate': '',
                    'ContentDuplicate': 'duplicate_of_doc3',
                    'LabelDuplicate': 'duplicate_of_doc4'
                }
            ]
        }

        # Test JSON output
        json_output = format_data(
            data_with_duplicates,
            'JSON'
        )

        # Parse JSON to verify structure
        parsed_json = json.loads(json_output)

        # Verify all duplicate fields are in the JSON output
        for item in parsed_json['json_data']:
            for field in DUPLICATE_FIELD_ORDER:
                self.assertIn(field, item, f"Duplicate field {field} missing from JSON output")

        # Test CSV output
        csv_output = format_data(
            data_with_duplicates,
            'CSV',
            original_field_keys=['id', 'name']
        )

        # Check CSV header contains all duplicate fields
        csv_lines = csv_output.strip().split('\n')
        header = csv_lines[0]
        for field in DUPLICATE_FIELD_ORDER:
            self.assertIn(field, header, f"Duplicate field {field} missing from CSV header")

        # Verify duplicate values are preserved in CSV data
        csv_reader = csv.DictReader(io.StringIO(csv_output))
        rows = list(csv_reader)

        # Check first row values
        self.assertEqual(rows[0]['FieldsContentHash'], 'hash1')
        self.assertEqual(rows[0]['FileDuplicate'], 'duplicate_of_doc2')
        self.assertEqual(rows[0]['ContentDuplicate'], '')
        self.assertEqual(rows[0]['LabelDuplicate'], '')

        # Check second row values
        self.assertEqual(rows[1]['FieldsContentHash'], 'hash2')
        self.assertEqual(rows[1]['FileDuplicate'], '')
        self.assertEqual(rows[1]['ContentDuplicate'], 'duplicate_of_doc3')
        self.assertEqual(rows[1]['LabelDuplicate'], 'duplicate_of_doc4')


class DuplicateUtilsTests(TestCase):
    """Tests for duplicate utilities - from test_duplicate_utils.py"""

    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        now = timezone.now()
        self.doc1, _ = create_document(data_pool=self.dp, label="Original", content_md5="md5_1",
                                       created_at=now - timezone.timedelta(hours=2))
        self.doc2, _ = create_document(data_pool=self.dp, label="Duplicate1", content_md5="md5_2",
                                       created_at=now - timezone.timedelta(hours=1))
        self.doc3, _ = create_document(data_pool=self.dp, label="Duplicate2", content_md5="md5_3", created_at=now)

        self.doc_data1, _ = DocumentData.objects.get_or_create(document=self.doc1, data_pool=self.dp)
        self.doc_data2, _ = DocumentData.objects.get_or_create(document=self.doc2, data_pool=self.dp)
        self.doc_data3, _ = DocumentData.objects.get_or_create(document=self.doc3, data_pool=self.dp)

        self.doc_data1.source_file_id = "src_file_1"
        self.doc_data1.save()
        self.doc_data2.source_file_id = "src_file_2"
        self.doc_data2.save()
        self.doc_data3.source_file_id = "src_file_3"
        self.doc_data3.save()

        self.doc_data1.received_at = now - timezone.timedelta(hours=2)
        self.doc_data1.save()
        self.doc_data2.received_at = now - timezone.timedelta(hours=1)
        self.doc_data2.save()
        self.doc_data3.received_at = now
        self.doc_data3.save()

        self.rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="CONTENT_DUPLICATE",
            defaults={'description': "Documents with identical content", 'data_pool': self.dp}
        )

        DocumentRelationship.objects.create(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data2,
            relationship_type=self.rel_type,
            data_pool=self.dp
        )
        DocumentRelationship.objects.create(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data3,
            relationship_type=self.rel_type,
            data_pool=self.dp
        )

    def test_get_document_relationships_with_document_relationships(self):
        """Test get_document_relationships uses DocumentRelationship objects properly."""
        # Get relationships using the new approach
        obfuscated_ids = [
            self.doc_data1.obfuscated_id,
            self.doc_data2.obfuscated_id,
            self.doc_data3.obfuscated_id,
        ]
        relationships = get_document_relationships(document_obfuscated_ids=obfuscated_ids, data_pool_id=self.dp.id)

        # Check that relationships were correctly identified
        # relationships is a list of dicts with keys: source_document_id, target_document_id, relationship_type, id
        # We expect two relationships: doc_data1 points to doc_data2 and doc_data3 as duplicates
        expected = set([
            (self.doc_data1.document.obfuscated_id, self.doc_data2.document.obfuscated_id),
            (self.doc_data1.document.obfuscated_id, self.doc_data3.document.obfuscated_id),
        ])
        found = set((rel['source_document_id'], rel['target_document_id']) for rel in relationships)
        self.assertEqual(found, expected)

    def test_apply_duplicate_flags_with_document_relationship_approach(self):
        """Test apply_duplicate_flags works with DocumentRelationship approach output."""
        # Get relationships
        obfuscated_ids = [
            self.doc_data1.obfuscated_id,
            self.doc_data2.obfuscated_id,
            self.doc_data3.obfuscated_id,
        ]
        get_document_relationships(document_obfuscated_ids=obfuscated_ids, data_pool_id=self.dp.id)

        # Apply flags to original document
        data_row_original = {"doc_id": "src_file_1"}
        data_row_original = apply_duplicate_flags(data_row_original, self.doc_data1, for_json=True)

        # Original should have has_duplicates=True
        self.assertTrue(data_row_original["has_duplicates"])
        self.assertFalse(data_row_original.get("is_duplicate", False))
        self.assertIn("duplicate_doc_ids", data_row_original)
        self.assertIn(self.doc2.obfuscated_id, data_row_original["duplicate_doc_ids"])
        self.assertIn(self.doc3.obfuscated_id, data_row_original["duplicate_doc_ids"])

        # Apply flags to duplicate document
        data_row_duplicate = {"doc_id": "src_file_2"}
        data_row_duplicate = apply_duplicate_flags(data_row_duplicate, self.doc_data2, for_json=True)

        # Duplicate should have is_duplicate=True
        self.assertTrue(data_row_duplicate["is_duplicate"])
        self.assertEqual(data_row_duplicate["duplicate_of"], self.doc1.obfuscated_id)

    def test_calculate_content_hash(self):
        """Test calculate_content_hash function."""
        data_row = {
            "field1": "value1",
            "field2": "value2",
            "field3": 123
        }

        # Test with valid fields
        hash1 = calculate_content_hash(data_row, ["field1", "field2"])
        self.assertIsNotNone(hash1)

        # Test with different field order (should be the same hash)
        hash2 = calculate_content_hash(data_row, ["field2", "field1"])
        self.assertNotEqual(hash1, hash2)  # Order matters

        # Test with missing field
        hash3 = calculate_content_hash(data_row, ["field1", "missing_field"])
        self.assertIsNotNone(hash3)
        self.assertNotEqual(hash1, hash3)

        # Test with empty fields list
        hash4 = calculate_content_hash(data_row, [])
        self.assertIsNone(hash4)


class NamingUtilsTests(BulkDataPackagerTestCase):
    """Tests for naming utilities - from test_naming_utils.py"""

    @patch('packager.utils.naming_utils.logger')
    def test_render_grouped_documents_packager_config_error(self, mock_logger):
        """
        Test that render_grouped_documents handles errors when fetching packager config.
        It should log an error and proceed with default CoC settings (disabled).
        """
        # Create mock Document and DocumentData objects using simple classes
        mock_document = MockDocumentForTest(
            obfuscated_id="doc_err_123",
            label="Error Config Test Document",
            status="VERIFIED",
            created_at=datetime(2023, 1, 1, 10, 0, 0),
            updated_at=datetime(2023, 1, 1, 11, 0, 0)
        )
        mock_doc_data = MockDocumentDataForTest(
            pk=1,
            obfuscated_id="doc_err_123",
            document=mock_document,
            source_file_id="source_config_error",
            canonical_document_data=None
        )

        # Mock Packager object whose get_config method raises an exception
        mock_packager = MagicMock()
        mock_packager.get_config.side_effect = Exception("Simulated config error!")
        # Don't mock generate_processed_data_filename to test both error cases
        grouped_docs = {
            "error_group": [mock_doc_data]
        }

        # Patch DocumentData.objects.get to return the mock object for the refresh logic
        with patch('packager.models.DocumentData.objects.get') as mock_db_get:
            mock_db_get.return_value = mock_doc_data

            # Call the function
            result = render_grouped_documents(grouped_docs, mock_packager, include_document_content=False)

        # Assertions

        # Expect two error calls:
        # 1. Config error
        # 2. Filename generation error
        from unittest.mock import call
        mock_logger.error.assert_has_calls([
            call("Error getting packager config for rendering: Simulated config error!"),
            call('Generated filename for group error_group is not a string or is empty. Using fallback.')
        ])

        self.assertEqual(mock_logger.error.call_count, 2)

        self.assertEqual(len(result.rendered_items), 1)
        # The content is a JSON string representing a list of dicts; parse it
        import json
        rendered_item_content = json.loads(result.rendered_items[0].content)
        # Access the content from the 'data' key, which is a list, and get the first item
        self.assertIn('data', rendered_item_content)
        self.assertIsInstance(rendered_item_content['data'], list)
        self.assertEqual(len(rendered_item_content['data']), 1)
        rendered_item_content = rendered_item_content['data'][0]

        # Check that CoC fields are NOT appended due to the error (default behavior)
        self.assertNotIn("source_file_id", rendered_item_content)

        # Check that original doc_entry fields are still present (base rendering worked)
        self.assertIn("id", rendered_item_content)
        self.assertEqual(rendered_item_content["id"], mock_doc_data.document.obfuscated_id)
        self.assertIn("label", rendered_item_content)
        self.assertEqual(rendered_item_content["label"], mock_doc_data.document.label)
        self.assertIn("status", rendered_item_content)
        self.assertEqual(rendered_item_content["status"], mock_doc_data.document.status)

        # Ensure no unexpected errors in the result list itself
        self.assertEqual(len(result.errors), 0)

    def test_render_grouped_documents_with_coc_fields(self):
        """
        Test that render_grouped_documents appends CoC fields
        when configuration is enabled.
        """

        # Create a class that can be serialized to JSON for testing
        class SerializableObject:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)

            def __getattr__(self, name):
                # Return None for attributes that don't exist
                if name.startswith('__'):
                    raise AttributeError(name)
                return None

        # Create Document object that can be serialized
        mock_document = SerializableObject(
            obfuscated_id="doc_123",
            label="Test Document",
            status="VERIFIED",
            created_at=datetime(2023, 1, 1, 10, 0, 0),
            updated_at=datetime(2023, 1, 1, 11, 0, 0),
        )

        # Configure mock document's extractions to handle properly
        mock_document.extractions = SerializableObject()
        mock_document.extractions.exists = lambda: False

        # Create DocumentData object that can be serialized
        mock_doc_data = SerializableObject(
            pk=1,
            obfuscated_id="doc_123",
            document=mock_document,
            source_file_id="source_123",
            source_file_name="source.pdf",
            source_file_md5="md5hash",
            source_file_size=1024,
            source_file_num_pages=5,
            received_at="2023-01-01T09:00:00Z",
            status="VERIFIED",
            parent_file_id=None,
            relationship_to_parent=None,
            exclusion_reason=None,
            id="doc_123"
        )

        # Mock PackagerConfig with CoC field appending enabled
        mock_config = MagicMock(spec=PackagerConfig)
        mock_config.output_format = PackagerDataOutputFormats.JSON.name
        mock_config.append_coc_fields_to_data = True
        mock_config.coc_field_config = [
            "source_file_id",
            "source_file_name",
            "received_at",
            "status"
        ]
        mock_config.delivery_data_grouping_strategy = "SPLIT_BY_DOC"  # Or any other strategy
        mock_config.include_relationship_details_in_data_export = False
        mock_config.exclude_duplicates_from_delivery = False  # Add the missing attribute

        # Mock Packager object with get_config method
        mock_packager = MagicMock()
        mock_packager.get_config.return_value = mock_config

        # Mock grouped_docs structure
        grouped_docs = {
            "group1": [mock_doc_data]
        }

        # Mock apps.get_model to return a mock DocumentData model
        # This is needed because render_grouped_documents dynamically gets the model
        # Patch DocumentData.objects.get to return the mock object
        with patch('packager.models.DocumentData.objects.get') as mock_get:
            mock_get.return_value = mock_doc_data

            # Call the function
            result = render_grouped_documents(grouped_docs, mock_packager, include_document_content=False)

            # Assertions
            self.assertEqual(len(result.rendered_items), 1)
            rendered_item_content = result.rendered_items[0].content
            import json
            rendered_item_content = json.loads(rendered_item_content)
            # Access the content from the 'data' key, which is a list, and get the first item
            self.assertIn('data', rendered_item_content)
            self.assertIsInstance(rendered_item_content['data'], list)
            self.assertEqual(len(rendered_item_content['data']), 1)  # Assuming one item for this test
            rendered_item_content = rendered_item_content['data'][0]

            # Check that the appended fields are present in the output
            self.assertIn("source_file_id", rendered_item_content)
            self.assertIn("source_file_name", rendered_item_content)
            self.assertIn("received_at", rendered_item_content)
            self.assertIn("status", rendered_item_content)

            # Check that the values are correctly mapped
            self.assertEqual(rendered_item_content["source_file_id"], mock_doc_data.source_file_id)
            self.assertEqual(rendered_item_content["source_file_name"], mock_doc_data.source_file_name)
            self.assertEqual(rendered_item_content["received_at"], mock_doc_data.received_at)
            self.assertEqual(rendered_item_content["status"], mock_doc_data.status)

            # Check that original doc_entry fields are also present
            self.assertIn("id", rendered_item_content)
            self.assertIn("label", rendered_item_content)
            self.assertIn("status", rendered_item_content)

    def test_render_grouped_documents_without_coc_fields(self):
        """
        Test that render_grouped_documents does NOT append CoC fields
        when configuration is disabled.
        """
        # Create mock DocumentData and Document objects
        mock_doc_data = MagicMock(spec=DocumentData)
        mock_doc_data.pk = 1  # Add a mock primary key
        mock_doc_data.obfuscated_id = "doc_123"
        mock_doc_data.document = MagicMock()
        mock_doc_data.document.obfuscated_id = "doc_123"
        mock_doc_data.document.label = "Test Document"
        mock_doc_data.document.status = "VERIFIED"
        mock_doc_data.document.created_at = datetime(2023, 1, 1, 10, 0, 0)
        mock_doc_data.document.updated_at = datetime(2023, 1, 1, 11, 0, 0)
        mock_doc_data.source_file_id = "source_123"
        mock_doc_data.source_file_name = "source.pdf"
        mock_doc_data.source_file_md5 = "md5hash"
        mock_doc_data.source_file_size = 1024
        mock_doc_data.source_file_num_pages = 5
        mock_doc_data.received_at = "2023-01-01T09:00:00Z"
        mock_doc_data.status = "VERIFIED"
        mock_doc_data.parent_file_id = None
        mock_doc_data.relationship_to_parent = None
        mock_doc_data.exclusion_reason = None

        # Mock PackagerConfig with CoC field appending disabled
        mock_config = MagicMock(spec=PackagerConfig)
        mock_config.append_coc_fields_to_data = False  # Disabled
        mock_config.coc_field_config = [
            "source_file_id",
            "source_file_name",
            "received_at",
            "status"
        ]
        mock_config.delivery_data_grouping_strategy = "SPLIT_BY_DOC"
        mock_config.include_relationship_details_in_data_export = False
        mock_config.exclude_duplicates_from_delivery = False
        # Mock Packager object with get_config method
        mock_packager = MagicMock()
        mock_packager.get_config.return_value = mock_config

        # Mock grouped_docs structure
        grouped_docs = {
            "group1": [mock_doc_data]
        }

        # Mock apps.get_model to return a mock DocumentData model
        # This is needed because render_grouped_documents dynamically gets the model
        # Patch DocumentData.objects.get to return the mock object
        with patch('packager.models.DocumentData.objects.get') as mock_get:
            mock_get.return_value = mock_doc_data

            # Call the function
            result = render_grouped_documents(grouped_docs, mock_packager, include_document_content=False)

            # Assertions
            self.assertEqual(len(result.rendered_items), 1)
            rendered_item_content = result.rendered_items[0].content

            # Check that the appended fields are NOT present
            self.assertNotIn("source_file_id", rendered_item_content)
            self.assertNotIn("source_file_name", rendered_item_content)
            self.assertNotIn("received_at", rendered_item_content)
            self.assertNotIn("DocumentID", rendered_item_content)
            self.assertIn("id", rendered_item_content)
            self.assertIn("label", rendered_item_content)
            self.assertIn("status", rendered_item_content)

    def test_render_grouped_documents_with_empty_coc_config(self):
        """
        Test that render_grouped_documents does NOT append CoC fields
        when the field list is empty.
        """
        # Create mock DocumentData and Document objects
        mock_doc_data = MagicMock(spec=DocumentData)
        mock_doc_data.pk = 1  # Add a mock primary key
        mock_doc_data.obfuscated_id = "doc_123"
        mock_doc_data.document = MagicMock()
        mock_doc_data.document.obfuscated_id = "doc_123"
        mock_doc_data.document.label = "Test Document"
        mock_doc_data.document.status = "VERIFIED"
        mock_doc_data.document.created_at = datetime(2023, 1, 1, 10, 0, 0)
        mock_doc_data.document.updated_at = datetime(2023, 1, 1, 11, 0, 0)
        mock_doc_data.source_file_id = "source_123"
        mock_doc_data.source_file_name = "source.pdf"
        mock_doc_data.source_file_md5 = "md5hash"
        mock_doc_data.source_file_size = 1024
        mock_doc_data.source_file_num_pages = 5
        mock_doc_data.received_at = "2023-01-01T09:00:00Z"
        mock_doc_data.status = "VERIFIED"
        mock_doc_data.parent_file_id = None
        mock_doc_data.relationship_to_parent = None
        mock_doc_data.exclusion_reason = None

        # Mock PackagerConfig with CoC field appending enabled but empty list
        mock_config = MagicMock(spec=PackagerConfig)
        mock_config.append_coc_fields_to_data = True
        mock_config.coc_field_config = []  # Empty list
        mock_config.delivery_data_grouping_strategy = "SPLIT_BY_DOC"
        mock_config.include_relationship_details_in_data_export = False
        mock_config.exclude_duplicates_from_delivery = False  # Add the missing attribute

        # Mock Packager object with get_config method
        mock_packager = MagicMock()
        mock_packager.get_config.return_value = mock_config

        # Mock grouped_docs structure
        grouped_docs = {
            "group1": [mock_doc_data]
        }

        # Mock apps.get_model to return a mock DocumentData model
        # This is needed because render_grouped_documents dynamically gets the model
        # Patch DocumentData.objects.get to return the mock object
        with patch('packager.models.DocumentData.objects.get') as mock_get:
            mock_get.return_value = mock_doc_data

            # Call the function
            result = render_grouped_documents(grouped_docs, mock_packager, include_document_content=False)

            # Assertions
            self.assertEqual(len(result.rendered_items), 1)
            rendered_item_content = result.rendered_items[0].content

            # Check that no extra fields were appended
            # We can check for the presence of a known CoC field that would have been appended
            self.assertNotIn("source_file_id", rendered_item_content)

            # Check that original doc_entry fields are still present
            self.assertIn("id", rendered_item_content)
            self.assertIn("label", rendered_item_content)
            self.assertIn("status", rendered_item_content)


# Helper mock classes for NamingUtilsTests
class MockDocumentForTest:
    def __init__(self, obfuscated_id, label, status, created_at, updated_at):
        self.obfuscated_id = obfuscated_id
        self.label = label
        self.status = status
        self.created_at = created_at
        self.updated_at = updated_at


class MockDocumentDataForTest:
    def __init__(self, pk, obfuscated_id, document, source_file_id, canonical_document_data):
        self.pk = pk
        self.id = pk  # Add id attribute that duplicate_utils expects
        self.obfuscated_id = obfuscated_id
        self.document = document
        self.source_file_id = source_file_id
        self.canonical_document_data = canonical_document_data
        # Add a _meta attribute to make it look like a Django model for ensure_serializable
        self._meta = MagicMock()
        self._meta.concrete_fields = [
            MagicMock(name='pk'),
            MagicMock(name='id'),
            MagicMock(name='obfuscated_id'),
            MagicMock(name='document'),
            MagicMock(name='source_file_id'),
            MagicMock(name='canonical_document_data'),
        ]


# Helper mock classes
class MockDocument:
    def __init__(self, **kwargs):
        self.obfuscated_id = None  # Default common attribute
        self.__dict__.update(kwargs)
        self.__class__.__name__ = 'Document'

    @property
    def data_pool_id(self):
        # Simulate Django's foreign key _id attribute
        return self.data_pool.id if self.data_pool and hasattr(self.data_pool, 'id') else None


class MockDataPool:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
        self.__class__.__name__ = 'DataPool'


class MockDocumentData:
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)
        self.__class__.__name__ = 'DocumentData'


class GetSingleMetadataValueTests(FastPackagerTestCase):
    """Tests for get_single_metadata_value function"""

    def setUp(self):
        self.mock_data_pool = MockDataPool(name="Test Pool", obfuscated_id="dp123")
        self.mock_document = MockDocument(
            label="Test Doc",
            obfuscated_id="doc123",
            created_at=datetime(2023, 1, 1, 10, 30, 0),
            data_pool=self.mock_data_pool,
            metadata={"custom_key": "custom_value", "another_key": 123, "none_key": None},
            is_active=True,
            version=1,
            description=None,
            complex_field={"key": "value", "nested": [1, 2]}
        )
        self.mock_doc_data_instance = MockDocumentData(
            some_field="doc_data_value",
            document=self.mock_document,
            obfuscated_id="dd123"
        )

    def test_get_value_direct_attribute(self):
        result = get_single_metadata_value("document.label", self.mock_document)
        self.assertEqual(result, "Test Doc")

    def test_get_value_datetime_with_format(self):
        result = get_single_metadata_value("document.created_at", self.mock_document, date_format="%Y-%m-%d")
        self.assertEqual(result, "2023-01-01")

    def test_get_value_datetime_without_format(self):
        # Relies on the default string representation of datetime
        expected_value = str(self.mock_document.created_at)
        result = get_single_metadata_value("document.created_at", self.mock_document)
        self.assertEqual(result, expected_value)

    def test_get_value_none_attribute(self):
        result = get_single_metadata_value("document.description", self.mock_document)
        self.assertEqual(result, "")

    def test_get_value_boolean_attribute_true(self):
        result = get_single_metadata_value("document.is_active", self.mock_document)
        self.assertEqual(result, "True")

    def test_get_value_boolean_attribute_false(self):
        self.mock_document.is_active = False
        result = get_single_metadata_value("document.is_active", self.mock_document)
        self.assertEqual(result, "False")

    def test_get_value_integer_attribute(self):
        result = get_single_metadata_value("document.version", self.mock_document)
        self.assertEqual(result, "1")

    def test_get_value_document_metadata_existing_key(self):
        result = get_single_metadata_value("document.metadata.custom_key", self.mock_document)
        self.assertEqual(result, "custom_value")

    def test_get_value_document_metadata_integer_value(self):
        result = get_single_metadata_value("document.metadata.another_key", self.mock_document)
        self.assertEqual(result, "123")

    def test_get_value_document_metadata_none_value(self):
        result = get_single_metadata_value("document.metadata.none_key", self.mock_document)
        self.assertEqual(result, "")

    def test_get_value_document_metadata_non_existent_key(self):
        result = get_single_metadata_value("document.metadata.non_existent_key", self.mock_document)
        self.assertEqual(result, "")

    def test_get_value_document_metadata_is_none(self):
        self.mock_document.metadata = None
        result = get_single_metadata_value("document.metadata.custom_key", self.mock_document)
        self.assertEqual(result, "")

    @patch('django.apps.apps.get_model')
    def test_get_value_document_data_from_document_found(self, mock_get_model):
        MockModel = MagicMock()
        # Create a mock object that will be returned by filter()
        mock_filter_result = MagicMock()
        # Set the return value of the first() method on the filter result mock
        mock_filter_result.first.return_value = self.mock_doc_data_instance
        # Set the return value of filter() when called with the expected arguments
        MockModel.objects.filter.return_value = mock_filter_result
        mock_get_model.return_value = MockModel

        result = get_single_metadata_value("document_data.some_field", self.mock_document)
        self.assertEqual(result, "doc_data_value")
        mock_get_model.assert_called_once_with('packager', 'DocumentData')
        MockModel.objects.filter.assert_called_once_with(document=self.mock_document)

    @patch('django.apps.apps.get_model')
    def test_get_value_document_data_from_document_not_found(self, mock_get_model):
        MockModel = MagicMock()
        MockModel.objects.filter().first.return_value = None
        mock_get_model.return_value = MockModel

        result = get_single_metadata_value("document_data.some_field", self.mock_document)
        self.assertEqual(result, "")

    @patch('django.apps.apps.get_model')
    def test_get_value_document_data_non_existent_field_raises_valueerror(self, mock_get_model):
        """
        Test that get_single_metadata_value raises ValueError when a non-existent
        field is requested on the document_data object.
        """
        # Create a mock DocumentData instance that does NOT have 'non_existent_field'
        mock_doc_data = MagicMock()
        # Ensure it doesn't have the attribute
        if hasattr(mock_doc_data, 'non_existent_field'):
            del mock_doc_data.non_existent_field

        MockModel = MagicMock()

        # Define a side effect for filter that returns a mock object with first configured
        def filter_side_effect(*args, **kwargs):
            mock_queryset_return = MagicMock()
            mock_queryset_return.first.return_value = mock_doc_data
            return mock_queryset_return

        MockModel.objects.filter.side_effect = filter_side_effect

        mock_get_model.return_value = MockModel

        # Assert that calling with a non-existent field raises ValueError
        with self.assertRaisesRegex(ValueError, "Could not find field non_existent_field on"):
            get_single_metadata_value("document_data.non_existent_field", self.mock_document)

        mock_get_model.assert_called_once_with('packager', 'DocumentData')
        MockModel.objects.filter.assert_called_once_with(document=self.mock_document)

    def test_invalid_metadata_field_format_raises_valueerror(self):
        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: documentlabel. Expected format: 'model.field'"):
            get_single_metadata_value("documentlabel", self.mock_document)

    def test_non_existent_field_raises_valueerror(self):
        with self.assertRaisesRegex(ValueError, "Could not find field non_existent_field on"):
            get_single_metadata_value("document.non_existent_field", self.mock_document)

    def test_non_existent_relationship_raises_valueerror(self):
        # Use a DocumentData instance to test relationship traversal starting from document
        with self.assertRaisesRegex(ValueError, r"Could not find relationship non_existent_rel on .*"):
            get_single_metadata_value("document.non_existent_rel.some_field", self.mock_doc_data_instance)

    def test_none_intermediate_object_in_path(self):
        self.mock_document.data_pool = None
        result = get_single_metadata_value("document.data_pool_id", self.mock_document)
        self.assertEqual(result, "")

    def test_invalid_input_none_metadata_field_raises_valueerror(self):
        with self.assertRaisesRegex(ValueError, "Invalid metadata field or source object: None"):
            get_single_metadata_value(None, self.mock_document)

    def test_invalid_input_empty_metadata_field_raises_valueerror(self):
        with self.assertRaisesRegex(ValueError, "Invalid metadata field or source object: "):
            get_single_metadata_value("", self.mock_document)

    def test_invalid_input_none_source_obj_raises_valueerror(self):
        with self.assertRaisesRegex(ValueError, "Invalid metadata field or source object: document.label"):
            get_single_metadata_value("document.label", None)

    @patch('packager.utils.naming_utils.logger.error')
    def test_generic_exception_during_processing(self, mock_logger_error):
        class DocWithFaultyProperty(MockDocument):
            @property
            def problematic_field(self):
                raise TypeError("Property error")

        faulty_doc = DocWithFaultyProperty(label="test", obfuscated_id="faulty_doc")

        result = get_single_metadata_value("document.problematic_field", faulty_doc)
        self.assertEqual(result, "")
        mock_logger_error.assert_called_once()
        self.assertIn("Error getting metadata value for document.problematic_field: Property error",
                      mock_logger_error.call_args[0][0])


class GeneralUtilsTests(FastPackagerTestCase):
    """Tests for general utilities - from test_utils.py"""

    def setUp(self):
        # Disconnect the signal to prevent automatic DocumentData creation
        post_save.disconnect(handle_document_creation_for_coc, sender=Document)

        try:
            self.org, _ = create_organization()
            self.dp, _ = create_data_pool(organization=self.org)
            self.doc1, _ = create_document(data_pool=self.dp)
            self.doc2, _ = create_document(data_pool=self.dp)

            self.packager = create_packager(data_pool=self.dp, label="Test Packager", source_file_naming_config={
                'elements': [{'type': 'STRING_LITERAL', 'value': 'GLYNT'},
                             {'type': 'METADATA', 'value': 'document.created_at', 'format': '%Y-%m-%d'}],
                'separator': '-',
                'max_length': 100
            })
            self.ts, _ = create_training_ready_training_set(data_pool=self.dp)
            self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)
            self.package = Package.objects.create(packager=self.packager, data_pool=self.dp)

            # Now we can create DocumentData objects manually without conflicts
            self.doc_data1 = DocumentData.objects.create(document=self.doc1, data_pool=self.dp)
            self.doc_data2 = DocumentData.objects.create(document=self.doc2, data_pool=self.dp)
        finally:
            # Reconnect the signal for other tests
            post_save.connect(handle_document_creation_for_coc, sender=Document)

    def test_get_single_metadata_value(self):
        """Test metadata value retrieval for different model types and formats"""
        value = get_single_metadata_value('document.obfuscated_id', self.doc1)
        self.assertEqual(value, self.doc1.obfuscated_id)

        value = get_single_metadata_value('document_data.obfuscated_id', self.doc1)
        self.assertEqual(value, self.doc_data1.obfuscated_id)

        # Test organization ID lookup through document's data_pool relationship
        value = get_single_metadata_value('data_pool.organization.obfuscated_id', self.doc1)
        self.assertEqual(value, self.org.obfuscated_id)

        value = get_single_metadata_value('document.created_at', self.doc1,
                                          '%Y-%m-%d')
        self.assertEqual(value, self.doc1.created_at.strftime('%Y-%m-%d'))

        with self.assertRaises(ValueError):
            get_single_metadata_value('invalid_format', self.doc1)

    def test_get_available_metadata_attributes(self):
        """Test retrieval of available metadata attributes"""
        attributes = get_available_metadata_attributes()

        self.assertIn('document', attributes)
        self.assertIn('extraction_batch', attributes)
        self.assertIn('data_pool', attributes)
        self.assertIn('organization', attributes)

        doc_attrs = attributes['document']
        self.assertTrue(any('document.id' in attr for attr in doc_attrs))
        self.assertTrue(any('document.created_at' in attr for attr in doc_attrs))

        dp_attrs = attributes['data_pool']
        self.assertTrue(any('data_pool.id' in attr for attr in dp_attrs))

    def test_parse_metadata_field(self):
        """Test metadata field parsing for various formats"""
        model, field = parse_metadata_field('document.created_at')
        self.assertEqual(model, 'document')
        self.assertEqual(field, 'created_at')

        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: invalid. "
                                    "Expected format: 'model.field'"):
            parse_metadata_field('invalid')

        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: too.many.parts. "
                                    "Expected format: 'model.field'"):
            parse_metadata_field('too.many.parts')

        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: . Expected format: 'model.field'"):
            parse_metadata_field('')

    def test_format_data(self):
        """Test data formatting with different formats and data structures"""
        test_data = {
            'json_data': [{'field1': 'value1', 'nested': {'key': 'value'}}, {'field2': 'value2', 'list': [1, 2, 3]}]
        }

        json_result = format_data(test_data, PackagerDataOutputFormats.JSON.name)
        self.assertIn('json_data', json_result)
        self.assertIn('field1', json_result)
        self.assertIn('nested', json_result)

        csv_result = format_data(test_data, PackagerDataOutputFormats.CSV.name)

        csv_result = format_data(test_data, PackagerDataOutputFormats.CSV.name)
        self.assertIn('field1', csv_result)
        self.assertIn('field2', csv_result)
        self.assertIn('nested.key', csv_result)

        with self.assertRaises(DataFormattingError):
            format_data(test_data, 'INVALID')

        with self.assertRaises(DataFormattingError):
            format_data({'invalid': 'structure'}, PackagerDataOutputFormats.CSV.name)

    def test_format_json(self):
        """Test JSON formatting specifically"""
        data = {'key': 'value', 'number': 123}
        result = format_json(data)
        self.assertIn('"key": "value"', result)
        self.assertIn('"number": 123', result)

        nested_data = {'parent': {'child': 'value'}}
        result = format_json(nested_data)
        self.assertIn('"parent":', result)
        self.assertIn('"child":', result)

        complex_data = {'date': timezone.now(), 'list': [1, 2, 3]}
        result = format_json(complex_data)
        self.assertIn('"date":', result)
        self.assertIn('"list":', result)

    def test_format_csv(self):
        """Test CSV formatting with various data structures and explicit ordering."""
        data = {
            'json_data': [{'col1': 'val1', 'col2': 'val2'}, {'col1': 'val3', 'col2': 'val4'}]
        }
        result = format_csv(data, ["col1", "col2"])
        self.assertIn(r'"col1","col2"', result)
        self.assertIn(r'"val1","val2"', result)
        self.assertIn(r'"val3","val4"', result)

        explicit_order = ['col2', 'col1']
        result_ordered = format_csv(data, original_field_keys=explicit_order)
        self.assertIn(r'"col2","col1"', result_ordered.splitlines()[0])
        self.assertIn(r'"val2","val1"', result_ordered)
        self.assertIn(r'"val4","val3"', result_ordered)

        data_extra_keys = {
            'json_data': [{'col1': 'v1', 'col2': 'v2', 'col3': 'v3'}, {'col1': 'v4', 'col2': 'v5'}]
        }
        explicit_order_partial = ['col2', 'col1']
        result_extra = format_csv(data_extra_keys, original_field_keys=explicit_order_partial)
        self.assertIn(r'"col2","col1"', result_extra.splitlines()[0])
        self.assertIn(r'"v5","v4",""', result_extra)

        nested_data = {
            'json_data': [{'col1': {'nested': 'val1'}, 'col2': 'val2'}, {'col1': {'nested': 'val3'}, 'col2': 'val4'}]
        }
        result_nested = format_csv(nested_data, ["col1", "col2"])
        self.assertIn(r'"col1","col2"', result_nested)

        empty_data = {'json_data': []}
        self.assertEqual(format_csv(empty_data), '')

        with self.assertRaises(DataFormattingError):
            format_csv({'invalid': 'structure'})

    def test_format_csv_mixed_key_types(self):
        """Test CSV formatting handles mixed key types (None, int, str) without TypeError."""
        data = {
            'json_data': [{'string_key': 'a', 123: 'b', None: 'c'}, {'string_key': 'd', 123: 'e', 'another_key': 'f'}]
        }
        try:
            result = format_csv(data)
            self.assertIn(r'"123","None","another_key","string_key"', result.splitlines()[0])
            self.assertIn(r'"e","","f","d"', result)
            self.assertIn(r'"b","c","","a"', result)

        except TypeError:
            self.fail("format_csv raised TypeError with mixed key types")
        except DataFormattingError as e:
            self.fail(f"format_csv raised DataFormattingError unexpectedly: {e}")

    def test_flatten_dict(self):
        """Test dictionary flattening with various structures"""
        nested_dict = {
            'level1': {
                'level2': {
                    'level3': 'value'
                }
            }, 'simple': 'value'
        }
        result = flatten_dict(nested_dict)
        self.assertEqual(result['level1__level2__level3'], 'value')
        self.assertEqual(result['simple'], 'value')

        dict_with_list = {
            'key': 'value', 'list': [1, 2, 3]
        }
        result = flatten_dict(dict_with_list)
        self.assertEqual(result['list'], '[1, 2, 3]')

        self.assertEqual(flatten_dict({}), {})

        result = flatten_dict(nested_dict, sep='_')
        self.assertEqual(result['level1_level2_level3'], 'value')

    def test_generate_filename(self):
        """Test filename generation with various configurations"""
        test_date = timezone.now()
        self.doc1.created_at = test_date
        self.doc1.save()

        # Use the cached Pydantic NamingConfig directly
        source_naming_config: NamingConfig = self.packager.naming_config
        filename = generate_filename(source_naming_config, '.pdf', self.doc1,
                                     'fallback.pdf',
                                     is_processed_data_config=False)
        self.assertTrue(filename.endswith('.pdf'))
        self.assertIn('GLYNT', filename)
        self.assertIn(test_date.strftime('%Y-%m-%d'), filename)

        # Create NamingConfig from dictionary for testing specific scenarios
        processed_naming_config_with_label = NamingConfig(
            elements=[NamingConfigElement(type='STRING_LITERAL', value='DOCDATA'),
                      NamingConfigElement(type='METADATA', value='document.label')], separator='_',
            max_length=100
        )
        self.doc1.label = "test_document.pdf"
        self.doc1.save()

        filename = generate_filename(processed_naming_config_with_label, '.json',
                                     self.doc1, 'fallback.json',
                                     is_processed_data_config=True)
        self.assertTrue(filename.endswith('.json'))
        self.assertIn('DOCDATA', filename)
        self.assertIn('test_document', filename)
        self.assertNotIn('.pdf.json', filename)

        self.doc1.label = "test_document"
        self.doc1.save()
        filename = generate_filename(processed_naming_config_with_label, '.json',
                                     self.doc1, 'fallback.json',
                                     is_processed_data_config=True)
        self.assertTrue(filename.endswith('.json'))
        self.assertIn('DOCDATA', filename)
        self.assertIn('test_document', filename)
        self.assertNotIn('.json.json', filename)

        # Test max_length fallback with a NamingConfig object
        long_config_dict = self.packager.source_file_naming_config.copy()
        long_config_dict['max_length'] = 15  # Set a small max_length that will be exceeded
        long_config = NamingConfig(**long_config_dict)
        fallback = 'short.pdf'
        filename = generate_filename(long_config, '.pdf', self.doc1,
                                     fallback, is_processed_data_config=False)
        # When max_length is exceeded, should use fallback
        self.assertEqual(filename, fallback)

        # Test invalid config handling - should raise PydanticValidationError
        invalid_config_dict = {
            'elements': [{'type': 'METADATA', 'value': 'invalid.field'}], 'separator': '-', 'max_length': 100
        }
        with self.assertRaises(PydanticValidationError):
            NamingConfig(**invalid_config_dict)

    def test_to_bytes(self):
        """Test the to_bytes function with string and bytes input."""
        string_input = "test string"
        bytes_output = to_bytes(string_input)
        self.assertEqual(bytes_output, b"test string")

        bytes_input = b"test bytes"
        bytes_output = to_bytes(bytes_input)
        self.assertEqual(bytes_output, b"test bytes")

    def test_generate_filename_with_no_extractions(self):
        """Test filename generation with no extractions"""
        doc_no_extractions, _ = create_document(data_pool=self.dp)
        naming_config_dict = {
            'elements': [{'type': 'STRING_LITERAL', 'value': 'INVOICE'},
                         {'type': 'DOCUMENT_DATA', 'value': 'CustomerName'}], 'separator': '-', 'max_length': 100
        }
        naming_config = NamingConfig(**naming_config_dict)
        filename = generate_filename(naming_config, '.json', doc_no_extractions,
                                     'fallback.json',
                                     is_processed_data_config=True)
        self.assertEqual(filename, 'INVOICE.json')

    def test_generate_filename_with_empty_transformed_data(self):
        """Test filename generation with empty transformed data using real extraction fixture"""
        extraction, _ = create_extraction(document=self.doc1, data_pool=self.dp, training_revision=self.tr)
        with patch.object(extraction, 'get_transformed_results', return_value=[]):
            naming_config_dict = {
                'elements': [{'type': 'STRING_LITERAL', 'value': 'INVOICE'},
                             {'type': 'DOCUMENT_DATA', 'value': 'CustomerName'}], 'separator': '-', 'max_length': 100
            }
            naming_config = NamingConfig(**naming_config_dict)
            filename = generate_filename(naming_config, '.json', self.doc1,
                                         'fallback.json',
                                         is_processed_data_config=True)
            self.assertEqual(filename, 'INVOICE.json')

    def test_generate_random_id(self):
        """Test _generate_random_id function creates IDs with expected properties"""
        random_id = _generate_random_id()
        self.assertEqual(len(random_id), 6)
        self.assertTrue(all(c in string.ascii_letters + string.digits + "-_" for c in random_id))

        random_id = _generate_random_id(size=10)
        self.assertEqual(len(random_id), 10)

        random_id = _generate_random_id(no_invalid_digits=True)
        self.assertEqual(len(random_id), 6)
        self.assertTrue(all(c in string.ascii_letters + string.digits for c in random_id))
        self.assertFalse(any(c in "-_" for c in random_id))

        self.assertNotEqual(_generate_random_id(), _generate_random_id())

    def test_to_bytes_with_other_types(self):
        """Test to_bytes function with None and other non-string/non-bytes inputs"""
        with self.assertRaises(TypeError):
            to_bytes(None)

        with self.assertRaises(TypeError):
            to_bytes(123)

        with self.assertRaises(TypeError):
            to_bytes(["list", "items"])

    def test_generate_filename_with_relationship_id(self):
        """Test filename generation includes document relationship identifier"""
        from packager.models import DocumentRelationshipType, DocumentRelationship
        rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label='DUPLICATE',
            data_pool=self.dp
        )
        relationship = DocumentRelationship.objects.create(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data2,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        filename = generate_filename(
            self.packager.naming_config, '.pdf', self.doc1,
            'fallback.pdf',
            is_processed_data_config=False,
            relationship_id=str(relationship.id)  # Pass relationship ID
        )

        # Should end with the relationship's UUID (not the doc obfuscated_id)
        self.assertTrue(filename.endswith(f'_{relationship.id}.pdf'),
                        f"Filename '{filename}' should end with the relationship UUID '_{relationship.id}.pdf'")

    def test_format_data_with_empty_input(self):
        """Test format_data with empty input for different formats"""
        empty_json_result = format_data({}, PackagerDataOutputFormats.JSON.name)
        self.assertEqual(empty_json_result, '{}')

        empty_list_json_result = format_data([], PackagerDataOutputFormats.JSON.name)
        self.assertEqual(empty_list_json_result, '[]')

        with self.assertRaises(DataFormattingError):
            format_data({}, PackagerDataOutputFormats.CSV.name)

        empty_csv_result = format_data({'json_data': []}, PackagerDataOutputFormats.CSV.name)
        self.assertEqual(empty_csv_result, "")

    def test_format_json_with_datetime(self):
        """Test JSON formatting with datetime objects that need serialization"""
        now = timezone.now()
        data = {'timestamp': now, 'value': 'test'}
        result = format_json(data)

        self.assertIn('"timestamp":', result)
        self.assertIsInstance(json.loads(result)['timestamp'], str)

        yesterday = now - timezone.timedelta(days=1)
        nested_data = {
            'events': [{'timestamp': now, 'type': 'current'}, {'timestamp': yesterday, 'type': 'past'}]
        }

        result = format_json(nested_data)
        parsed = json.loads(result)

        self.assertIsInstance(parsed['events'][0]['timestamp'], str)
        self.assertIsInstance(parsed['events'][1]['timestamp'], str)

    def test_parse_metadata_field_error_handling(self):
        """Test error handling in parse_metadata_field function"""
        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: "
                                    "invalid_format. Expected format: 'model.field'"):
            parse_metadata_field("invalid_format")

        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: .. Expected format: 'model.field'"):
            parse_metadata_field(".")

        with self.assertRaisesRegex(ValueError,
                                    "Invalid metadata field format: "
                                    "model.field.extra. Expected format: 'model.field'"):
            parse_metadata_field("model.field.extra")

    def test_get_single_metadata_value_nonexistent_field(self):
        """Test getting metadata for nonexistent field"""
        with self.assertRaises(ValueError):
            get_single_metadata_value('document.nonexistent_field', self.doc1)

    def test_flatten_dict_with_nested_arrays(self):
        """Test flatten_dict with complex nested structure including arrays"""
        nested_dict = {
            "level1": {
                "level2": {
                    "array": [1, 2, 3], "object_array": [{"key": "value"}, {"key": "value2"}]
                }
            }, "simple": "value"
        }

        flattened = flatten_dict(nested_dict, preserve_arrays=True)

        self.assertEqual(flattened["level1__level2__array"], [1, 2, 3])
        self.assertEqual(flattened["level1__level2__object_array"], [{"key": "value"}, {"key": "value2"}])
        self.assertEqual(flattened["simple"], "value")

    def test_format_data_unsupported_format(self):
        """Test format_data with unsupported format"""
        test_data = {"key": "value"}
        with self.assertRaises(DataFormattingError):
            format_data(test_data, "UNSUPPORTED_FORMAT")

    def test_generate_filename_with_all_element_types(self):
        """Test generate_filename with all possible element types and combinations"""
        extraction, _ = create_extraction(document=self.doc1, data_pool=self.dp, training_revision=self.tr)
        naming_config_dict = {  # Renamed from naming_config to avoid conflict
            'elements': [
                {'type': 'STRING_LITERAL', 'value': 'PREFIX'},
                {'type': 'METADATA', 'value': 'document.obfuscated_id'},  # CHANGED
                {'type': 'DOCUMENT_DATA', 'value': 'CustomerName'},
            ],
            'separator': '_',
            'max_length': 100
        }
        with patch.object(Extraction, 'get_transformed_results',
                          return_value=[{"CustomerName": "Value1", "Field2": "Value2"}]):
            actual_naming_config = NamingConfig(**naming_config_dict)  # Use the dict here
            filename = generate_filename(actual_naming_config, '.pdf', self.doc1, 'fallback.pdf',
                                         is_processed_data_config=True)

            self.assertIn('PREFIX', filename)
            self.assertIn(self.doc1.obfuscated_id, filename)  # This should now pass
            self.assertIn('Value1', filename)

    def test_create_package_zip_with_different_content_types(self):
        """Test that create_package_zip handles different content types correctly."""
        rendered_package_groups = [
            RenderedPackageItem(filename="json_file.json", content='{"key": "value", "nested": {"child": 123}}'),
            RenderedPackageItem(filename="csv_file.csv", content="header1,header2\nvalue1,value2\nvalue3,value4"),
        ]

        response = create_package_zip(rendered_package_groups, filename="mixed_content.zip",
                                      use_profiler_response=True)

        self.assertEqual(response['Content-Type'], 'application/zip')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="mixed_content.zip"')

        content = b''.join(response.streaming_content)
        with zipfile.ZipFile(BytesIO(content), 'r') as zf:
            self.assertEqual(len(zf.namelist()), 2)  # Expecting only 2 files in the zip
            with zf.open("json_file.json") as f:
                self.assertEqual(f.read(), b'{"key": "value", "nested": {"child": 123}}')

            with zf.open("csv_file.csv") as f:
                self.assertEqual(f.read(), b"header1,header2\nvalue1,value2\nvalue3,value4")

    def test_create_package_zip_with_source_files(self):
        """Test that create_package_zip correctly handles the source files structure."""
        import zipfile
        from packager.utils.file_utils import create_package_zip
        from packager.pydantic_classes import RenderedPackageItem, SourceFile

        source_files = [
            SourceFile(filename="source1.pdf", content=b"PDF content"),
            SourceFile(filename="source2.pdf", content=b"Text content to be converted to bytes")
        ]
        rendered_package_groups = [
            RenderedPackageItem(filename="data.json", content='{"key": "value"}', source_files=source_files)
        ]
        response = create_package_zip(rendered_package_groups, filename="with_sources.zip",
                                      use_profiler_response=True)
        content = b''.join(response.streaming_content)
        with zipfile.ZipFile(BytesIO(content), 'r') as zf:
            self.assertEqual(len(zf.namelist()),
                             3)  # Expecting 3 files: data.json, source/source1.pdf, source/source2.pdf
            self.assertIn("data.json", zf.namelist())
            self.assertIn("source/source1.pdf", zf.namelist())
            self.assertIn("source/source2.pdf", zf.namelist())
            with zf.open("data.json") as f:
                self.assertEqual(f.read(), b'{"key": "value"}')
            with zf.open("source/source1.pdf") as f:
                self.assertEqual(f.read(), b"PDF content")
            with zf.open("source/source2.pdf") as f:
                self.assertEqual(f.read(), b"Text content to be converted to bytes")

    def test_to_bytes_conversion(self):
        """Test the to_bytes function with various input types"""
        self.assertEqual(to_bytes("test"), b"test")
        self.assertEqual(to_bytes(""), b"")
        self.assertEqual(to_bytes("áéíóú"), "áéíóú".encode('utf-8'))

        original_bytes = b"already bytes"
        self.assertIs(type(to_bytes(original_bytes)), bytes)
        self.assertEqual(to_bytes(original_bytes), original_bytes)

        with self.assertRaises(TypeError):
            to_bytes(None)

        with self.assertRaises(TypeError):
            to_bytes(123)

        with self.assertRaises(TypeError):
            to_bytes(["list", "items"])

    def test_decode_api_content_for_zip(self):
        """Test the decode_api_content_for_zip function with various input types including CSV files."""
        import base64

        input_items = [
            RenderedPackageItem(filename='test.txt', content='plain text content')
        ]
        decoded_items = decode_api_content_for_zip(input_items)

        self.assertEqual(len(decoded_items), 1)
        self.assertEqual(decoded_items[0].filename, 'test.txt')
        self.assertEqual(decoded_items[0].content, 'plain text content')
        test_bytes = b"binary content"
        encoded_bytes = base64.b64encode(test_bytes).decode('ascii')

        input_with_encoded = [
            RenderedPackageItem(filename='binary.pdf', content={'data': encoded_bytes, 'encoding': 'base64'})
        ]
        decoded_with_encoded = decode_api_content_for_zip(input_with_encoded)

        self.assertEqual(len(decoded_with_encoded), 1)
        self.assertEqual(decoded_with_encoded[0].filename, 'binary.pdf')
        self.assertEqual(decoded_with_encoded[0].content, 'binary content')
        csv_content = '"Field1","Field2"\n"value1","value2"\n"value3","value4"'

        csv_input = [
            RenderedPackageItem(filename='data.csv', content=csv_content)
        ]
        logger.info(f"Input items for CSV test: type={type(csv_input)}, items={len(csv_input)}")
        logger.info(f"Input item details: filename={csv_input[0].filename}, content_type={type(csv_input[0].content)}")

        decoded_csv = decode_api_content_for_zip(csv_input)

        self.assertEqual(len(decoded_csv), 1)
        self.assertEqual(decoded_csv[0].filename, 'data.csv')
        self.assertEqual(decoded_csv[0].content, csv_content)
        source_files = [
            SourceFile(filename='source1.pdf', content=b'pdf content'),
            SourceFile(filename='source2.txt', content=b'text content')
        ]

        input_with_sources = [
            RenderedPackageItem(filename='data.json', content='{"key": "value"}', source_files=source_files)
        ]

        decoded_with_sources = decode_api_content_for_zip(input_with_sources)

        self.assertEqual(len(decoded_with_sources), 1)
        self.assertEqual(decoded_with_sources[0].filename, 'data.json')
        self.assertEqual(decoded_with_sources[0].content, '{"key": "value"}')
        self.assertEqual(len(decoded_with_sources[0].source_files), 2)
        self.assertEqual(decoded_with_sources[0].source_files[0].filename, 'source1.pdf')
        self.assertEqual(decoded_with_sources[0].source_files[0].content, b'pdf content')
        self.assertEqual(decoded_with_sources[0].source_files[1].filename, 'source2.txt')
        self.assertEqual(decoded_with_sources[0].source_files[1].content, b'text content')
        encoded_source_files = [
            SourceFile(filename='source1.pdf',
                       content={'data': base64.b64encode(b'pdf content').decode('ascii'), 'encoding': 'base64'}),
            SourceFile(filename='source2.txt',
                       content={'data': base64.b64encode(b'text content').decode('ascii'), 'encoding': 'base64'})
        ]
        input_with_encoded_sources = [
            RenderedPackageItem(filename='data.json', content='{"key": "value"}',
                                source_files=encoded_source_files)
        ]

        decoded_with_encoded_sources = decode_api_content_for_zip(input_with_encoded_sources)

        self.assertEqual(len(decoded_with_encoded_sources), 1)
        self.assertEqual(decoded_with_encoded_sources[0].filename, 'data.json')
        self.assertEqual(decoded_with_encoded_sources[0].content, '{"key": "value"}')
        self.assertEqual(len(decoded_with_encoded_sources[0].source_files), 2)
        self.assertEqual(decoded_with_encoded_sources[0].source_files[0].filename, 'source1.pdf')
        self.assertEqual(decoded_with_encoded_sources[0].source_files[0].content, b'pdf content')
        self.assertEqual(decoded_with_encoded_sources[0].source_files[1].filename, 'source2.txt')
        self.assertEqual(decoded_with_encoded_sources[0].source_files[1].content, b'text content')
        binary_string_input = [
            RenderedPackageItem(filename='binary_string.bin', content='binary content')
        ]
        decoded_binary_string = decode_api_content_for_zip(binary_string_input)

        self.assertEqual(len(decoded_binary_string), 1)
        self.assertEqual(decoded_binary_string[0].filename, 'binary_string.bin')
        # Based on the behavior with base64 encoded content, expecting string instead of bytes
        self.assertEqual(decoded_binary_string[0].content, 'binary content')
