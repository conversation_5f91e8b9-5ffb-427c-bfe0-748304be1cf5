"""
Tests to verify the performance optimizations are working correctly.
"""
import logging
from unittest.mock import patch

from django.test import TransactionTestCase
from django.utils import timezone
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from documents.tests.util import create_document
from extract.tests.util import create_extraction_batch
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, PackageEntry, ExtractionBatchData
from packager.tasks import prepare_packages, process_documents, process_extraction_batches
from packager.tests.util import create_packager
from training.tests.util import create_training_ready_training_set, create_training_revision

logger = logging.getLogger(__name__)


class PerformanceOptimizationTestCase(TransactionTestCase, TestCaseMixin):
    """Test case to verify performance optimizations work correctly."""

    def setUp(self):
        """Set up test data."""
        # Helper functions return (object, path) tuples
        self.organization, _ = create_organization()
        self.data_pool, _ = create_data_pool(self.organization)
        self.training_set, _ = create_training_ready_training_set(self.data_pool)
        self.training_revision, _ = create_training_revision(self.data_pool, self.training_set)

    def test_bulk_operations_in_process_documents(self):
        """Test that process_documents uses bulk operations correctly."""
        # Create a packager and package
        packager = create_packager(self.data_pool)
        package, _ = packager.get_working_package()

        documents = []
        for i in range(5):
            doc, _ = create_document(self.data_pool, label=f"test_doc_{i}")
            documents.append(doc)

        # Mock bulk operations to verify they're called
        with patch.object(DocumentData.objects, 'bulk_create') as mock_bulk_create, \
                patch.object(PackageEntry.objects, 'bulk_create') as mock_pe_bulk_create:
            result = process_documents(documents, package, self.data_pool)

            self.assertTrue(result)

            # Verify bulk operations were called
            mock_bulk_create.assert_called_once()
            mock_pe_bulk_create.assert_called_once()

            created_doc_data = mock_bulk_create.call_args[0][0]
            created_package_entries = mock_pe_bulk_create.call_args[0][0]

            self.assertEqual(len(created_doc_data), 5)
            self.assertEqual(len(created_package_entries), 5)

    def test_bulk_operations_in_process_extraction_batches(self):
        """Test that process_extraction_batches uses bulk operations correctly."""
        packager = create_packager(self.data_pool)
        package, _ = packager.get_working_package()

        extraction_batch, _ = create_extraction_batch(self.data_pool, training_revision=self.training_revision)

        documents = []
        for i in range(3):
            doc, _ = create_document(self.data_pool, label=f"batch_doc_{i}")
            documents.append(doc)
            extraction_batch.documents.add(doc)

        with patch.object(ExtractionBatchData.objects, 'bulk_create') as mock_ebd_bulk_create, \
                patch.object(DocumentData.objects, 'bulk_create') as mock_dd_bulk_create, \
                patch.object(PackageEntry.objects, 'bulk_create') as mock_pe_bulk_create:
            result = process_extraction_batches([extraction_batch], package, self.data_pool)

            self.assertTrue(result)

            # Verify bulk operations were called
            mock_ebd_bulk_create.assert_called_once()
            mock_dd_bulk_create.assert_called_once()
            mock_pe_bulk_create.assert_called_once()

    def test_prepare_packages_uses_bulk_operations(self):
        """Test that prepare_packages uses bulk operations for PackageEntry creation."""
        packager = create_packager(
            self.data_pool,
            document_status_filter=['VERIFIED'],
            packaging_begin_date=timezone.now().date(),
            packaging_end_date=timezone.now().date()
        )

        documents = []
        for i in range(3):
            doc, _ = create_document(self.data_pool, label=f"filtered_doc_{i}", status='VERIFIED')
            DocumentData.objects.create(
                document=doc,
                data_pool=self.data_pool,
                source_file_id=doc.obfuscated_id,
                source_file_name=doc.label,
                status='VERIFIED'
            )
            documents.append(doc)

        with patch.object(PackageEntry.objects, 'bulk_create') as mock_bulk_create, \
                patch.object(PackageEntry.objects, 'bulk_update') as mock_bulk_update:
            result = prepare_packages(packager.id)

            self.assertTrue(result)

            self.assertTrue(mock_bulk_create.called or mock_bulk_update.called)

    def test_no_redundant_duplicate_checks_in_prepare_packages(self):
        """Test that prepare_packages no longer performs synchronous duplicate checks."""
        packager = create_packager(
            self.data_pool,
            document_status_filter=['VERIFIED'],
            packaging_begin_date=timezone.now().date(),
            packaging_end_date=timezone.now().date()
        )

        doc, _ = create_document(self.data_pool, label="test_doc", status='VERIFIED')
        DocumentData.objects.create(
            document=doc,
            data_pool=self.data_pool,
            source_file_id=doc.obfuscated_id,
            source_file_name=doc.label,
            status='VERIFIED',
            is_md5_check_completed=False,
            is_label_check_completed=False,
            is_content_check_completed=False
        )

        with patch('packager.tasks.DuplicateDetectionService') as mock_service:
            result = prepare_packages(packager.id)

            self.assertTrue(result)

            mock_service.assert_not_called()

    def test_optimized_filtering_with_select_related(self):
        """Test that the optimized filtering includes proper select_related."""
        packager = create_packager(
            self.data_pool,
            document_status_filter=['VERIFIED'],
            packaging_begin_date=timezone.now().date(),
            packaging_end_date=timezone.now().date()
        )

        doc, _ = create_document(self.data_pool, label="test_doc", status='VERIFIED')
        DocumentData.objects.create(
            document=doc,
            data_pool=self.data_pool,
            source_file_id=doc.obfuscated_id,
            source_file_name=doc.label,
            status='VERIFIED'
        )

        filtered_data = packager.get_filtered_document_data_additive()

        self.assertIn('document', filtered_data.query.select_related)
        self.assertIn('canonical_document_data', filtered_data.query.select_related)

        self.assertEqual(filtered_data.count(), 1)

        doc_data = filtered_data.first()
        self.assertEqual(doc_data.document.label, "test_doc")
