"""
Optimized base test classes for packager tests.

This module provides optimized base classes that reduce database queries,
improve test execution time, and provide consistent patterns for packager tests.
"""
import os
import logging
from unittest.mock import patch

from django.test import TestCase, TransactionTestCase
from django.db.models.signals import post_save
from django.conf import settings
import rest_framework

from glynt_api.tests.util import TestCaseMixin
from packager.signals import (
    handle_document_creation_for_coc,
    package_created_document_for_stream_schedule,
    document_created_coc_event,
    package_created_coc_event
)

logger = logging.getLogger(__name__)


class OptimizedPackagerTestMixin:
    """
    Base mixin providing optimized patterns for packager tests.
    
    Features:
    - Centralized signal management
    - Conditional filesystem setup
    - Shared test data patterns
    - Performance optimizations
    """
    
    # Class-level flags to control behavior
    needs_filesystem = False
    needs_signals_disconnected = True
    
    @classmethod
    def setUpClass(cls):
        """Set up class-level optimizations."""
        super().setUpClass()
        
        # Disconnect signals that can interfere with tests
        if cls.needs_signals_disconnected:
            cls._disconnect_packager_signals()
    
    @classmethod
    def tearDownClass(cls):
        """Clean up class-level optimizations."""
        # Reconnect signals
        if cls.needs_signals_disconnected:
            cls._reconnect_packager_signals()
        
        super().tearDownClass()
    
    @classmethod
    def _disconnect_packager_signals(cls):
        """Disconnect packager-related signals that can interfere with tests."""
        try:
            post_save.disconnect(handle_document_creation_for_coc, sender='documents.Document')
            post_save.disconnect(package_created_document_for_stream_schedule, sender='documents.Document')
            post_save.disconnect(document_created_coc_event, sender='documents.Document')
            post_save.disconnect(package_created_coc_event, sender='packager.Package')
            logger.debug("Disconnected packager signals for test class")
        except Exception as e:
            logger.warning(f"Error disconnecting signals: {e}")
    
    @classmethod
    def _reconnect_packager_signals(cls):
        """Reconnect packager-related signals after tests."""
        try:
            post_save.connect(handle_document_creation_for_coc, sender='documents.Document')
            post_save.connect(package_created_document_for_stream_schedule, sender='documents.Document')
            post_save.connect(document_created_coc_event, sender='documents.Document')
            post_save.connect(package_created_coc_event, sender='packager.Package')
            logger.debug("Reconnected packager signals after test class")
        except Exception as e:
            logger.warning(f"Error reconnecting signals: {e}")
    
    def setUp(self):
        """Set up individual test optimizations."""
        super().setUp()
        
        # Conditional filesystem setup
        if self.needs_filesystem:
            self._setup_filesystem()
    
    def _setup_filesystem(self):
        """Set up filesystem for tests that need it."""
        if hasattr(self, 'setUpPyfakefs'):
            self.setUpPyfakefs()

            # Add required real directories safely (check if they already exist)
            try:
                drf_template_dir = os.path.join(os.path.dirname(rest_framework.__file__), 'templates')
                if os.path.exists(drf_template_dir) and not self.fs.exists(drf_template_dir):
                    self.fs.add_real_directory(drf_template_dir)
            except (OSError, AttributeError):
                pass  # Directory might already exist or not be available

            try:
                import django
                django_views_dir = os.path.join(os.path.dirname(django.__file__), 'views')
                if os.path.exists(django_views_dir) and not self.fs.exists(django_views_dir):
                    self.fs.add_real_directory(django_views_dir, read_only=False)
            except (OSError, AttributeError):
                pass  # Directory might already exist or not be available

            # Add template directories from settings safely
            try:
                for template_engine in settings.TEMPLATES:
                    for d in template_engine.get('DIRS', []):
                        if os.path.exists(d) and not self.fs.exists(d):
                            self.fs.add_real_directory(d)
            except (OSError, AttributeError):
                pass  # Directories might already exist or not be available
    
    def setUpTestData(self):
        """
        Override this method to set up shared test data.
        
        This method should be used instead of setUp() for creating
        test data that can be shared across test methods.
        """
        pass


class FastPackagerTestCase(OptimizedPackagerTestMixin, TestCase):
    """
    Optimized test case for fast packager tests.
    
    Use this for:
    - Tests that don't need database transactions
    - Tests that don't need filesystem operations
    - Simple unit tests
    - Validation tests
    """
    
    needs_filesystem = False
    needs_signals_disconnected = True
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Set up shared test data once per class
        cls.setUpTestData()
    
    @classmethod
    def setUpTestData(cls):
        """Override in subclasses to set up shared test data."""
        pass


class TransactionPackagerTestCase(OptimizedPackagerTestMixin, TransactionTestCase, TestCaseMixin):
    """
    Optimized test case for packager tests requiring database transactions.
    
    Use this for:
    - Tests that need database transactions
    - Tests that need filesystem operations
    - Integration tests
    - Tests with complex database operations
    """
    
    needs_filesystem = True
    needs_signals_disconnected = True
    
    def setUp(self):
        super().setUp()
        # Set up test data for each test method
        self.setUpTestData()


class BulkDataPackagerTestCase(FastPackagerTestCase):
    """
    Optimized test case for tests with bulk data operations.
    
    Use this for:
    - Tests that create many objects
    - Performance tests
    - Tests with large datasets
    - Bulk operation tests
    """
    
    needs_filesystem = False
    needs_signals_disconnected = True
    
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Additional setup for bulk operations
        cls._setup_bulk_data()
    
    @classmethod
    def _setup_bulk_data(cls):
        """Set up bulk test data efficiently."""
        # Override in subclasses for bulk data setup
        pass


class SignalTestCase(TransactionPackagerTestCase):
    """
    Specialized test case for testing signal behavior.
    
    Use this for:
    - Tests that need to verify signal behavior
    - Tests that need signals connected
    - Signal integration tests
    """
    
    needs_signals_disconnected = False  # Keep signals connected for signal tests
    
    def setUp(self):
        super().setUp()
        # Ensure signals are connected for signal tests
        self._reconnect_packager_signals()


# Backward compatibility aliases
BasePackagerTest = TransactionPackagerTestCase  # For existing tests that use this name
