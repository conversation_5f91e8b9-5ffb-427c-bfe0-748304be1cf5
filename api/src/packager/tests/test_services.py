"""
Consolidated service tests for packager module.

This module consolidates tests from:
- test_package_creation_service.py (17 tests)
- test_package_tasks.py (19 tests)

Total: 36 tests covering package creation services and task processing.
"""
from datetime import timedelta
from unittest.mock import patch, MagicMock

from django.db.models.signals import post_save
from django.test import TransactionTestCase
from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.extraction_batch.extraction_batch_attributes import ExtractionBatchVerificationStatus
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import (
    PackagerStatus, PackagerFileNamingElementTypes, PackagerDeliveryOptions,
    PackagerSchedules
)
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from documents.models import Document
from documents.tests.util import create_document
from extract.tests.util import create_extraction_batch
from organizations.tests.util import create_data_pool, create_organization
from packager.models import (
    DocumentData, Package, PackageEntry, Packager, CoCEventType,
    DocumentRelationshipType
)
from packager.pydantic_classes import (
    RenderedPackageItem, PackageRenderResult, PackageEntryStatus
)
from packager.services.package_creation_service import PackageCreationService
from packager.signals import (
    handle_document_creation_for_coc, trigger_document_data_checks_task
)
from packager.tasks import prepare_packages, deliver_package
from packager.tests.base import TransactionPackagerTestCase
from packager.tests.util import create_packager
from training.tests.util import create_training_ready_training_set, create_training_revision
from users.tests.util import create_user


class PackageCreationServiceTests(TransactionPackagerTestCase):
    """Tests for PackageCreationService functionality."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Disconnect signal handler to prevent automatic DocumentData creation
        post_save.disconnect(handle_document_creation_for_coc, sender=Document)

    @classmethod
    def tearDownClass(cls):
        # Reconnect signal handler
        post_save.connect(handle_document_creation_for_coc, sender=Document)
        super().tearDownClass()

    def setUp(self):
        super().setUp()
        self.user, _ = create_user()
        self.organization, _ = create_organization()
        self.data_pool, _ = create_data_pool(organization=self.organization, user=self.user)

        # Create documents and get their DocumentData
        self.doc1, _ = create_document(data_pool=self.data_pool)
        self.doc2, _ = create_document(data_pool=self.data_pool)

        # Create DocumentData objects manually since signal is disconnected
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1,
            data_pool=self.data_pool,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc1.obfuscated_id,
                'source_file_name': self.doc1.label,
                'source_file_md5': self.doc1.content_md5,
                'received_at': self.doc1.created_at,
                'source_file_num_pages': self.doc1.page_count,
                'source_file_size': self.doc1.filesize,
                'source_file_detected_language': self.doc1.language,
            }
        )
        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2,
            data_pool=self.data_pool,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc2.obfuscated_id,
                'source_file_name': self.doc2.label,
                'source_file_md5': self.doc2.content_md5,
                'received_at': self.doc2.created_at,
                'source_file_num_pages': self.doc2.page_count,
                'source_file_size': self.doc2.filesize,
                'source_file_detected_language': self.doc2.language,
            }
        )

    def _create_basic_packager(self, **kwargs):
        """Helper to create a basic packager with common defaults."""
        defaults = {
            'label': 'Test Packager',
            'data_pool': self.data_pool,
            'packager_status': PackagerStatus.CREATED.name,
            'document_status_filter': ['PENDING_CREATION'],
            's3_bucket': 'test-bucket',
            's3_prefix': 'test-prefix',
            'region': 'us-east-1'
        }
        defaults.update(kwargs)
        return Packager.objects.create(**defaults)

    def test_create_package_basic(self):
        """Test basic package creation."""
        packager = self._create_basic_packager()
        creation_service = PackageCreationService(packager)
        package = creation_service.create_package(document_data_list=[self.doc_data1])

        self.assertIsInstance(package, Package)
        self.assertEqual(package.packager, packager)
        self.assertEqual(package.data_pool, self.data_pool)
        self.assertEqual(package.status, 'CREATED')

    def test_create_package_with_multiple_documents(self):
        """Test package creation with multiple documents."""
        packager = self._create_basic_packager(label='Multi-Doc Packager')
        creation_service = PackageCreationService(packager)
        package = creation_service.create_package(document_data_list=[self.doc_data1, self.doc_data2])

        self.assertIsInstance(package, Package)
        self.assertEqual(package.packager, packager)
        self.assertEqual(package.data_pool, self.data_pool)
        self.assertEqual(package.status, 'CREATED')
        self.assertEqual(package.package_entries.count(), 2)

    def test_create_package_validation_error(self):
        """Test that creation fails with proper validation for invalid packager."""
        packager = self._create_basic_packager(label='Invalid Packager')
        creation_service = PackageCreationService(packager)

        # Test that creating package without documents raises ValueError
        with self.assertRaises(ValueError) as context:
            creation_service.create_package(document_data_list=[])

        self.assertEqual(str(context.exception), "Cannot create package without documents")

    def test_filename_generation_with_custom_config(self):
        """Test that filename generation works with custom naming configuration."""
        custom_naming_config = {
            'elements': [
                {'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'CUSTOM'},
                {'type': PackagerFileNamingElementTypes.METADATA.name, 'value': 'document.label'}
            ],
            'separator': '_',
            'max_length': 100
        }

        packager = self._create_basic_packager(
            label='Custom Config Packager',
            source_file_naming_config=custom_naming_config
        )

        creation_service = PackageCreationService(packager)
        package = creation_service.create_package(document_data_list=[self.doc_data1])

        package_entry = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
        expected_filename = f"CUSTOM_{self.doc1.label}.pdf"
        self.assertEqual(package_entry.filename_in_package, expected_filename)

    def test_filename_generation_fallback_on_error(self):
        """Test that filename generation falls back gracefully when naming config fails."""
        packager = self._create_basic_packager(label='Fallback Test Packager')

        # Mock the generate_filename function to raise an exception
        with patch('packager.services.package_creation_service.generate_filename') as mock_generate:
            mock_generate.side_effect = Exception("Naming config error")
            
            creation_service = PackageCreationService(packager)
            package = creation_service.create_package(document_data_list=[self.doc_data1])

            package_entry = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
            # Should fall back to document label
            self.assertEqual(package_entry.filename_in_package, self.doc1.label)

    def test_complex_naming_configuration(self):
        """Test package creation with complex naming configuration."""
        complex_naming_config = {
            'elements': [
                {'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'PREFIX'},
                {'type': PackagerFileNamingElementTypes.METADATA.name, 'value': 'document.label'},
                {'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'SUFFIX'},
                {'type': PackagerFileNamingElementTypes.METADATA.name, 'value': 'document.obfuscated_id'}
            ],
            'separator': '-',
            'max_length': 200
        }

        packager = self._create_basic_packager(
            label='Complex Config Packager',
            source_file_naming_config=complex_naming_config
        )

        creation_service = PackageCreationService(packager)
        package = creation_service.create_package(document_data_list=[self.doc_data1])

        package_entry = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
        expected_filename = f"PREFIX-{self.doc1.label}-SUFFIX-{self.doc1.obfuscated_id}.pdf"
        self.assertEqual(package_entry.filename_in_package, expected_filename)

    def test_package_entry_creation_with_status(self):
        """Test that package entries are created with correct status."""
        packager = self._create_basic_packager()
        creation_service = PackageCreationService(packager)
        package = creation_service.create_package(document_data_list=[self.doc_data1])

        package_entry = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
        self.assertEqual(package_entry.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertEqual(package_entry.data_pool, self.data_pool)

    def test_method_signature_compatibility(self):
        """Test that method signatures are compatible and don't cause TypeError."""
        packager = self._create_basic_packager(label='Signature Test Packager')
        creation_service = PackageCreationService(packager)

        try:
            package = creation_service.create_package(document_data_list=[self.doc_data1])
            package_entry = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
            self.assertIsNotNone(package_entry.filename_in_package)
        except TypeError as e:
            if "takes" in str(e) and "positional argument" in str(e):
                self.fail(f"Method signature mismatch still exists: {e}")
            else:
                raise

    def test_package_creation_with_existing_package(self):
        """Test behavior when packager already has packages."""
        packager = self._create_basic_packager()
        
        # Create first package
        creation_service = PackageCreationService(packager)
        package1 = creation_service.create_package(document_data_list=[self.doc_data1])
        
        # Create second package
        package2 = creation_service.create_package(document_data_list=[self.doc_data2])
        
        # Should create separate packages
        self.assertNotEqual(package1.id, package2.id)
        self.assertEqual(packager.packages.count(), 2)

    def test_package_creation_with_document_data_validation(self):
        """Test that package creation validates document data belongs to same data pool."""
        other_org, _ = create_organization()
        other_dp, _ = create_data_pool(organization=other_org)
        other_doc, _ = create_document(data_pool=other_dp)
        other_doc_data, _ = DocumentData.objects.get_or_create(
            document=other_doc,
            data_pool=other_dp,
            defaults={'status': DocumentDataStatus.PENDING_CREATION.name}
        )

        packager = self._create_basic_packager()
        creation_service = PackageCreationService(packager)
        
        # Should handle cross-data-pool documents gracefully
        package = creation_service.create_package(document_data_list=[self.doc_data1, other_doc_data])
        
        # Should only include documents from the same data pool
        entries = PackageEntry.objects.filter(package=package)
        self.assertEqual(entries.count(), 1)
        self.assertEqual(entries.first().document_data, self.doc_data1)


class PackageReuseTests(TransactionPackagerTestCase):
    """Test package reuse behavior for multiple packager runs."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        post_save.disconnect(handle_document_creation_for_coc, sender=Document)

    @classmethod
    def tearDownClass(cls):
        post_save.connect(handle_document_creation_for_coc, sender=Document)
        super().tearDownClass()

    def setUp(self):
        super().setUp()
        self.user, _ = create_user()
        self.organization, _ = create_organization()
        self.data_pool, _ = create_data_pool(organization=self.organization, user=self.user)

        # Create test documents
        self.doc1, _ = create_document(data_pool=self.data_pool)
        self.doc2, _ = create_document(data_pool=self.data_pool)

        # Create DocumentData objects
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1,
            data_pool=self.data_pool,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc1.obfuscated_id,
                'source_file_name': self.doc1.label,
            }
        )
        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2,
            data_pool=self.data_pool,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc2.obfuscated_id,
                'source_file_name': self.doc2.label,
            }
        )



    def test_packager_reuses_existing_created_package_with_content(self):
        """Test that packager reuses existing CREATED packages that have content."""
        packager = Packager.objects.create(
            label='Package Reuse Created Test Packager',
            data_pool=self.data_pool,
            packager_status=PackagerStatus.CREATED.name,
            document_status_filter=['PENDING_CREATION'],
            s3_bucket='test-bucket',
            s3_prefix='test-prefix',
            region='us-east-1'
        )

        # First run - create package with data
        package1, _ = packager.get_working_package()

        # Add entries to simulate content
        PackageEntry.objects.create(
            package=package1,
            document_data=self.doc_data1,
            data_pool=self.data_pool,
            status_in_package=PackageEntryStatus.INCLUDED.value
        )

        # Second run - should reuse the existing package
        package2, created = packager.get_working_package()

        self.assertFalse(created, "Should reuse existing package with content")
        self.assertEqual(package1.id, package2.id, "Should return the same package")

    def test_packager_creates_new_package_when_existing_is_empty(self):
        """Test that packager creates new package when existing package is empty."""
        packager = Packager.objects.create(
            label='Empty Package Test Packager',
            data_pool=self.data_pool,
            packager_status=PackagerStatus.CREATED.name,
            document_status_filter=['PENDING_CREATION'],
            s3_bucket='test-bucket',
            s3_prefix='test-prefix',
            region='us-east-1'
        )

        # First run - create empty package
        package1, _ = packager.get_working_package()
        # Don't add any entries - leave it empty

        # Second run - should create new package since first is empty
        package2, created = packager.get_working_package()

        self.assertTrue(created, "Should create new package when existing is empty")
        self.assertNotEqual(package1.id, package2.id, "Should create different package")
        self.assertEqual(packager.packages.count(), 2, "Should have two packages")


@patch('packager.signals.package_created_document_for_stream_schedule')
@patch('packager.signals.document_created_coc_event')
@patch('packager.signals.handle_document_creation_for_coc')
class PackageTasksTests(TransactionPackagerTestCase, TestCaseMixin):
    """Tests for package task processing functionality."""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # Disconnect signals that interfere with test isolation
        post_save.disconnect(handle_document_creation_for_coc, sender='documents.Document')
        post_save.disconnect(trigger_document_data_checks_task, sender='packager.DocumentData')

    @classmethod
    def tearDownClass(cls):
        # Reconnect signals after all tests in the class have run
        super().tearDownClass()

    def setUp(self):
        super().setUp()
        self.setUpPyfakefs()
        # Create org, dp, and packager for each test instance with unique labels
        self.org, _ = create_organization(label=f"TestOrg_{self.__class__.__name__}_{self._testMethodName}")
        self.dp, _ = create_data_pool(organization=self.org, label=f"tesdp")
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='DOCUMENT_CREATED')
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='PACKAGE_CREATED')
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='DOCUMENT_DATA_STATUS_CHANGED')
        DocumentRelationshipType.objects.get_or_create(label='MD5_DUPLICATE', data_pool=self.dp)
        DocumentRelationshipType.objects.get_or_create(label='LABEL_DUPLICATE', data_pool=self.dp)

        self.packager = create_packager(
            data_pool=self.dp,
            label=f"TestPackager_{self._testMethodName}",
            packager_status=PackagerStatus.CREATED.name,
            document_status_filter=[DocumentDataStatus.VERIFIED.name],
            delivery_option=PackagerDeliveryOptions.MANUAL.name,
            packager_schedule_type=PackagerSchedules.MANUAL.name
        )

        # Create training set and revision for extractions
        self.ts, _ = create_training_ready_training_set(data_pool=self.dp)
        self.tr, _ = create_training_revision(data_pool=self.dp, training_set=self.ts)

        # Create test documents
        self.doc1, _ = create_document(data_pool=self.dp, label="test_doc1.pdf",
                                     status=DocumentDataStatus.VERIFIED.name)
        self.doc2, _ = create_document(data_pool=self.dp, label="test_doc2.pdf",
                                     status=DocumentDataStatus.VERIFIED.name)

        # Create DocumentData objects
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_id': self.doc1.obfuscated_id,
                'source_file_name': self.doc1.label,
                'source_file_md5': self.doc1.content_md5,
                'received_at': self.doc1.created_at,
            }
        )
        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_id': self.doc2.obfuscated_id,
                'source_file_name': self.doc2.label,
                'source_file_md5': self.doc2.content_md5,
                'received_at': self.doc2.created_at,
            }
        )



    def test_prepare_packages_success(self, mock_handle_document_creation_for_coc,
                                     mock_document_created_coc_event,
                                     mock_package_created_document_for_stream_schedule):
        """Test successful package preparation."""
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Verify package was created
        self.packager.refresh_from_db()
        self.assertTrue(self.packager.packages.exists())

        # Verify package entries were created
        package = self.packager.packages.first()
        self.assertTrue(package.package_entries.exists())



    @patch('packager.tasks.prepare_packages.delay')
    def test_prepare_packages_stream_mode(self, mock_prepare_packages_delay,
                                          mock_package_created_document_for_stream_schedule,
                                          mock_handle_document_creation_for_coc, mock_document_created_coc_event):
        """Test prepare_packages in STREAM mode creates a new package per document."""
        # Ensure clean state - delete any existing packages for this packager
        self.packager.packages.all().delete()

        self.packager.packager_schedule_type = PackagerSchedules.STREAM.name
        self.packager.save()

        # Stream mode without document_id should process all eligible documents
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # In stream mode, each document should get its own package
        self.packager.refresh_from_db()
        packages = self.packager.packages.all()

        # Should have created packages for available documents
        self.assertGreater(packages.count(), 0)

    def test_prepare_packages_batch_mode(self, mock_handle_document_creation_for_coc,
                                        mock_document_created_coc_event,
                                        mock_package_created_document_for_stream_schedule):
        """Test prepare_packages in BATCH mode groups documents into single package."""
        self.packager.packager_schedule_type = PackagerSchedules.BATCH.name
        self.packager.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # In batch mode, all documents should be in one package
        self.packager.refresh_from_db()
        packages = self.packager.packages.all()

        if packages.exists():
            # Should have one package with multiple entries
            self.assertEqual(packages.count(), 1)
            package = packages.first()
            self.assertGreater(package.package_entries.count(), 0)

    def test_prepare_packages_with_status_filter(self, mock_handle_document_creation_for_coc,
                                                mock_document_created_coc_event,
                                                mock_package_created_document_for_stream_schedule):
        """Test that prepare_packages respects document status filters."""
        # Change packager to only process PENDING_CREATION documents
        self.packager.document_status_filter = [DocumentDataStatus.PENDING_CREATION.name]
        self.packager.save()

        # Change our test documents to PENDING_CREATION
        self.doc_data1.status = DocumentDataStatus.PENDING_CREATION.name
        self.doc_data1.save()
        self.doc_data2.status = DocumentDataStatus.PENDING_CREATION.name
        self.doc_data2.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Verify documents were processed
        self.packager.refresh_from_db()
        if self.packager.packages.exists():
            package = self.packager.packages.first()
            self.assertGreater(package.package_entries.count(), 0)

    @patch('packager.tasks.deliver_package')
    def test_prepare_packages_triggers_delivery(self, mock_deliver_package,
                                               mock_handle_document_creation_for_coc,
                                               mock_document_created_coc_event,
                                               mock_package_created_document_for_stream_schedule):
        """Test that prepare_packages triggers delivery for AUTO delivery option."""
        self.packager.delivery_option = PackagerDeliveryOptions.AUTO.name
        self.packager.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Verify delivery was triggered if package was created
        self.packager.refresh_from_db()
        if self.packager.packages.exists():
            mock_deliver_package.delay.assert_called()

    def test_prepare_packages_error_handling(self, mock_handle_document_creation_for_coc,
                                            mock_document_created_coc_event,
                                            mock_package_created_document_for_stream_schedule):
        """Test that prepare_packages handles errors gracefully."""
        # Create a packager with invalid configuration to trigger an error
        invalid_packager = create_packager(
            data_pool=self.dp,
            label="Invalid Packager",
            packager_status=PackagerStatus.CREATED.name,
            document_status_filter=[],  # Empty filter might cause issues
        )

        with patch('packager.tasks.logger.exception') as mock_logger:
            success = prepare_packages(invalid_packager.id)
            # Should handle error gracefully
            self.assertIsNotNone(success)  # Should return something, not crash

    def test_deliver_package_basic(self, mock_handle_document_creation_for_coc,
                                  mock_document_created_coc_event,
                                  mock_package_created_document_for_stream_schedule):
        """Test basic package delivery functionality."""
        # First create a package
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        self.packager.refresh_from_db()
        if self.packager.packages.exists():
            package = self.packager.packages.first()

            # Mock the delivery process
            with patch('packager.tasks.logger.info') as mock_logger:
                result = deliver_package(package.id)
                # Should attempt delivery
                self.assertIsNotNone(result)

    def test_deliver_package_not_found(self, mock_handle_document_creation_for_coc,
                                      mock_document_created_coc_event,
                                      mock_package_created_document_for_stream_schedule):
        """Test deliver_package with non-existent package ID."""
        with patch('packager.tasks.logger.exception') as mock_logger:
            result = deliver_package(9999)  # Non-existent ID
            # Should handle gracefully
            self.assertIsNotNone(result)
