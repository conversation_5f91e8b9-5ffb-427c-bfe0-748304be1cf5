from unittest.mock import patch, MagicMock

from django.db.models.signals import post_save
from django.test import TransactionTestCase
from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.extraction_batch.extraction_batch_attributes import ExtractionBatchVerificationStatus
from glynt_schemas.packager.package_attributes import PackageStatus
from glynt_schemas.packager.packager_attributes import (PackagerDeliveryOptions,
                                                        PackagerSchedules, PackagerStatus)
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from documents.tests.util import create_document
from extract.tests.util import create_extraction_batch
from organizations.tests.util import create_data_pool, create_organization
from packager.models import (CoCEventType, PackageEntry, Package, DocumentRelationship, DocumentRelationshipType,
                             DocumentData)
from packager.pydantic_classes import RenderedPackageItem, PackageRenderResult, PackageEntryStatus
from packager.signals import handle_document_creation_for_coc
from packager.tasks import prepare_packages, deliver_package
from packager.tests.util import create_packager
from training.tests.util import create_training_ready_training_set, create_training_revision


@patch('packager.signals.document_created_coc_event')
@patch('packager.signals.handle_document_creation_for_coc')
class PreparePackagesTaskTests(TransactionTestCase, TestCaseMixin):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        post_save.disconnect(handle_document_creation_for_coc, sender='documents.Document')

    @classmethod
    def tearDownClass(cls):
        # Reconnect signals if they were disconnected for the whole class
        post_save.connect(handle_document_creation_for_coc, sender='documents.Document')
        super().tearDownClass()

    @classmethod
    def setUpTestData(cls):
        pass

    def setUp(self):
        self.setUpPyfakefs()
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create required CoC event types
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='DOCUMENT_CREATED')
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='PACKAGE_CREATED')
        CoCEventType.objects.get_or_create(data_pool=self.dp, label='DOCUMENT_DATA_STATUS_CHANGED')

        # Create required DocumentRelationshipType records for duplicate detection
        self.md5_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label='MD5_DUPLICATE',
            data_pool=self.dp,
            defaults={'description': 'Documents with identical MD5 hashes'}
        )
        self.label_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label='LABEL_DUPLICATE',
            data_pool=self.dp,
            defaults={'description': 'Documents with identical labels'}
        )
        self.content_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label='CONTENT_DUPLICATE',
            data_pool=self.dp,
            defaults={'description': 'Documents with identical content'}
        )

        self.packager = create_packager(data_pool=self.dp,
                                        document_status_filter=[DocumentDataStatus.PENDING_CREATION.name,
                                                                DocumentDataStatus.VERIFIED.name],
                                        packager_schedule_type=PackagerSchedules.MANUAL.name,
                                        exclude_duplicates_from_delivery=True)

        # Create documents and document data for general tests with unique MD5 hashes
        self.doc1, _ = create_document(data_pool=self.dp,
                                       status=DocumentDataStatus.PENDING_CREATION.name,
                                       label="doc1.pdf",
                                       content_md5="unique_md5_doc1")
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc1.obfuscated_id,
                'source_file_name': self.doc1.label,
                'source_file_md5': self.doc1.content_md5,
                'received_at': self.doc1.created_at
            })
        self.doc_data1.status = DocumentDataStatus.PENDING_CREATION.name
        self.doc_data1.save()

        self.doc2, _ = create_document(data_pool=self.dp,
                                       status=DocumentDataStatus.PENDING_CREATION.name,
                                       label="doc2.pdf",
                                       content_md5="unique_md5_doc2")
        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': self.doc2.obfuscated_id,
                'source_file_name': self.doc2.label,
                'source_file_md5': self.doc2.content_md5,
                'received_at': self.doc2.created_at
            })
        self.doc_data2.status = DocumentDataStatus.PENDING_CREATION.name
        self.doc_data2.save()

        # Create documents for duplicate testing with proper timestamps to ensure deterministic behavior
        base_time = timezone.now()

        self.original_doc, _ = create_document(
            data_pool=self.dp,
            status=DocumentDataStatus.VERIFIED.name,
            label="OriginalDoc",
            content_md5="original_md5"
        )
        # Set created_at to ensure it's the oldest
        self.original_doc.created_at = base_time
        self.original_doc.save()

        self.original_doc_data, _ = DocumentData.objects.get_or_create(
            document=self.original_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': self.original_doc.content_md5,
                'source_file_name': self.original_doc.label,
                'source_file_id': self.original_doc.obfuscated_id,
                'received_at': base_time
            })
        self.original_doc_data.status = DocumentDataStatus.VERIFIED.name
        self.original_doc_data.received_at = base_time
        self.original_doc_data.save()

        # MD5 duplicate (newer timestamp)
        self.md5_duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="MD5DuplicateDoc",
            content_md5="original_md5",
            status=DocumentDataStatus.VERIFIED.name
        )
        self.md5_duplicate_doc.created_at = base_time + timezone.timedelta(minutes=1)
        self.md5_duplicate_doc.save()

        self.md5_duplicate_doc_data, _ = DocumentData.objects.get_or_create(
            document=self.md5_duplicate_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': self.md5_duplicate_doc.content_md5,
                'source_file_name': self.md5_duplicate_doc.label,
                'source_file_id': self.md5_duplicate_doc.obfuscated_id,
                'received_at': base_time + timezone.timedelta(minutes=1)
            })
        self.md5_duplicate_doc_data.status = DocumentDataStatus.VERIFIED.name
        self.md5_duplicate_doc_data.received_at = base_time + timezone.timedelta(minutes=1)
        self.md5_duplicate_doc_data.save()

        # Label duplicate (newer timestamp)
        self.label_duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="OriginalDoc",
            content_md5="unique_md5_for_label_dupe",
            status=DocumentDataStatus.VERIFIED.name
        )
        self.label_duplicate_doc.created_at = base_time + timezone.timedelta(minutes=2)
        self.label_duplicate_doc.save()

        self.label_duplicate_doc_data, _ = DocumentData.objects.get_or_create(
            document=self.label_duplicate_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': self.label_duplicate_doc.content_md5,
                'source_file_name': self.label_duplicate_doc.label,
                'source_file_id': self.label_duplicate_doc.obfuscated_id,
                'received_at': base_time + timezone.timedelta(minutes=2)
            })
        self.label_duplicate_doc_data.status = DocumentDataStatus.VERIFIED.name
        self.label_duplicate_doc_data.received_at = base_time + timezone.timedelta(minutes=2)
        self.label_duplicate_doc_data.save()

        # Content duplicate (newer timestamp)
        self.content_duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="ContentDuplicateDoc",
            content_md5="unique_md5_for_content_dupe",
            status=DocumentDataStatus.VERIFIED.name
        )
        self.content_duplicate_doc.created_at = base_time + timezone.timedelta(minutes=3)
        self.content_duplicate_doc.save()

        self.content_duplicate_doc_data, _ = DocumentData.objects.get_or_create(
            document=self.content_duplicate_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': self.content_duplicate_doc.content_md5,
                'source_file_name': self.content_duplicate_doc.label,
                'source_file_id': self.content_duplicate_doc.obfuscated_id,
                'received_at': base_time + timezone.timedelta(minutes=3)
            })
        self.content_duplicate_doc_data.status = DocumentDataStatus.VERIFIED.name
        self.content_duplicate_doc_data.received_at = base_time + timezone.timedelta(minutes=3)
        self.content_duplicate_doc_data.save()

    def test_prepare_packages_creates_package(self, mock_handle_document_creation_for_coc,
                                              mock_document_created_coc_event):
        # Configure packager to process PENDING_CREATION documents and disable duplicate exclusion
        self.packager.document_status_filter = [DocumentDataStatus.PENDING_CREATION.name]
        self.packager.exclude_duplicates_from_delivery = False  # Disable duplicate exclusion for this test
        self.packager.save()

        # Get baseline document count
        filtered_data = self.packager.get_filtered_document_data_additive()
        expected_count = filtered_data.count()
        self.assertEqual(expected_count, 2)  # Doc1 and Doc2 from setUp

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        self.packager.refresh_from_db()
        self.assertIsNotNone(self.packager.last_run)

        package = self.packager.packages.first()
        self.assertIsNotNone(package)
        self.assertEqual(package.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

        # Assert PackageEntry count matches filtered document data
        self.assertEqual(PackageEntry.objects.filter(package=package).count(), expected_count)
        for doc_data in filtered_data:
            entry = PackageEntry.objects.get(package=package, document_data=doc_data)
            self.assertEqual(entry.status_in_package, PackageEntryStatus.INCLUDED.name)

    def test_prepare_packages_packager_not_found(self, mock_handle_document_creation_for_coc,
                                                 mock_document_created_coc_event):
        """Test that prepare_packages handles a non-existent packager ID."""
        with patch('packager.tasks.logger.exception') as mock_logger:
            success = prepare_packages(9999)  # Non-existent ID
            self.assertFalse(success)
            mock_logger.assert_called_once_with('packager.retrieve.not_found packager_id=9999')

    def test_prepare_packages_no_documents_or_batches(self, mock_handle_document_creation_for_coc,
                                                      mock_document_created_coc_event):
        """Test that prepare_packages handles the case where no documents or batches match filters."""
        # Clear all filters to ensure no documents match
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        # Verify no documents are found with empty filters
        filtered_data = self.packager.get_filtered_document_data_additive()
        self.assertEqual(filtered_data.count(), 0)

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)
        self.assertEqual(self.packager.packages.count(), 0)  # No package should be created if no data

    def test_prepare_packages_processes_documents(self, mock_handle_document_creation_for_coc,
                                                  mock_document_created_coc_event):
        """Test successful document processing during package preparation."""
        # Configure packager to only process PENDING_CREATION documents for this test
        self.packager.document_status_filter = [DocumentDataStatus.PENDING_CREATION.name]
        self.packager.save()

        # Ensure documents are present and match the filter (PENDING_CREATION)
        filtered_data = self.packager.get_filtered_document_data_additive()
        expected_count = filtered_data.count()
        self.assertEqual(expected_count, 2)  # Doc1 and Doc2 from setUp

        # Mark doc2 as a duplicate of doc1 (MD5_DUPLICATE)
        DocumentRelationship.objects.create(
            source_document_data=self.doc_data1,  # doc1 is canonical
            target_document_data=self.doc_data2,  # doc2 is duplicate
            relationship_type=self.md5_rel_type,
            data_pool=self.dp
        )

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Check package is created and processed successfully
        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_SUCCESSFUL.name)

        package = self.packager.packages.first()
        self.assertIsNotNone(package)
        self.assertEqual(package.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

        # Verify PackageEntry records were created for the filtered documents
        self.assertEqual(PackageEntry.objects.filter(package=package).count(), expected_count)
        entry1 = PackageEntry.objects.get(package=package, document_data=self.doc_data1)
        entry2 = PackageEntry.objects.get(package=package, document_data=self.doc_data2)
        self.assertEqual(entry1.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)

    def test_prepare_packages_processes_extraction_batches(self, mock_handle_document_creation_for_coc,
                                                           mock_document_created_coc_event):
        """Test successful extraction batch processing during package preparation."""
        # Configure packager to only process extraction batches and disable duplicate exclusion
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = [ExtractionBatchVerificationStatus.VERIFIED.name]
        self.packager.exclude_duplicates_from_delivery = False  # Disable duplicate exclusion for this test
        self.packager.save()

        # Create TrainingSet and TrainingRevision for the ExtractionBatch
        batch_ts, _ = create_training_ready_training_set(data_pool=self.dp)
        batch_tr, _ = create_training_revision(data_pool=self.dp, training_set=batch_ts)

        # Create a verified extraction batch and associate it with doc1 and doc2
        batch, _ = create_extraction_batch(
            data_pool=self.dp,
            documents=[self.doc1, self.doc2],
            verification_status=ExtractionBatchVerificationStatus.VERIFIED.name,
            training_revision=batch_tr
        )

        # Get baseline document count from additive filtering (should be 2 from the batch)
        filtered_data = self.packager.get_filtered_document_data_additive()
        expected_count = filtered_data.count()
        self.assertEqual(expected_count, 2, "Should have documents from verified extraction batches")

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        package = self.packager.packages.first()
        self.assertIsNotNone(package)
        self.assertEqual(package.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

        # Verify PackageEntry records were created with correct count
        self.assertEqual(PackageEntry.objects.filter(package=package).count(), expected_count)

        # Verify the DocumentData instances from the batch are in the package
        for doc in [self.doc1, self.doc2]:
            doc_data = DocumentData.objects.get(document=doc, data_pool=self.dp)
            self.assertTrue(package.package_entries.filter(document_data=doc_data).exists())
            entry = PackageEntry.objects.get(package=package, document_data=doc_data)
            self.assertEqual(entry.status_in_package, PackageEntryStatus.INCLUDED.name)

    @patch('packager.tasks.process_extraction_batches')
    @patch('packager.tasks.process_documents')
    def test_prepare_packages_handles_process_failure(self, mock_process_documents, mock_process_extraction_batches,
                                                      mock_handle_document_creation_for_coc,
                                                      mock_document_created_coc_event):
        """Test handling of document processing failure during package preparation."""
        # Ensure documents are present for processing
        filtered_data = self.packager.get_filtered_document_data_additive()
        self.assertGreater(filtered_data.count(), 0)

        # Mock process_documents to return False to simulate failure
        mock_process_documents.return_value = False
        # Mock process_extraction_batches to return True (or False, doesn't matter for this test)
        mock_process_extraction_batches.return_value = True

        success = prepare_packages(self.packager.id)
        self.assertFalse(success)  # Expecting False because process_documents failed

        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_FAILED.name)

        package = self.packager.packages.first()
        self.assertIsNotNone(package)
        self.assertEqual(package.status, PackageStatus.PROCESSING_FAILED.name)

    def test_prepare_packages_stream_mode_invalid_document_id(self, mock_handle_document_creation_for_coc,
                                                              mock_document_created_coc_event):
        """Test stream mode with an invalid document ID."""
        self.packager.packager_schedule_type = PackagerSchedules.STREAM.name
        self.packager.save()

        non_existent_id = 999999
        success = prepare_packages(self.packager.id, document_id=non_existent_id)
        self.assertFalse(success)  # Task should fail when document is not found in stream mode

        # Verify packager status is set to failed
        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_FAILED.name)

        # No packages should be created for invalid document ID
        self.assertEqual(self.packager.packages.count(), 0)

    def test_prepare_packages_with_documents_and_batches(self, mock_handle_document_creation_for_coc,
                                                         mock_document_created_coc_event):
        """Test prepare_packages creates both DocumentData and ExtractionBatchData in one run,
        using an isolated DataPool and Packager."""
        # Create a new Organization and DataPool for this test to ensure isolation
        test_org, _ = create_organization(label="test_prepare_packages_doc_batch_org")
        test_dp, _ = create_data_pool(organization=test_org, label="test_prepare_packages_doc_batch_dp")
        CoCEventType.objects.get_or_create(data_pool=test_dp, label='DOCUMENT_CREATED')

        # Create documents within the new DataPool with unique MD5 hashes to avoid duplicate detection
        doc1_test, _ = create_document(data_pool=test_dp, status=DocumentDataStatus.PENDING_CREATION.name,
                                       label="test_doc1.pdf", content_md5="unique_md5_1")
        doc2_test, _ = create_document(data_pool=test_dp, status=DocumentDataStatus.PENDING_CREATION.name,
                                       label="test_doc2.pdf", content_md5="unique_md5_2")
        doc3_test, _ = create_document(data_pool=test_dp, status=DocumentDataStatus.VERIFIED.name,
                                       label="test_doc3_verified.pdf", content_md5="unique_md5_3")

        # Explicitly get or create DocumentData for the test documents and set their status
        doc_data1_test, _ = DocumentData.objects.get_or_create(
            document=doc1_test,
            data_pool=test_dp,
            defaults={'status': DocumentDataStatus.PENDING_CREATION.name}
        )
        doc_data1_test.status = DocumentDataStatus.PENDING_CREATION.name
        doc_data1_test.save()

        doc_data2_test, _ = DocumentData.objects.get_or_create(
            document=doc2_test,
            data_pool=test_dp,
            defaults={'status': DocumentDataStatus.PENDING_CREATION.name}
        )
        doc_data2_test.status = DocumentDataStatus.PENDING_CREATION.name
        doc_data2_test.save()

        doc_data3_test, _ = DocumentData.objects.get_or_create(
            document=doc3_test,
            data_pool=test_dp,
            defaults={'status': DocumentDataStatus.VERIFIED.name}
        )
        doc_data3_test.status = DocumentDataStatus.VERIFIED.name
        doc_data3_test.save()

        # Create a new Packager for this DataPool
        test_packager = create_packager(
            data_pool=test_dp,
            document_status_filter=[DocumentDataStatus.CREATED.name, DocumentDataStatus.PENDING_CREATION.name,
                                    DocumentDataStatus.VERIFIED.name],
            eb_status_filter=[ExtractionBatchVerificationStatus.VERIFIED.name],
            packager_schedule_type=PackagerSchedules.MANUAL.name
        )

        # Create TrainingSet and TrainingRevision for the ExtractionBatch
        ts, _ = create_training_ready_training_set(data_pool=test_dp)
        tr, _ = create_training_revision(data_pool=test_dp, training_set=ts)

        # Create ExtractionBatch with doc3_test
        eb_test, _ = create_extraction_batch(
            data_pool=test_dp,
            documents=[doc3_test],
            verification_status=ExtractionBatchVerificationStatus.VERIFIED.name,
            training_revision=tr
        )

        # Add the created batch ID to the packager's filter
        test_packager.eb_id_filter = [str(eb_test.id)]
        test_packager.save()

        # Set up finished state for the batch
        # eb_test.finished = True
        # eb_test.save()

        # Get baseline count from additive filtering
        filtered_data = test_packager.get_filtered_document_data_additive()
        expected_doc_count = filtered_data.count()
        self.assertEqual(expected_doc_count, 3)  # All docs should be included

        # Run package preparation
        success = prepare_packages(test_packager.id)

        self.assertTrue(success)

        test_packager.refresh_from_db()  # Refresh to get the latest package association
        package = test_packager.packages.first()
        self.assertIsNotNone(package)
        self.assertEqual(package.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

        # Verify correct number of entries were created
        self.assertEqual(PackageEntry.objects.filter(package=package).count(), expected_doc_count)

        # Verify each document is included exactly once
        for doc in [doc1_test, doc2_test, doc3_test]:
            doc_data = DocumentData.objects.get(document=doc, data_pool=test_dp)
            self.assertTrue(
                PackageEntry.objects.filter(package=package, document_data=doc_data).exists(),
                f"Missing package entry for document {doc.label}"
            )

    def test_document_data_package_field_is_none_after_processing(self, mock_handle_document_creation_for_coc,
                                                                  mock_document_created_coc_event):
        """
        Test that DocumentData instances are properly linked to packages through PackageEntry, not direct package field.
        This verifies the correct relationship model is used.
        """
        # Configure packager to process PENDING_CREATION documents for this test
        self.packager.document_status_filter = [DocumentDataStatus.PENDING_CREATION.name]
        self.packager.save()

        # Ensure documents are present for processing
        doc1, _ = create_document(data_pool=self.dp,
                                  status=DocumentDataStatus.PENDING_CREATION.name,
                                  label="doc1_for_package_test.pdf")
        doc2, _ = create_document(data_pool=self.dp,
                                  status=DocumentDataStatus.PENDING_CREATION.name,
                                  label="doc2_for_package_test.pdf")

        # Ensure DocumentData exists for these documents before calling prepare_packages
        # This simulates the signal handler creating them
        doc_data1, _ = DocumentData.objects.get_or_create(
            document=doc1, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': doc1.obfuscated_id,
                'source_file_name': doc1.label,
                'source_file_md5': doc1.content_md5,
                'received_at': doc1.created_at
            }
        )
        doc_data1.status = DocumentDataStatus.PENDING_CREATION.name
        doc_data1.save()

        doc_data2, _ = DocumentData.objects.get_or_create(
            document=doc2, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': doc2.obfuscated_id,
                'source_file_name': doc2.label,
                'source_file_md5': doc2.content_md5,
                'received_at': doc2.created_at
            }
        )
        doc_data2.status = DocumentDataStatus.PENDING_CREATION.name
        doc_data2.save()

        # Run the prepare_packages task
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Retrieve the DocumentData instances again to get the state after processing
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()

        # Verify that DocumentData doesn't have a direct package field
        self.assertFalse(hasattr(doc_data1, 'package'), "DocumentData should not have a 'package' attribute")
        self.assertFalse(hasattr(doc_data2, 'package'), "DocumentData should not have a 'package' attribute")

        # Verify that the relationship is properly established through PackageEntry
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Verify PackageEntry records were created
        self.assertTrue(PackageEntry.objects.filter(
            package=package,
            document_data=doc_data1
        ).exists())
        self.assertTrue(PackageEntry.objects.filter(
            package=package,
            document_data=doc_data2
        ).exists())

    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    def test_prepare_packages_identifies_md5_duplicates(self, mock_run_checks, mock_handle_document_creation_for_coc,
                                                        mock_document_created_coc_event):
        """Test that MD5 duplicates are correctly identified and excluded if configured."""
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        # Mock the duplicate detection service to simulate MD5 duplicate detection
        def mock_duplicate_detection(check_types):
            # Simulate finding MD5 duplicate and creating relationship
            DocumentRelationship.objects.get_or_create(
                source_document_data=self.original_doc_data,
                target_document_data=self.md5_duplicate_doc_data,
                relationship_type=self.md5_rel_type,
                data_pool=self.dp,
                defaults={'metadata': {'detection_type': 'md5_duplicate'}}
            )
            # Set canonical pointer
            self.md5_duplicate_doc_data.canonical_document_data = self.original_doc_data
            self.md5_duplicate_doc_data.save()

        mock_run_checks.side_effect = mock_duplicate_detection

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Check original document's PackageEntry
        original_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.original_doc_data
        )
        self.assertEqual(original_entry.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertIsNone(original_entry.exclusion_reason)

        # Verify PackageEntry was created for the duplicate but with EXCLUDED status
        duplicate_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.md5_duplicate_doc_data
        )
        self.assertEqual(duplicate_entry.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)
        self.assertEqual(duplicate_entry.exclusion_reason, 'MD5_DUPLICATE')

        # Verify DocumentRelationship was created
        relationship = DocumentRelationship.objects.get(
            source_document_data=self.original_doc_data,
            target_document_data=self.md5_duplicate_doc_data,
            relationship_type=self.md5_rel_type
        )
        self.assertIsNotNone(relationship)

        # Verify canonical pointer is set
        self.md5_duplicate_doc_data.refresh_from_db()
        self.assertEqual(self.md5_duplicate_doc_data.canonical_document_data, self.original_doc_data)

    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    def test_prepare_packages_identifies_label_duplicates(self, mock_run_checks, mock_handle_document_creation_for_coc,
                                                          mock_document_created_coc_event):
        """Test that label duplicates are correctly identified and excluded if configured."""
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        # Mock the duplicate detection service to simulate label duplicate detection
        def mock_duplicate_detection(check_types):
            # Since label_duplicate_doc has newer timestamp, it should be marked as duplicate
            DocumentRelationship.objects.get_or_create(
                source_document_data=self.original_doc_data,
                target_document_data=self.label_duplicate_doc_data,
                relationship_type=self.label_rel_type,
                data_pool=self.dp,
                defaults={'metadata': {'detection_type': 'label_duplicate'}}
            )
            # Set canonical pointer
            self.label_duplicate_doc_data.canonical_document_data = self.original_doc_data
            self.label_duplicate_doc_data.save()

        mock_run_checks.side_effect = mock_duplicate_detection

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Check canonical document's PackageEntry (original_doc is canonical due to earlier timestamp)
        canonical_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.original_doc_data
        )
        self.assertEqual(canonical_entry.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertIsNone(canonical_entry.exclusion_reason)

        # Verify PackageEntry was created for the duplicate but with EXCLUDED status
        duplicate_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.label_duplicate_doc_data
        )
        self.assertEqual(duplicate_entry.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)
        self.assertEqual(duplicate_entry.exclusion_reason, 'LABEL_DUPLICATE')

        # Verify DocumentRelationship was created
        relationship = DocumentRelationship.objects.get(
            source_document_data=self.original_doc_data,
            target_document_data=self.label_duplicate_doc_data,
            relationship_type=self.label_rel_type
        )
        self.assertIsNotNone(relationship)

        # Verify canonical pointer is set
        self.label_duplicate_doc_data.refresh_from_db()
        self.assertEqual(self.label_duplicate_doc_data.canonical_document_data, self.original_doc_data)

    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    def test_prepare_packages_with_content_duplicates(self, mock_run_checks, mock_handle_document_creation_for_coc,
                                                      mock_document_created_coc_event):
        """Test that content duplicates are correctly identified and excluded if configured."""
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        # Create original document with earlier timestamp
        base_time = timezone.now()
        original_doc, _ = create_document(
            data_pool=self.dp,
            status=DocumentDataStatus.VERIFIED.name,
            label="OriginalContentDoc",
            content_md5="original_content_md5"
        )
        original_doc.created_at = base_time
        original_doc.save()

        original_doc_data, _ = DocumentData.objects.get_or_create(
            document=original_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': original_doc.content_md5,
                'source_file_name': original_doc.label,
                'source_file_id': original_doc.obfuscated_id,
                'received_at': base_time,
                'content_hash': 'content_hash_1'
            })
        original_doc_data.status = DocumentDataStatus.VERIFIED.name
        original_doc_data.received_at = base_time
        original_doc_data.content_hash = 'content_hash_1'
        original_doc_data.save()

        # Create content duplicate document with later timestamp
        content_duplicate_doc, _ = create_document(
            data_pool=self.dp,
            label="ContentDuplicateDoc",
            content_md5="different_md5",
            status=DocumentDataStatus.VERIFIED.name
        )
        content_duplicate_doc.created_at = base_time + timezone.timedelta(minutes=1)
        content_duplicate_doc.save()

        content_duplicate_doc_data, _ = DocumentData.objects.get_or_create(
            document=content_duplicate_doc, data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_md5': content_duplicate_doc.content_md5,
                'source_file_name': content_duplicate_doc.label,
                'source_file_id': content_duplicate_doc.obfuscated_id,
                'received_at': base_time + timezone.timedelta(minutes=1),
                'content_hash': 'content_hash_1'  # Same content hash as original
            })
        content_duplicate_doc_data.status = DocumentDataStatus.VERIFIED.name
        content_duplicate_doc_data.received_at = base_time + timezone.timedelta(minutes=1)
        content_duplicate_doc_data.content_hash = 'content_hash_1'
        content_duplicate_doc_data.save()

        # Mock the duplicate detection service to simulate content duplicate detection
        def mock_duplicate_detection(check_types):
            # Simulate finding content duplicate and creating relationship
            DocumentRelationship.objects.get_or_create(
                source_document_data=original_doc_data,
                target_document_data=content_duplicate_doc_data,
                relationship_type=self.content_rel_type,
                data_pool=self.dp,
                defaults={'metadata': {'detection_type': 'content_duplicate'}}
            )
            # Set canonical pointer
            content_duplicate_doc_data.canonical_document_data = original_doc_data
            content_duplicate_doc_data.save()

        mock_run_checks.side_effect = mock_duplicate_detection

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Check original document's PackageEntry
        original_entry = PackageEntry.objects.get(
            package=package,
            document_data=original_doc_data
        )
        self.assertEqual(original_entry.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertIsNone(original_entry.exclusion_reason)

        # Verify PackageEntry was created for the content duplicate but with EXCLUDED status
        duplicate_entry = PackageEntry.objects.get(
            package=package,
            document_data=content_duplicate_doc_data
        )
        self.assertEqual(duplicate_entry.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)
        self.assertEqual(duplicate_entry.exclusion_reason, 'CONTENT_DUPLICATE')

        # Verify DocumentRelationship was created
        relationship = DocumentRelationship.objects.get(
            source_document_data=original_doc_data,
            target_document_data=content_duplicate_doc_data,
            relationship_type=self.content_rel_type
        )
        self.assertIsNotNone(relationship)

        # Verify canonical pointer is set
        content_duplicate_doc_data.refresh_from_db()
        self.assertEqual(content_duplicate_doc_data.canonical_document_data, original_doc_data)

    def test_prepare_packages_calls_duplicate_detection_service(self, mock_handle_document_creation_for_coc,
                                                                mock_document_created_coc_event):
        """Test that prepare_packages properly calls DuplicateDetectionService for each document."""
        # Configure packager to process VERIFIED documents (which have data in setUp)
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.exclude_duplicates_from_delivery = True  # Enable duplicate detection
        self.packager.save()

        # Ensure the VERIFIED documents have incomplete duplicate checks so the service gets called
        verified_doc_datas = [self.original_doc_data, self.md5_duplicate_doc_data,
                              self.label_duplicate_doc_data, self.content_duplicate_doc_data]
        for doc_data in verified_doc_datas:
            doc_data.is_md5_check_completed = False
            doc_data.is_label_check_completed = False
            doc_data.is_content_check_completed = False
            doc_data.save()

        # Debug: Check the state after setting flags to False
        for doc_data in verified_doc_datas:
            doc_data.refresh_from_db()
            print(f"Doc {doc_data.obfuscated_id}: md5={doc_data.is_md5_check_completed}, "
                  f"label={doc_data.is_label_check_completed}, content={doc_data.is_content_check_completed}")

        filtered_data = self.packager.get_filtered_document_data_additive()
        print(f"Filtered data count: {filtered_data.count()}")
        print(f"Filtered data IDs: {[dd.obfuscated_id for dd in filtered_data]}")

        with patch('packager.tasks.DuplicateDetectionService') as mock_service_class:
            mock_service_instance = MagicMock()
            # Set up the mock to have a truthy document_data attribute
            mock_service_instance.document_data = MagicMock()
            mock_service_class.return_value = mock_service_instance

            # Run package preparation
            success = prepare_packages(self.packager.id)
            self.assertTrue(success)

            expected_calls = filtered_data.count()
            print(f"Expected calls: {expected_calls}, Actual calls: {mock_service_class.call_count}")

            # Debug: Print all calls to the mock
            print(f"Mock service class call args list: {mock_service_class.call_args_list}")
            print(f"Mock service instance run_checks call count: {mock_service_instance.run_checks.call_count}")
            print(f"Mock service instance run_checks call args list: {mock_service_instance.run_checks.call_args_list}")

            self.assertEqual(expected_calls, mock_service_class.call_count)
            self.assertEqual(expected_calls, mock_service_instance.run_checks.call_count)

    def test_prepare_packages_without_duplicate_exclusion(self, mock_handle_document_creation_for_coc,
                                                          mock_document_created_coc_event):
        """Test that when exclude_duplicates_from_delivery=False, duplicates are still included."""
        self.packager.exclude_duplicates_from_delivery = False
        self.packager.save()

        # Manually create a duplicate relationship to test behavior
        DocumentRelationship.objects.create(
            source_document_data=self.original_doc_data,
            target_document_data=self.md5_duplicate_doc_data,
            relationship_type=self.md5_rel_type,
            data_pool=self.dp,
            metadata={'detection_type': 'md5_duplicate'}
        )
        self.md5_duplicate_doc_data.canonical_document_data = self.original_doc_data
        self.md5_duplicate_doc_data.save()

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Both original and duplicate should be included when exclusion is disabled
        original_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.original_doc_data
        )
        self.assertEqual(original_entry.status_in_package, PackageEntryStatus.INCLUDED.name)

        duplicate_entry = PackageEntry.objects.get(
            package=package,
            document_data=self.md5_duplicate_doc_data
        )
        self.assertEqual(duplicate_entry.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertIsNone(duplicate_entry.exclusion_reason)

    def test_prepare_packages_handles_document_status_filtering(self, mock_handle_document_creation_for_coc,
                                                                mock_document_created_coc_event):
        """Test that prepare_packages correctly filters documents by status."""
        # Set packager to only include VERIFIED documents
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.save()

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Only VERIFIED documents should be included
        package_entries = PackageEntry.objects.filter(package=package)
        verified_docs = [self.original_doc_data, self.md5_duplicate_doc_data,
                         self.label_duplicate_doc_data, self.content_duplicate_doc_data]

        # Should have entries for all VERIFIED documents
        self.assertEqual(package_entries.count(), len(verified_docs))

        # PENDING_CREATION documents should not be included
        self.assertFalse(PackageEntry.objects.filter(
            package=package,
            document_data__in=[self.doc_data1, self.doc_data2]
        ).exists())

    def test_prepare_packages_creates_coc_events(self, mock_handle_document_creation_for_coc,
                                                 mock_document_created_coc_event):
        """Test that prepare_packages creates appropriate CoC events."""
        from packager.models import CoCEvent

        # Count initial CoC events
        initial_coc_count = CoCEvent.objects.filter(data_pool=self.dp).count()

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Verify CoC events were created
        final_coc_count = CoCEvent.objects.filter(data_pool=self.dp).count()

        # Should have more CoC events after package preparation
        # (exact count depends on implementation, but should be greater)
        self.assertGreaterEqual(final_coc_count, initial_coc_count)

        # Verify package creation event exists
        package = self.packager.packages.first()
        package_coc_events = CoCEvent.objects.filter(
            data_pool=self.dp,
            package=package
        )
        self.assertGreater(package_coc_events.count(), 0)

    def test_prepare_packages_signal_integration(self, mock_handle_document_creation_for_coc,
                                                 mock_document_created_coc_event):
        """Test that prepare_packages properly integrates with signal handlers."""
        # Reset mock call counts
        mock_handle_document_creation_for_coc.reset_mock()
        mock_document_created_coc_event.reset_mock()

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Verify that the mocked signals were called
        # Note: The exact number of calls depends on the implementation
        # but we should verify the signals are being triggered
        self.assertGreaterEqual(mock_handle_document_creation_for_coc.call_count, 0)
        self.assertGreaterEqual(mock_document_created_coc_event.call_count, 0)

    def test_prepare_packages_additive_filtering_logic(self, mock_handle_document_creation_for_coc,
                                                       mock_document_created_coc_event):
        """Test that prepare_packages uses additive filtering correctly."""
        # Create a packager with both document and extraction batch filters
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.eb_status_filter = [ExtractionBatchVerificationStatus.VERIFIED.name]
        self.packager.save()

        # Create an extraction batch with verified status
        ts, _ = create_training_ready_training_set(data_pool=self.dp)
        tr, _ = create_training_revision(data_pool=self.dp, training_set=ts)

        # Create a new document for the batch
        batch_doc, _ = create_document(
            data_pool=self.dp,
            status=DocumentDataStatus.VERIFIED.name,
            label="BatchDoc.pdf"
        )

        # Create DocumentData for the batch document (this is normally done by signals)
        batch_doc_data, _ = DocumentData.objects.get_or_create(
            document=batch_doc,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.VERIFIED.name,
                'source_file_id': batch_doc.obfuscated_id,
                'source_file_name': batch_doc.label,
                'source_file_md5': batch_doc.content_md5,
                'received_at': batch_doc.created_at
            }
        )
        batch_doc_data.status = DocumentDataStatus.VERIFIED.name
        batch_doc_data.save()

        batch, _ = create_extraction_batch(
            data_pool=self.dp,
            documents=[batch_doc],
            verification_status=ExtractionBatchVerificationStatus.VERIFIED.name,
            training_revision=tr
        )

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Get the created package
        package = self.packager.packages.first()
        self.assertIsNotNone(package)

        # Verify that documents from both filters are included
        package_entries = PackageEntry.objects.filter(package=package)

        # Should include VERIFIED documents from document filter
        verified_doc_entries = package_entries.filter(
            document_data__document__status=DocumentDataStatus.VERIFIED.name
        )
        print(verified_doc_entries)
        self.assertGreater(verified_doc_entries.count(), 0)

        # Should include documents from VERIFIED extraction batches
        batch_entry = package_entries.filter(document_data=batch_doc_data)
        self.assertTrue(batch_entry.exists())

    def test_prepare_packages_handles_empty_filters_correctly(self, mock_handle_document_creation_for_coc,
                                                              mock_document_created_coc_event):
        """Test that prepare_packages handles empty filters correctly."""
        # Set all filters to empty
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        # Run package preparation
        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Should not create any packages when no filters match
        self.assertEqual(self.packager.packages.count(), 0)

    def test_prepare_packages_reuses_successful_package_on_second_run(self, mock_handle_document_creation_for_coc,
                                                                      mock_document_created_coc_event):
        """Test the exact scenario described: packager should reuse existing successful packages."""
        doc1, _ = create_document(data_pool=self.dp)
        doc_data1, _ = DocumentData.objects.get_or_create(
            document=doc1,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': doc1.obfuscated_id,
                'source_file_name': doc1.label,
                'source_file_md5': doc1.content_md5,
                'received_at': doc1.created_at,
                'source_file_num_pages': doc1.page_count,
                'source_file_size': doc1.filesize
            }
        )

        self.packager.document_status_filter = ['PENDING_CREATION']
        self.packager.save()

        success1 = prepare_packages(self.packager.id)
        self.assertTrue(success1)

        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_SUCCESSFUL.name)

        package1 = self.packager.packages.first()
        self.assertIsNotNone(package1)
        self.assertEqual(package1.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

        entry_count1 = package1.package_entries.count()
        self.assertGreater(entry_count1, 0)

        package1_id = package1.id

        success2 = prepare_packages(self.packager.id)
        self.assertTrue(success2)

        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_SUCCESSFUL.name)

        self.assertEqual(self.packager.packages.count(), 1)

        package2 = self.packager.packages.first()
        self.assertEqual(package1_id, package2.id)  # Same package reused
        self.assertEqual(package2.status, PackageStatus.PROCESSING_SUCCESSFUL.name)

    def test_prepare_packages_marks_successful_when_no_new_data(self, mock_handle_document_creation_for_coc,
                                                                mock_document_created_coc_event):
        """Test that when no new data is found, both packager and package are marked as successful."""
        self.packager.document_status_filter = ['NONEXISTENT_STATUS']
        self.packager.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        self.packager.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_SUCCESSFUL.name)

    def test_prepare_packages_reuses_package_with_existing_entries(self, mock_handle_document_creation_for_coc,
                                                                   mock_document_created_coc_event):
        """Test that prepare_packages reuses packages that already have entries."""
        doc1, _ = create_document(data_pool=self.dp)
        doc_data1, _ = DocumentData.objects.get_or_create(
            document=doc1,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': doc1.obfuscated_id,
                'source_file_name': doc1.label,
                'source_file_md5': doc1.content_md5,
                'received_at': doc1.created_at,
                'source_file_num_pages': doc1.page_count,
                'source_file_size': doc1.filesize
            }
        )

        self.packager.document_status_filter = ['PENDING_CREATION']
        self.packager.save()

        success1 = prepare_packages(self.packager.id)
        self.assertTrue(success1)

        package1 = self.packager.packages.first()
        initial_entry_count = package1.package_entries.count()
        self.assertGreater(initial_entry_count, 0)

        doc2, _ = create_document(data_pool=self.dp)
        doc_data2, _ = DocumentData.objects.get_or_create(
            document=doc2,
            data_pool=self.dp,
            defaults={
                'status': DocumentDataStatus.PENDING_CREATION.name,
                'source_file_id': doc2.obfuscated_id,
                'source_file_name': doc2.label,
                'source_file_md5': doc2.content_md5,
                'received_at': doc2.created_at,
                'source_file_num_pages': doc2.page_count,
                'source_file_size': doc2.filesize
            }
        )

        success2 = prepare_packages(self.packager.id)
        self.assertTrue(success2)

        self.assertEqual(self.packager.packages.count(), 1)

        package2 = self.packager.packages.first()
        self.assertEqual(package1.id, package2.id)  # Same package

        final_entry_count = package2.package_entries.count()
        self.assertEqual(final_entry_count, initial_entry_count + 1)

        self.packager.refresh_from_db()
        package2.refresh_from_db()
        self.assertEqual(self.packager.packager_status, PackagerStatus.PROCESSING_SUCCESSFUL.name)
        self.assertEqual(package2.status, PackageStatus.PROCESSING_SUCCESSFUL.name)


@patch('packager.signals.handle_document_creation_for_coc')
class PackageEntryDeliveryTests(TransactionTestCase, TestCaseMixin):
    """Test class focusing on PackageEntry updates during package delivery."""

    def setUp(self):
        self.setUpPyfakefs()
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(
            data_pool=self.dp,
            label="DeliveryPackager",
            delivery_schedule_type=PackagerDeliveryOptions.AUTOMATIC.name
        )

        # Create package in VERIFIED status
        self.package = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.VERIFIED.name
        )

        # Create multiple documents and package entries with different statuses
        self.doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf")
        self.doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf")
        self.doc3, _ = create_document(data_pool=self.dp, label="doc3.pdf")

        self.doc_data1, _ = DocumentData.objects.get_or_create(document=self.doc1, data_pool=self.dp)
        self.doc_data2, _ = DocumentData.objects.get_or_create(document=self.doc2, data_pool=self.dp)
        self.doc_data3, _ = DocumentData.objects.get_or_create(document=self.doc3, data_pool=self.dp)

        # Create PackageEntry records with different statuses
        self.entry1 = PackageEntry.objects.create(
            package=self.package,
            document_data=self.doc_data1,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name  # Should start as INCLUDED
        )
        self.entry2 = PackageEntry.objects.create(
            package=self.package,
            document_data=self.doc_data2,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,  # Won't be delivered
            exclusion_reason='MD5_DUPLICATE'
        )
        self.entry3 = PackageEntry.objects.create(
            package=self.package,
            document_data=self.doc_data3,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name  # Should start as INCLUDED
        )

    @patch('packager.tasks._upload_to_s3', return_value=True)
    @patch('packager.models.Package.render_package_data')
    def test_deliver_package_updates_only_delivered_entries(self, mock_render_package_data, mock_upload_s3,
                                                            mock_handle_document_creation_for_coc):
        """Test that deliver_package only updates delivered_at for TRANSMISSION_SUCCESSFUL entries."""
        # Create a package in VERIFIED status with entries
        package = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.VERIFIED.name
        )

        doc1, _ = create_document(data_pool=self.dp, label="Doc1ForDelivery")
        doc_data1, _ = DocumentData.objects.get_or_create(document=doc1, data_pool=self.dp,
                                                          defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data1.status = DocumentDataStatus.VERIFIED.name
        doc_data1.save()

        doc2, _ = create_document(data_pool=self.dp, label="Doc2ForDelivery")
        doc_data2, _ = DocumentData.objects.get_or_create(document=doc2, data_pool=self.dp,
                                                          defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data2.status = DocumentDataStatus.VERIFIED.name
        doc_data2.save()

        entry1 = PackageEntry.objects.create(
            package=package,
            document_data=doc_data1,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name  # Should be INCLUDED before delivery
        )
        entry2 = PackageEntry.objects.create(
            package=package,
            document_data=doc_data2,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name,  # Should remain excluded
            exclusion_reason='MD5_DUPLICATE'
        )

        # Mock render_package_data to return items for the included entry
        mock_rendered_item = RenderedPackageItem(filename="test_file.json", content={"key": "value"})
        mock_render_package_data.return_value = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[])

        # Deliver the package
        deliver_package(package_id=package.obfuscated_id, data_pool_id=self.dp.id)

        # Refresh entries from database
        entry1.refresh_from_db()
        entry2.refresh_from_db()

        # Verify INCLUDED entry status is updated to TRANSMISSION_SUCCESSFUL and has delivered_at timestamp
        self.assertEqual(entry1.status_in_package, PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name)
        self.assertIsNotNone(entry1.delivered_at)

        # Verify EXCLUDED entry status remains EXCLUDED and doesn't have delivered_at timestamp
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)
        self.assertIsNone(entry2.delivered_at)

        # Verify package status is updated to TRANSMISSION_SUCCESSFUL
        package.refresh_from_db()
        self.assertEqual(package.status, PackageStatus.TRANSMISSION_SUCCESSFUL.name)

    @patch('packager.tasks._upload_to_s3', return_value=False)
    @patch('packager.models.Package.render_package_data')
    def test_failed_delivery_does_not_update_entries(self, mock_render_package_data, mock_upload_s3,
                                                     mock_handle_document_creation_for_coc):
        """Test that failed delivery doesn't update PackageEntry delivered_at timestamps."""
        # Create a package in VERIFIED status with entries
        package = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.VERIFIED.name
        )

        doc1, _ = create_document(data_pool=self.dp, label="Doc1ForFailedDelivery")
        doc_data1, _ = DocumentData.objects.get_or_create(document=doc1, data_pool=self.dp,
                                                          defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data1.status = DocumentDataStatus.VERIFIED.name
        doc_data1.save()

        entry1 = PackageEntry.objects.create(
            package=package,
            document_data=doc_data1,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name
        )

        mock_rendered_item = RenderedPackageItem(filename="test_file.json", content={"key": "value"})
        mock_render_package_data.return_value = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[])

        deliver_package(package_id=package.obfuscated_id, data_pool_id=self.dp.id)

        # Refresh entry from database
        entry1.refresh_from_db()

        # Verify entry status remains INCLUDED and doesn't have delivered_at timestamp
        self.assertEqual(entry1.status_in_package,
                         PackageEntryStatus.INCLUDED.name)  # Status should not change on failed delivery
        self.assertIsNone(entry1.delivered_at)

        # Verify package status is updated to failed
        package.refresh_from_db()
        self.assertEqual(package.status, PackageStatus.TRANSMISSION_FAILED.name)

    @patch('packager.tasks._upload_to_s3', return_value=True)
    @patch('packager.models.Package.render_package_data')
    def test_automatic_delivery_updates_multiple_packages(self, mock_render_package_data, mock_upload_to_s3,
                                                          mock_handle_document_creation_for_coc):
        """Test that _deliver_automatic_packages updates entries across multiple packages."""
        # Create another packager with automatic delivery
        packager2 = create_packager(
            data_pool=self.dp,
            label="DeliveryPackager2",
            delivery_schedule_type=PackagerDeliveryOptions.AUTOMATIC.name
        )

        # Create two packages in VERIFIED status for the first packager
        package1 = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.VERIFIED.name
        )
        package2 = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.VERIFIED.name
        )

        # Create entries for package1
        doc1_pkg1, _ = create_document(data_pool=self.dp, label="Doc1Pkg1")
        doc_data1_pkg1, _ = DocumentData.objects.get_or_create(document=doc1_pkg1, data_pool=self.dp,
                                                               defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data1_pkg1.status = DocumentDataStatus.VERIFIED.name
        doc_data1_pkg1.save()
        entry1_pkg1 = PackageEntry.objects.create(
            package=package1,
            document_data=doc_data1_pkg1,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name
        )

        # Create entries for package2
        doc1_pkg2, _ = create_document(data_pool=self.dp, label="Doc1Pkg2")
        doc_data1_pkg2, _ = DocumentData.objects.get_or_create(document=doc1_pkg2, data_pool=self.dp,
                                                               defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data1_pkg2.status = DocumentDataStatus.VERIFIED.name
        doc_data1_pkg2.save()
        entry1_pkg2 = PackageEntry.objects.create(
            package=package2,
            document_data=doc_data1_pkg2,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name
        )

        # Mock render_package_data to return some rendered items
        mock_rendered_item = RenderedPackageItem(filename="test_file.json", content={"key": "value"})
        mock_render_package_data.return_value = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[])

        from packager.tasks import _deliver_automatic_packages
        success = _deliver_automatic_packages()

        self.assertTrue(success)

        # Verify all TRANSMISSION_SUCCESSFUL entries got timestamps
        for entry in [entry1_pkg1, entry1_pkg2]:
            entry.refresh_from_db()
            self.assertEqual(entry.status_in_package, PackageEntryStatus.TRANSMISSION_SUCCESSFUL.name)
            self.assertIsNotNone(entry.delivered_at)

        # Verify package statuses are updated
        package1.refresh_from_db()
        self.assertEqual(package1.status, PackageStatus.TRANSMISSION_SUCCESSFUL.name)
        package2.refresh_from_db()
        self.assertEqual(package2.status, PackageStatus.TRANSMISSION_SUCCESSFUL.name)

    @patch('packager.tasks._upload_to_s3')
    @patch('packager.models.Package.render_package_data')
    def test_deliver_package_with_invalid_status(self, mock_render_package_data, mock_upload_s3,
                                                 mock_handle_document_creation_for_coc):
        """Test that deliver_package doesn't update entries for package with invalid status."""
        # Create a package with a status other than VERIFIED
        package = Package.objects.create(
            packager=self.packager,
            data_pool=self.dp,
            status=PackageStatus.PROCESSING.name  # Invalid status for delivery
        )

        doc1, _ = create_document(data_pool=self.dp, label="Doc1ForInvalidStatus")
        doc_data1, _ = DocumentData.objects.get_or_create(document=doc1, data_pool=self.dp,
                                                          defaults={'status': DocumentDataStatus.VERIFIED.name})
        doc_data1.status = DocumentDataStatus.VERIFIED.name
        doc_data1.save()

        entry1 = PackageEntry.objects.create(
            package=package,
            document_data=doc_data1,
            data_pool=self.dp,
            status_in_package=PackageEntryStatus.INCLUDED.name
        )

        # Mock render_package_data to return items (though it shouldn't be called)
        mock_rendered_item = RenderedPackageItem(filename="test_file.json", content={"key": "value"})
        mock_render_package_data.return_value = PackageRenderResult(rendered_items=[mock_rendered_item], errors=[])

        # Attempt delivery
        deliver_package(package_id=package.obfuscated_id, data_pool_id=self.dp.id)

        # Verify upload and render were not called
        mock_render_package_data.assert_not_called()
        mock_upload_s3.assert_not_called()

        # Verify entry status and delivered_at are unchanged
        entry1.refresh_from_db()
        self.assertEqual(entry1.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertIsNone(entry1.delivered_at)

        # Verify package status is unchanged
        package.refresh_from_db()
        self.assertEqual(package.status, PackageStatus.PROCESSING.name)
