"""
Consolidated content hashing and duplicate detection tests.

This module consolidates tests from:
- test_content_hashing.py (8 tests)
- test_content_hash_and_duplicates.py (11 tests)
- test_document_data_content_hash.py (5 tests)
- test_duplicate_detection_content_hash.py (3 tests)
- test_duplicate_detection_service.py (10 tests)

Total: 37 tests covering content hashing, duplicate detection, and integration.
"""
import hashlib
import json
import logging
from unittest.mock import MagicMock, patch

from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from django.db.models import Q
from django.test import Client

from documents.models import Document
from documents.tests.util import create_document
from extract.models import Extraction
from extract.tests.util import create_extraction as util_create_extraction
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, DocumentRelationship, DocumentRelationshipType, Package, PackageEntry
from packager.pydantic_classes import PackageEntryStatus
from packager.services.duplicate_detection_service import DuplicateDetectionService
from packager.tasks import prepare_packages
from packager.tests.base import TransactionPackagerTestCase
from packager.tests.util import create_packager, get_api_path
from packager.utils.content_hashing import generate_document_content_hash
from training.tests.util import create_training_set, create_training_revision
from users.tests.util import create_user

logger = logging.getLogger(__name__)


class ContentHashingTests(TransactionPackagerTestCase):
    """Tests for content hashing utilities - from test_content_hashing.py"""

    def test_generate_hash_with_normal_text(self):
        """Test hash generation with regular document text."""
        extraction = MagicMock(spec=Extraction)
        extraction._raw_results = {
            "field1": "Some text content",
            "field2": "More content",
            "field3": 123
        }
        extraction.get_transformed_results = lambda: [extraction._raw_results]

        hash1 = generate_document_content_hash(extraction=extraction, fields=["field1", "field2"])

        self.assertIsInstance(hash1, str)
        self.assertEqual(len(hash1), 64)  # SHA256 hex string is 64 characters

        hash2 = generate_document_content_hash(extraction=extraction, fields=["field2", "field1"])
        self.assertEqual(hash1, hash2)

        expected_data = {"field1": "Some text content", "field2": "More content"}
        expected_hash = hashlib.sha256(json.dumps(expected_data, sort_keys=True).encode('utf-8')).hexdigest()
        self.assertEqual(hash1, expected_hash)

    def test_hash_preserves_raw_values(self):
        """Test that hash generation preserves raw values without normalization."""
        extraction = MagicMock(spec=Extraction)
        extraction.data = {
            "field1": "  Some text  with  extra  spaces  ",
            "field2": "Line1\nLine2\nLine3",
        }

        # Generate hash
        hash1 = generate_document_content_hash(extraction=extraction, fields=["field1", "field2"])

        # Create expected hash with raw values
        expected_data = {
            "field1": "  Some text  with  extra  spaces  ",
            "field2": "Line1\nLine2\nLine3"
        }
        expected_hash = hashlib.sha256(json.dumps(expected_data, sort_keys=True).encode('utf-8')).hexdigest()

        self.assertEqual(hash1, expected_hash)

    def test_generate_hash_with_case_sensitivity(self):
        """Test hash generation with case sensitivity."""
        document = MagicMock(spec=Document)

        extraction1 = MagicMock(spec=Extraction)
        extraction1.data = {
            "field1": "Some Text Content",
        }
        extraction1.get_transformed_results.return_value = [extraction1.data]

        extraction2 = MagicMock(spec=Extraction)
        extraction2.data = {
            "field1": "some text content",
        }
        extraction2.get_transformed_results.return_value = [extraction2.data]

        # Should generate different hashes (case sensitive)
        hash1 = generate_document_content_hash(extraction=extraction1, fields=["field1"])
        hash2 = generate_document_content_hash(extraction=extraction2, fields=["field1"])

        self.assertNotEqual(hash1, hash2)

        expected_hash1 = hashlib.sha256(
            json.dumps({"field1": "Some Text Content"}, sort_keys=True).encode('utf-8')).hexdigest()
        expected_hash2 = hashlib.sha256(
            json.dumps({"field1": "some text content"}, sort_keys=True).encode('utf-8')).hexdigest()

        self.assertEqual(hash1, expected_hash1)
        self.assertEqual(hash2, expected_hash2)

    def test_generate_hash_with_missing_fields(self):
        """Test hash generation with missing fields."""
        document = MagicMock(spec=Document)
        extraction = MagicMock(spec=Extraction)
        extraction.data = {
            "field1": "Some text content",
            # field2 is missing
        }
        extraction.get_transformed_results.return_value = [extraction.data]

        hash1 = generate_document_content_hash(extraction, ["field1", "field2"])

        expected_data = {"field1": "Some text content"}
        expected_hash = hashlib.sha256(json.dumps(expected_data, sort_keys=True).encode('utf-8')).hexdigest()

        self.assertEqual(hash1, expected_hash)

    def test_generate_hash_with_no_extraction(self):
        """Test hash generation with no extraction."""
        extraction = None

        hash1 = generate_document_content_hash(extraction, ["field1", "field2"])

        expected_hash = hashlib.sha256(b"").hexdigest()
        self.assertEqual(hash1, expected_hash)

    def test_generate_hash_with_empty_field_list(self):
        """Test hash generation with empty field list."""
        extraction = MagicMock(spec=Extraction)
        extraction.data = {
            "field1": "Some text content",
        }

        # Empty field list should result in empty content hash
        hash1 = generate_document_content_hash(extraction, [])

        # Should generate a hash of empty string
        expected_hash = hashlib.sha256(b"").hexdigest()
        self.assertEqual(hash1, expected_hash)

    def test_generate_hash_with_non_string_fields(self):
        """Test hash generation with non-string fields."""
        extraction = MagicMock(spec=Extraction)
        extraction.data = {
            "field1": 123,
            "field2": True,
            "field3": {"nested": "value"},
            "field4": ["list", "item"]
        }
        extraction.get_transformed_results.return_value = [extraction.data]

        # Should keep non-string fields as-is
        hash1 = generate_document_content_hash(extraction, ["field1", "field2", "field3", "field4"])

        # Should generate a valid hash
        expected_data = {
            "field1": 123,
            "field2": True,
            "field3": {"nested": "value"},
            "field4": ["list", "item"]
        }
        expected_hash = hashlib.sha256(json.dumps(expected_data, sort_keys=True).encode('utf-8')).hexdigest()

        self.assertEqual(hash1, expected_hash)

    def test_hash_with_whitespace_preserved(self):
        """Test that whitespace is preserved in hash generation."""
        extraction1 = MagicMock(spec=Extraction)
        extraction1.data = {
            "field1": "  Some text  with  extra  spaces  ",
        }
        extraction1.get_transformed_results.return_value = [extraction1.data]

        extraction2 = MagicMock(spec=Extraction)
        extraction2.data = {
            "field1": "Some text with extra spaces",
        }
        extraction2.get_transformed_results.return_value = [extraction2.data]

        # Should generate different hashes due to whitespace differences
        hash1 = generate_document_content_hash(extraction1, ["field1"])
        hash2 = generate_document_content_hash(extraction2, ["field1"])

        self.assertNotEqual(hash1, hash2)
        # Use a real Extraction for repeated hash consistency check
        org, _ = create_organization(label="test_org2")
        data_pool, _ = create_data_pool(organization=org, label="Test Pool 2")
        document, _ = create_document(data_pool=data_pool, label="Test Doc 2")
        training_set, _ = create_training_set(data_pool=data_pool)
        training_revision, _ = create_training_revision(data_pool=data_pool, training_set=training_set)
        extraction, _ = create_extraction(data_pool=data_pool, document=document,
                                          training_revision=training_revision,
                                          _raw_results={
                                              "field1": "Some text content",
                                              "field2": "More content"
                                          },
                                          _requester={"model": "User", "id": "dummy"},
                                          )
        extraction.save()
        extraction.get_transformed_results = lambda: [extraction._raw_results]

        # Generate hash multiple times
        hash1 = generate_document_content_hash(extraction, ["field1", "field2"])
        hash2 = generate_document_content_hash(extraction, ["field1", "field2"])
        hash3 = generate_document_content_hash(extraction, ["field1", "field2"])

        # All should be the same
        self.assertEqual(hash1, hash2)
        self.assertEqual(hash2, hash3)


class DuplicateDetectionServiceTests(TransactionPackagerTestCase, TestCaseMixin):
    """Tests for duplicate detection service - from test_duplicate_detection_service.py"""

    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create documents
        # Using distinct labels for initial setup unless specifically testing label duplication
        now = timezone.now()
        self.doc1, _ = create_document(data_pool=self.dp, label="Doc1_Initial", content_md5="md5_1",
                                       created_at=now - timezone.timedelta(seconds=4))
        self.doc2, _ = create_document(data_pool=self.dp, label="Doc2_Initial", content_md5="md5_2",
                                       created_at=now - timezone.timedelta(seconds=3))
        self.doc3, _ = create_document(data_pool=self.dp, label="Doc1_Initial", content_md5="md5_3",
                                       created_at=now - timezone.timedelta(seconds=2))
        self.doc4, _ = create_document(data_pool=self.dp, label="Doc4_Initial", content_md5="md5_1",
                                       created_at=now - timezone.timedelta(seconds=1))

        # Fetch DocumentData objects created by signals
        # This assumes the handle_document_creation_for_coc signal is connected and working
        print(self.doc1.obfuscated_id)
        self.doc_data1, _ = DocumentData.objects.get_or_create(
            document=self.doc1, data_pool=self.dp,
            defaults={'source_file_name': self.doc1.label, 'source_file_id': self.doc1.obfuscated_id,
                      'source_file_md5': self.doc1.content_md5, 'received_at': self.doc1.created_at}
        )
        self.doc_data2, _ = DocumentData.objects.get_or_create(
            document=self.doc2, data_pool=self.dp,
            defaults={'source_file_name': self.doc2.label, 'source_file_id': self.doc2.obfuscated_id,
                      'source_file_md5': self.doc2.content_md5, 'received_at': self.doc2.created_at}
        )
        self.doc_data3, _ = DocumentData.objects.get_or_create(
            document=self.doc3, data_pool=self.dp,
            defaults={'source_file_name': self.doc3.label, 'source_file_id': self.doc3.obfuscated_id,
                      'source_file_md5': self.doc3.content_md5, 'received_at': self.doc3.created_at}
        )
        self.doc_data4, _ = DocumentData.objects.get_or_create(
            document=self.doc4, data_pool=self.dp,
            defaults={'source_file_name': self.doc4.label, 'source_file_id': self.doc4.obfuscated_id,
                      'source_file_md5': self.doc4.content_md5, 'received_at': self.doc4.created_at}
        )

        # Use get_or_create for relationship types to prevent unique constraint errors
        # The `label` field on DocumentRelationshipType is globally unique.
        # If it were unique_together with data_pool, the get_or_create would need data_pool in the lookup.
        self.md5_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="MD5_DUPLICATE",
            defaults={'description': "Documents with identical MD5 hashes", 'data_pool': self.dp}
        )
        self.label_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="LABEL_DUPLICATE",
            defaults={'description': "Documents with identical labels", 'data_pool': self.dp}
        )
        self.content_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="CONTENT_DUPLICATE",
            defaults={'description': "Documents with identical content", 'data_pool': self.dp}
        )

        # Initialize service with the PK of the first DocumentData instance
        # This service instance can be used for tests focusing on doc_data1 as the primary subject
        # Individual tests should create new service instances for other doc_data objects.
        self.service = DuplicateDetectionService(self.doc_data1.pk)

    def test_check_md5_duplicates_with_duplicates(self):
        """Test MD5 duplicate detection when duplicates exist."""
        # Create a new service instance for the document with duplicate
        # Set timestamps to ensure proper ordering
        self.doc_data1.received_at = timezone.now() - timezone.timedelta(days=1)
        self.doc_data1.save()
        self.doc_data4.received_at = timezone.now()
        self.doc_data4.save()

        # Verify received_at timestamps are set correctly
        self.doc_data1.refresh_from_db()
        self.doc_data4.refresh_from_db()
        self.assertLess(self.doc_data1.received_at, self.doc_data4.received_at)

        service_for_doc4 = DuplicateDetectionService(self.doc_data4.pk)

        # Run the MD5 check
        result = service_for_doc4.run_checks(["MD5"])

        # Check that relationship was created
        relationship = DocumentRelationship.objects.filter(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data4,
            relationship_type=self.md5_rel_type
        ).first()

        self.assertIsNotNone(relationship)

        self.doc_data4.refresh_from_db()
        self.assertEqual(self.doc_data4.parent_file_id, 'pRTtV1')
        self.assertEqual(self.doc_data4.relationship_to_parent, "MD5_DUPLICATE")

        self.doc_data1.refresh_from_db()
        self.assertIsNone(self.doc_data1.parent_file_id)
        self.assertIsNone(self.doc_data1.relationship_to_parent)

    def test_check_md5_duplicates_without_duplicates(self):
        """Test MD5 duplicate detection when no duplicates exist."""
        service_for_doc2 = DuplicateDetectionService(self.doc_data2.pk)

        result = service_for_doc2.run_checks(["MD5"])

        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=self.doc_data2,
            relationship_type=self.md5_rel_type
        ).count()

        self.assertEqual(relationship_count, 0)

        # Check that parent_relationship was not updated on doc_data2
        self.doc_data2.refresh_from_db()
        self.assertIsNone(self.doc_data2.parent_file_id)
        self.assertIsNone(self.doc_data2.relationship_to_parent)
        # Check canonical_document_data is None for original
        self.assertIsNone(self.doc_data2.canonical_document_data)

    def test_check_label_duplicates_with_duplicates(self):
        """Test label duplicate detection when duplicates exist."""
        # Create a new service instance for the document with duplicate label
        service_for_doc3 = DuplicateDetectionService(self.doc_data3.pk)

        # Run the label check
        result = service_for_doc3.run_checks(["LABEL"])

        # Check that relationship was created
        relationship = DocumentRelationship.objects.filter(
            source_document_data=self.doc_data1,
            target_document_data=self.doc_data3,
            relationship_type=self.label_rel_type
        ).first()

        self.assertIsNotNone(relationship)

        self.doc_data3.refresh_from_db()
        # The parent_file_id stores the short form of the obfuscated_id, not the full PID format
        self.assertEqual(self.doc_data3.parent_file_id, 'pRTtV1')
        self.assertEqual(self.doc_data3.relationship_to_parent, "LABEL_DUPLICATE")

        self.doc_data1.refresh_from_db()
        self.assertIsNone(self.doc_data1.parent_file_id)
        self.assertIsNone(self.doc_data1.relationship_to_parent)

    def test_check_label_duplicates_without_duplicates(self):
        """Test label duplicate detection when no duplicates exist."""
        # Create a new service instance for the document without duplicate label
        service_for_doc2 = DuplicateDetectionService(self.doc_data2.pk)

        # Run the label check
        result = service_for_doc2.run_checks(["LABEL"])

        # Check that no relationship was created
        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=self.doc_data2,
            relationship_type=self.label_rel_type
        ).count()

        self.assertEqual(relationship_count, 0)

        # Check that parent_relationship was not updated on doc_data2
        self.doc_data2.refresh_from_db()
        self.assertIsNone(self.doc_data2.parent_file_id)
        self.assertIsNone(self.doc_data2.relationship_to_parent)
        self.assertIsNone(self.doc_data2.canonical_document_data)

    def test_check_content_duplicates_calculates_hash_when_missing(self):
        """Test content duplicate detection calculates hash when missing."""
        # Ensure underlying Document is VERIFIED (required for content duplicate check)
        self.doc1.status = DocumentDataStatus.VERIFIED.value
        self.doc1.save()

        # Setup - Make sure content hash is None to trigger calculate_content_hash call
        self.doc_data1.content_hash = None
        self.doc_data1.save(update_fields=["content_hash"])

        # Refresh to ensure we have the updated state
        self.doc_data1.refresh_from_db()
        self.assertIsNone(self.doc_data1.content_hash)

        # Create the service and patch the calculate_content_hash method
        service = DuplicateDetectionService(self.doc_data1.pk)

        # Use patch.object with autospec=True to ensure proper mocking
        with patch.object(service.document_data, "calculate_content_hash",
                          autospec=True, return_value="test_hash") as mock_calc_hash:
            # Call the method we're testing
            service._check_content_duplicates()

            # Verify calculate_content_hash was called
            mock_calc_hash.assert_called_once()

            # Verify content_hash was updated
            service.document_data.refresh_from_db()
            self.assertEqual(service.document_data.content_hash, "test_hash")

    def test_check_content_duplicates_with_duplicates(self):
        """Test content duplicate detection when duplicates exist - integration test."""
        # Set up documents with VERIFIED status
        self.doc1.status = DocumentDataStatus.VERIFIED.value
        self.doc1.save()
        self.doc3.status = DocumentDataStatus.VERIFIED.value
        self.doc3.save()

        # Set up content hashes to be the same (duplicates)
        self.doc_data1.content_hash = "same_hash"
        self.doc_data1.is_content_check_completed = True
        self.doc_data1.save()

        # doc_data3 has no hash initially, will be calculated
        self.doc_data3.content_hash = None
        self.doc_data3.is_content_check_completed = False
        self.doc_data3.save()

        # Create service for doc_data3 first
        service = DuplicateDetectionService(self.doc_data3.pk)

        # Mock the calculate_content_hash method on the service's document_data instance
        with patch.object(service.document_data, 'calculate_content_hash',
                          return_value="same_hash") as mock_calc_hash:
            # Run content check
            service.run_checks(["CONTENT"])

            # Verify calculate_content_hash was called
            mock_calc_hash.assert_called_once()

        # Refresh and verify duplicate relationship was created
        self.doc_data3.refresh_from_db()
        self.assertEqual(self.doc_data3.content_hash, "same_hash")
        self.assertEqual(self.doc_data3.relationship_to_parent, "CONTENT_DUPLICATE")
        self.assertEqual(self.doc_data3.parent_file_id, str(self.doc1.obfuscated_id))

    def test_check_content_duplicates_without_duplicates(self):
        """Test content duplicate detection when no duplicates exist."""
        # Set content hashes
        self.doc_data1.content_hash = "hash1"
        self.doc_data1.save()
        self.doc_data2.content_hash = "hash2"
        self.doc_data2.save()

        # Set document to VERIFIED status (required for content check)
        self.doc2.status = DocumentDataStatus.VERIFIED.value
        self.doc2.save()

        # Create a new service instance for the document without duplicate content
        service_for_doc2 = DuplicateDetectionService(self.doc_data2.pk)

        # Run the content check
        result = service_for_doc2.run_checks(["CONTENT"])

        # Check that no relationship was created
        relationship_count = DocumentRelationship.objects.filter(
            target_document_data=self.doc_data2,
            relationship_type=self.content_rel_type
        ).count()

        self.assertEqual(relationship_count, 0)

        # Check that parent_relationship was not updated on doc_data2
        self.doc_data2.refresh_from_db()
        self.assertIsNone(self.doc_data2.parent_file_id)
        self.assertIsNone(self.doc_data2.relationship_to_parent)
        # Check canonical_document_data is None for original
        self.assertIsNone(self.doc_data2.canonical_document_data)

    def test_run_checks_with_md5_label_checks(self):
        """Test run_checks method with multiple check types."""
        # Create and patch a new service instance
        service = DuplicateDetectionService(self.doc_data1.pk)

        with patch.object(service, '_check_md5_duplicates') as mock_md5, \
                patch.object(service, '_check_label_duplicates') as mock_label, \
                patch.object(service, '_check_content_duplicates') as mock_content:
            # Run checks for MD5 and LABEL
            service.run_checks(["MD5", "LABEL"])

            # Assert proper methods were called
            mock_md5.assert_called_once()
            mock_label.assert_called_once()
            mock_content.assert_not_called()

    def test_run_checks_with_content_check(self):
        """Test run_checks method with content check."""
        # Create and patch a new service instance
        service = DuplicateDetectionService(self.doc_data1.pk)

        with patch.object(service, '_check_md5_duplicates') as mock_md5, \
                patch.object(service, '_check_label_duplicates') as mock_label, \
                patch.object(service, '_check_content_duplicates') as mock_content:
            # Run checks for CONTENT only
            service.run_checks(["CONTENT"])

            # Assert proper methods were called
            mock_md5.assert_not_called()
            mock_label.assert_not_called()
            mock_content.assert_called_once()

    def test_run_checks_with_no_check_types(self):
        """Test run_checks method with empty check types list."""
        # Create and patch a new service instance
        service = DuplicateDetectionService(self.doc_data1.pk)

        with patch.object(service, '_check_md5_duplicates') as mock_md5, \
                patch.object(service, '_check_label_duplicates') as mock_label, \
                patch.object(service, '_check_content_duplicates') as mock_content:
            # Run checks with empty list
            service.run_checks([])

            # Assert no methods were called
            mock_md5.assert_not_called()
            mock_label.assert_not_called()
            mock_content.assert_not_called()


class ContentHashAndDuplicatesIntegrationTests(TransactionPackagerTestCase, TestCaseMixin):
    """Integration tests for content hashing and duplicate detection - from test_content_hash_and_duplicates.py"""

    def setUp(self):
        self.setUpPyfakefs()
        # Add necessary directories for Django templates
        import rest_framework
        import django
        import os
        from django.conf import settings

        drf_template_dir = os.path.join(os.path.dirname(rest_framework.__file__), 'templates')
        self.fs.add_real_directory(drf_template_dir)
        django_views_dir = os.path.join(os.path.dirname(django.__file__), 'views')
        self.fs.add_real_directory(django_views_dir, read_only=False)
        for template_engine in settings.TEMPLATES:
            for d in template_engine.get('DIRS', []):
                if os.path.exists(d):
                    self.fs.add_real_directory(d)

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create relationship types
        for label, description in [
            ("MD5_DUPLICATE", "Documents with identical MD5 hashes"),
            ("LABEL_DUPLICATE", "Documents with identical labels"),
            ("CONTENT_DUPLICATE", "Documents with identical content")
        ]:
            DocumentRelationshipType.objects.get_or_create(
                label=label, data_pool=self.dp, defaults={'description': description}
            )
        self.packager = create_packager(data_pool=self.dp, label="Test Packager")

    def _create_extraction_with_data(self, document, training_revision, data_dict):
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=document, data_pool=self.dp,
                                               training_revision=training_revision)
        extraction._raw_results = data_dict  # Store raw data that get_transformed_results would process
        extraction.save()
        return extraction

    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    @patch('packager.tasks.trigger_document_data_checks_task')
    @patch('packager.utils.naming_utils.get_single_metadata_value')
    def test_prepare_packages_excludes_content_duplicates(self, mock_get_single_metadata_value,
                                                          mock_trigger_checks_task, mock_dds_run_checks):
        """Test that prepare_packages excludes content duplicates when configured."""
        mock_dds_run_checks.return_value = None
        mock_trigger_checks_task.return_value = None
        mock_get_single_metadata_value.return_value = "mock_metadata_value"

        self.packager.exclude_duplicates_from_delivery = True
        self.packager.deduplication_content_fields = ["field1", "field2"]
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.save()

        doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status=DocumentDataStatus.VERIFIED.name)
        doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status=DocumentDataStatus.VERIFIED.name)
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        extraction_data = {'field1': 'abc', 'field2': 'xyz', 'field3': 'ignore'}
        self._create_extraction_with_data(doc1, training_revision, extraction_data)
        self._create_extraction_with_data(doc2, training_revision, extraction_data)

        # Create DocumentData objects
        doc_data1, _ = DocumentData.objects.get_or_create(
            document=doc1, data_pool=self.dp,
            defaults={
                'source_file_id': 'short_id_doc1_excl',
                'source_file_name': doc1.label,
                'status': DocumentDataStatus.VERIFIED.name
            }
        )
        doc_data2, _ = DocumentData.objects.get_or_create(
            document=doc2, data_pool=self.dp,
            defaults={
                'source_file_id': 'short_id_doc2_excl',
                'source_file_name': doc2.label,
                'status': DocumentDataStatus.VERIFIED.name
            }
        )

        # Set up content hashes to be the same (duplicates)
        mock_transformed_data = [{'field1': 'abc', 'field2': 'xyz'}]
        with patch.object(Extraction, 'get_transformed_results', return_value=mock_transformed_data):
            doc_data1.content_hash = doc_data1.calculate_content_hash(
                deduplication_fields=self.packager.deduplication_content_fields)
            doc_data1.is_content_check_completed = True
            doc_data1.save()
            doc_data2.content_hash = doc_data2.calculate_content_hash(
                deduplication_fields=self.packager.deduplication_content_fields)
            doc_data2.is_content_check_completed = True
            doc_data2.save()

        self.assertEqual(doc_data1.content_hash, doc_data2.content_hash)
        self.assertIsNotNone(doc_data1.content_hash)

        # Set up duplicate relationship
        content_rel_type, _ = DocumentRelationshipType.objects.get_or_create(label='CONTENT_DUPLICATE',
                                                                             data_pool=self.dp)
        doc_data1.canonical_document_data = None
        doc_data1.save()
        doc_data2.canonical_document_data = doc_data1
        doc_data2.parent_file_id = doc_data1.source_file_id
        doc_data2.relationship_to_parent = content_rel_type.label
        doc_data2.save()

        DocumentRelationship.objects.create(
            source_document_data=doc_data1,
            target_document_data=doc_data2,
            relationship_type=content_rel_type,
            data_pool=self.dp
        )

        # Test prepare_packages
        from packager.tasks import prepare_packages
        from packager.models import PackageEntry
        from packager.pydantic_classes import PackageEntryStatus

        prepare_packages(self.packager.id)
        self.packager.refresh_from_db()
        package = self.packager.packages.order_by('-created_at').first()
        self.assertIsNotNone(package)

        # Verify exclusion behavior
        entry1 = PackageEntry.objects.get(package=package, document_data=doc_data1)
        entry2 = PackageEntry.objects.get(package=package, document_data=doc_data2)

        self.assertEqual(entry1.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.EXCLUDED_AS_DUPLICATE.name)


class DocumentDataContentHashTests(TransactionPackagerTestCase):
    """Tests for DocumentData.calculate_content_hash method - from test_document_data_content_hash.py"""

    def setUpTestData(self):
        """Set up test data for content hash tests."""
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(
            data_pool=self.dp,
            deduplication_content_fields=['field1', 'field2']
        )

    def test_calculate_content_hash_with_raw_values(self):
        """Test that content hash uses raw field values without normalization."""
        # Create a document and document data instance specifically for this test
        doc, _ = create_document(data_pool=self.dp, status='VERIFIED', label='test_raw_values.pdf')
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=self.dp,
            source_file_id='test_raw_id',
            source_file_name=doc.label
        )

        # Create training set and revision for extraction
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set Raw")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        # Create extraction with training revision
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=doc, data_pool=self.dp,
                                               training_revision=training_revision)

        test_data = [{'field1': '  value with spaces  ', 'field2': 'line1\nline2'}]

        with patch.object(Extraction, 'get_transformed_results', return_value=test_data):
            content_hash = doc_data.calculate_content_hash(self.packager.deduplication_content_fields)
            # Hash of {"field1": "  value with spaces  ", "field2": "line1\\nline2"}
            expected_hash = 'd38645a54a169e0ad0356ca492c05b7d'
            self.assertEqual(content_hash, expected_hash)

    def test_calculate_content_hash_with_missing_fields(self):
        """Test calculate_content_hash when required fields are missing."""
        # Create a document and document data instance specifically for this test
        doc, _ = create_document(data_pool=self.dp, status='VERIFIED', label='test_missing_fields.pdf')
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=self.dp,
            source_file_id='test_missing_id',
            source_file_name=doc.label
        )

        # Create training set and revision for extraction
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set Missing")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        # Create extraction with training revision
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=doc, data_pool=self.dp,
                                               training_revision=training_revision)

        test_data = [{'field1': 'value1', 'field3': 'value3'}]  # field2 is missing

        with patch.object(Extraction, 'get_transformed_results', return_value=test_data):
            content_hash = doc_data.calculate_content_hash(self.packager.deduplication_content_fields)
            # Hash of {"field1": "value1"}
            expected_hash = '121490e93ba9a3eab2f013e5ca3a6cc8'
            self.assertEqual(content_hash, expected_hash)

    def test_calculate_content_hash_with_non_string_values(self):
        """Test calculate_content_hash with non-string field values."""
        # Create a document and document data instance specifically for this test
        doc, _ = create_document(data_pool=self.dp, status='VERIFIED', label='test_non_string_values.pdf')
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=self.dp,
            source_file_id='test_non_string_id',
            source_file_name=doc.label
        )

        # Create training set and revision for extraction
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        # Create extraction with training revision
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=doc, data_pool=self.dp,
                                               training_revision=training_revision)

        test_data = [{
            'field1': 123,  # Integer
            'field2': True,  # Boolean
            'field3': {'nested': 'value'},  # Dictionary
            'field4': ['list', 'item'],  # List
            'field5': None  # None
        }]

        with patch.object(Extraction, 'get_transformed_results', return_value=test_data):
            content_hash = doc_data.calculate_content_hash(self.packager.deduplication_content_fields)
            # Hash of {"field1": 123, "field2": true}
            expected_hash = '62de2fa6e734b1ef11fa3c2b0fbb0a21'
            self.assertEqual(content_hash, expected_hash)

    def test_hash_deterministic_with_same_values(self):
        """Test that hash generation is deterministic with the same input values."""
        # Create a document and document data instance specifically for this test
        doc, _ = create_document(data_pool=self.dp, status='VERIFIED', label='test_deterministic.pdf')
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=self.dp,
            source_file_id='test_deterministic_id',
            source_file_name=doc.label
        )

        # Create training set and revision for extraction
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set Deterministic")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        # Create extraction with training revision
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=doc, data_pool=self.dp,
                                               training_revision=training_revision)

        test_data = [{'field1': 'value1', 'field2': 'value2'}]

        with patch.object(Extraction, 'get_transformed_results', return_value=test_data):
            # Mock extractions.latest to return our extraction
            with patch.object(Extraction.objects, 'latest', return_value=extraction):
                # Calculate hash twice with the same inputs
                hash1 = doc_data.calculate_content_hash(
                    deduplication_fields=['field1', 'field2']
                )

                hash2 = doc_data.calculate_content_hash(
                    deduplication_fields=['field1', 'field2']
                )

                # Hashes should be identical
                self.assertEqual(hash1, hash2)

    def test_hash_deterministic_regardless_of_field_order(self):
        """Test that hash generation is deterministic regardless of field order."""
        # Create a document and document data instance specifically for this test
        doc, _ = create_document(data_pool=self.dp, status='VERIFIED', label='test_field_order.pdf')
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=self.dp,
            source_file_id='test_field_order_id',
            source_file_name=doc.label
        )

        # Create training set and revision for extraction
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set Field Order")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        # Create extraction with training revision
        from extract.tests.util import create_extraction as util_create_extraction
        extraction, _ = util_create_extraction(document=doc, data_pool=self.dp,
                                               training_revision=training_revision)

        test_data = [{'field1': 'value1', 'field2': 'value2'}]

        with patch.object(Extraction, 'get_transformed_results', return_value=test_data):
            # Mock extractions.latest to return our extraction
            with patch.object(Extraction.objects, 'latest', return_value=extraction):
                # Calculate hash with different field order
                hash1 = doc_data.calculate_content_hash(
                    deduplication_fields=['field1', 'field2']
                )

                hash2 = doc_data.calculate_content_hash(
                    deduplication_fields=['field2', 'field1']
                )

                # Hashes should be identical despite different field order
                self.assertEqual(hash1, hash2)


class DuplicateDetectionContentHashTests(TransactionPackagerTestCase, TestCaseMixin):
    """Tests for content hash-based duplicate detection - from test_duplicate_detection_content_hash.py"""

    def setUp(self):
        # Clear any existing test data
        DocumentRelationship.objects.all().delete()
        DocumentData.objects.all().delete()

        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

        # Create relationship types
        for label, description in [
            ("MD5_DUPLICATE", "Documents with identical MD5 hashes"),
            ("LABEL_DUPLICATE", "Documents with identical labels"),
            ("CONTENT_DUPLICATE", "Documents with identical content")
        ]:
            DocumentRelationshipType.objects.get_or_create(
                label=label, data_pool=self.dp, defaults={'description': description}
            )

        self.doc1, _ = create_document(data_pool=self.dp, label="doc1.pdf", status="VERIFIED")
        self.doc2, _ = create_document(data_pool=self.dp, label="doc2.pdf", status="VERIFIED")

        self.doc_data1, _ = DocumentData.objects.get_or_create(document=self.doc1, data_pool=self.dp)
        self.doc_data1.source_file_id = 'test_doc1'
        self.doc_data1.source_file_name = self.doc1.label
        self.doc_data1.source_file_md5 = 'a_content_md5'
        self.doc_data1.status = "VERIFIED"
        self.doc_data1.is_content_check_completed = False  # Reset this flag for testing
        self.doc_data1.save()

        self.doc_data2, _ = DocumentData.objects.get_or_create(document=self.doc2, data_pool=self.dp)
        self.doc_data2.source_file_id = 'test_doc2'
        self.doc_data2.source_file_name = self.doc2.label
        self.doc_data2.source_file_md5 = 'a_content_md5'  # Same MD5 to test duplicate detection
        self.doc_data2.status = "VERIFIED"
        self.doc_data2.is_content_check_completed = False  # Reset this flag for testing
        self.doc_data2.save()

    def test_content_hash_based_duplicate_detection(self):
        """Test that the content hash-based duplicate detection works correctly."""
        mock_data = {'field1': 'value1', 'field2': 'value2'}
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)
        from extract.tests.util import create_extraction
        extraction1, _ = create_extraction(data_pool=self.dp, document=self.doc1,
                                           training_revision=training_revision,
                                           _raw_results={
                                               "field1": "Some text content",
                                               "field2": "More content"
                                           },
                                           )
        extraction2, _ = create_extraction(data_pool=self.dp, document=self.doc2,
                                           training_revision=training_revision,
                                           _raw_results={
                                               "field1": "Some text content",
                                               "field2": "More content"
                                           },
                                           )

        packager = create_packager(data_pool=self.dp, deduplication_content_fields=['field1', 'field2'])

        content_rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label="CONTENT_DUPLICATE",
            defaults={'description': "Documents with identical content", 'data_pool': self.dp}
        )

        # Set documents to VERIFIED status (required for content duplicate detection)
        from glynt_schemas.document.document_attributes import DocumentDataStatus
        self.doc1.status = DocumentDataStatus.VERIFIED.value
        self.doc1.save()
        self.doc2.status = DocumentDataStatus.VERIFIED.value
        self.doc2.save()

        def mock_get_transformed_side_effect(*args, **kwargs):
            return [mock_data]

        # Patch the methods required for the test
        with patch.object(Extraction, 'get_transformed_results',
                          side_effect=mock_get_transformed_side_effect,
                          autospec=True) as mock_get_transformed, \
                patch('packager.tasks.trigger_document_data_checks_task.delay') as mock_celery_task:
            # Verify the patch is working
            test_result = extraction1.get_transformed_results()
            self.assertEqual(test_result, [mock_data], "Patch is not working correctly")

            # Test content hash calculation directly
            hash1 = self.doc_data1.calculate_content_hash(['field1', 'field2'])
            hash2 = self.doc_data2.calculate_content_hash(['field1', 'field2'])

            # Both should have the same hash since they have identical content
            self.assertIsNotNone(hash1, "Content hash should not be None for doc_data1")
            self.assertIsNotNone(hash2, "Content hash should not be None for doc_data2")
            self.assertEqual(hash1, hash2, "Content hashes should be identical for identical content")

            # Calculate expected hash
            import hashlib
            import json
            expected_data = {'field1': 'value1', 'field2': 'value2'}
            expected_hash = hashlib.md5(json.dumps(expected_data, sort_keys=True).encode()).hexdigest()
            self.assertEqual(hash1, expected_hash, "Content hash should match expected value")

            # Set the content hashes manually for the relationship test
            self.doc_data1.content_hash = hash1
            self.doc_data1.is_content_check_completed = False  # Reset to allow re-checking
            self.doc_data1.save(update_fields=['content_hash', 'is_content_check_completed'])
            self.doc_data2.content_hash = hash2
            self.doc_data2.is_content_check_completed = False  # Reset to allow re-checking
            self.doc_data2.save(update_fields=['content_hash', 'is_content_check_completed'])

            # Clear any existing relationships to test content duplicate detection cleanly
            DocumentRelationship.objects.filter(data_pool=self.dp).delete()
            self.doc_data1.relationship_to_parent = None
            self.doc_data1.parent_file_id = None
            self.doc_data1.save(update_fields=['relationship_to_parent', 'parent_file_id'])
            self.doc_data2.relationship_to_parent = None
            self.doc_data2.parent_file_id = None
            self.doc_data2.save(update_fields=['relationship_to_parent', 'parent_file_id'])

            # Ensure doc_data1 has an earlier received_at time to make it the canonical document
            import datetime
            earlier_time = timezone.now() - datetime.timedelta(minutes=1)
            later_time = timezone.now()
            self.doc_data1.received_at = earlier_time
            self.doc_data1.save(update_fields=['received_at'])
            self.doc_data2.received_at = later_time
            self.doc_data2.save(update_fields=['received_at'])

            service = DuplicateDetectionService(self.doc_data2.pk)
            service.document_data.refresh_from_db()

            service.run_checks(['CONTENT'])
            self.doc_data1.refresh_from_db()
            self.doc_data2.refresh_from_db()

            self.assertTrue(
                DocumentRelationship.objects.filter(
                    source_document_data=self.doc_data1,
                    target_document_data=self.doc_data2,
                    relationship_type__label='CONTENT_DUPLICATE'
                ).exists(),
                "CONTENT_DUPLICATE relationship should be created between documents with identical content hashes"
            )

            self.assertEqual(self.doc_data2.relationship_to_parent, 'CONTENT_DUPLICATE')
            self.assertEqual(self.doc_data2.parent_file_id, str(self.doc_data1.obfuscated_id))

    def test_content_hash_with_different_values_not_duplicates(self):
        """Test that documents with different content are not detected as duplicates."""
        # Mock different transformed data for each document
        mock_data1 = {'field1': 'value1', 'field2': 'value2'}
        mock_data2 = {'field1': 'different', 'field2': 'values'}

        # Create actual extractions for the documents to avoid patching related managers
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(training_set=training_set, data_pool=self.dp)

        from extract.tests.util import create_extraction
        extraction1, _ = create_extraction(data_pool=self.dp, document=self.doc1,
                                           training_revision=training_revision)
        extraction2, _ = create_extraction(data_pool=self.dp, document=self.doc2,
                                           training_revision=training_revision)

        # Clear any existing relationships to ensure a clean state for content hash checks
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()
        self.doc_data1.refresh_from_db()
        self.doc_data1.relationship_to_parent = None
        self.doc_data1.parent_file_id = None
        self.doc_data1.content_hash = None
        self.doc_data1.is_content_check_completed = False
        self.doc_data1.save()

        self.doc_data2.refresh_from_db()
        self.doc_data2.relationship_to_parent = None
        self.doc_data2.parent_file_id = None
        self.doc_data2.content_hash = None
        self.doc_data2.is_content_check_completed = False
        self.doc_data2.save()

        # Create a real packager with deduplication fields instead of mocking
        create_packager(data_pool=self.dp, deduplication_content_fields=['field1', 'field2'])

        doc1_id = self.doc1.id
        doc2_id = self.doc2.id

        def mock_get_transformed_side_effect(*args, **kwargs):
            extraction_self = args[0] if args else None
            if extraction_self.document.id == doc1_id:
                return [mock_data1]
            elif extraction_self.document.id == doc2_id:
                return [mock_data2]
            return [{}]

        with patch.object(Extraction, 'get_transformed_results',
                          side_effect=mock_get_transformed_side_effect,
                          autospec=True):

            hash1 = self.doc_data1.calculate_content_hash(['field1', 'field2'])
            hash2 = self.doc_data2.calculate_content_hash(['field1', 'field2'])

            self.assertIsNotNone(hash1, "Content hash should not be None for doc_data1")
            self.assertIsNotNone(hash2, "Content hash should not be None for doc_data2")
            self.assertNotEqual(hash1, hash2, "Content hashes should be different for different content")

            # Run duplicate detection on both documents
            service1 = DuplicateDetectionService(self.doc_data1.pk)
            service1.run_checks(['CONTENT'])

            service2 = DuplicateDetectionService(self.doc_data2.pk)
            service2.run_checks(['CONTENT'])

            # Refresh from database
            self.doc_data1.refresh_from_db()
            self.doc_data2.refresh_from_db()

            # Content hashes should be different
            self.assertIsNotNone(self.doc_data1.content_hash)
            self.assertIsNotNone(self.doc_data2.content_hash)
            self.assertNotEqual(self.doc_data1.content_hash, self.doc_data2.content_hash)

            # No relationship should exist between the documents
            self.assertFalse(
                DocumentRelationship.objects.filter(
                    source_document_data=self.doc_data1,
                    target_document_data=self.doc_data2,
                    relationship_type__label='CONTENT_DUPLICATE'
                ).exists()
            )

            # doc_data2 should not have relationship metadata
            self.assertIsNone(self.doc_data2.relationship_to_parent)
            self.assertIsNone(self.doc_data2.parent_file_id)

    def test_content_hash_with_raw_values_preserves_differences(self):
        """Test that content hash with raw values preserves differences that normalization would hide."""
        # Mock data with differences only in whitespace/formatting
        mock_data1 = {'field1': 'value', 'field2': 'test'}
        mock_data2 = {'field1': '  value  ', 'field2': 'test'}  # Extra spaces

        # Create actual extractions for the documents to avoid patching related managers
        training_set, _ = create_training_set(data_pool=self.dp)
        training_revision, _ = create_training_revision(training_set=training_set, data_pool=self.dp)

        from extract.tests.util import create_extraction
        extraction1, _ = create_extraction(data_pool=self.dp, document=self.doc1,
                                           training_revision=training_revision)
        extraction2, _ = create_extraction(data_pool=self.dp, document=self.doc2,
                                           training_revision=training_revision)

        # Clear any existing relationships to ensure a clean state for content hash checks
        DocumentRelationship.objects.filter(data_pool=self.dp).delete()
        self.doc_data1.refresh_from_db()
        self.doc_data1.relationship_to_parent = None
        self.doc_data1.parent_file_id = None
        self.doc_data1.content_hash = None
        self.doc_data1.is_content_check_completed = False
        self.doc_data1.save()

        self.doc_data2.refresh_from_db()
        self.doc_data2.relationship_to_parent = None
        self.doc_data2.parent_file_id = None
        self.doc_data2.content_hash = None
        self.doc_data2.is_content_check_completed = False
        self.doc_data2.save()

        # Create a real packager with deduplication fields instead of mocking
        packager = create_packager(data_pool=self.dp, deduplication_content_fields=['field1', 'field2'])

        # Use a side effect function that returns different data based on the extraction's document
        doc1_id = self.doc1.id
        doc2_id = self.doc2.id

        def mock_get_transformed_side_effect(*args, **kwargs):
            # When patching an instance method, the first arg is 'self' (the extraction instance)
            extraction_self = args[0] if args else None
            if extraction_self.document.id == doc1_id:
                return [mock_data1]
            elif extraction_self.document.id == doc2_id:
                return [mock_data2]
            return [{}]

        with patch.object(Extraction, 'get_transformed_results',
                          side_effect=mock_get_transformed_side_effect,
                          autospec=True):

            # Test content hash calculation directly first
            hash1 = self.doc_data1.calculate_content_hash(['field1', 'field2'])
            hash2 = self.doc_data2.calculate_content_hash(['field1', 'field2'])

            self.assertIsNotNone(hash1, "Content hash should not be None for doc_data1")
            self.assertIsNotNone(hash2, "Content hash should not be None for doc_data2")
            self.assertNotEqual(hash1, hash2, "Content hashes should be different due to raw value preservation")

            # Run duplicate detection on both documents
            service1 = DuplicateDetectionService(self.doc_data1.pk)
            service1.run_checks(['CONTENT'])

            service2 = DuplicateDetectionService(self.doc_data2.pk)
            service2.run_checks(['CONTENT'])

            # Refresh from database
            self.doc_data1.refresh_from_db()
            self.doc_data2.refresh_from_db()

            # Content hashes should be different due to raw value preservation
            self.assertIsNotNone(self.doc_data1.content_hash)
            self.assertIsNotNone(self.doc_data2.content_hash)
            self.assertNotEqual(self.doc_data1.content_hash, self.doc_data2.content_hash)

            # No relationship should exist between the documents
            self.assertFalse(
                DocumentRelationship.objects.filter(
                    source_document_data__in=[self.doc_data1, self.doc_data2],
                    target_document_data__in=[self.doc_data1, self.doc_data2],
                    relationship_type__label='CONTENT_DUPLICATE'
                ).exists()
            )


class ContentHashAndDuplicatesIntegrationTests(TransactionPackagerTestCase):
    """Integration tests for content hash and duplicates - from test_content_hash_and_duplicates.py"""

    def setUp(self):
        super().setUp()
        self.user, _ = create_user()
        self.organization, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.organization, user=self.user)
        self.packager = create_packager(data_pool=self.dp)

    def _create_extraction_with_data(self, document, training_revision, data_dict):
        """Create extraction with test data."""
        extraction, _ = util_create_extraction(document=document, data_pool=self.dp,
                                               training_revision=training_revision)
        extraction._raw_results = data_dict
        extraction.save()
        return extraction

    def _create_document_data(self, document, source_file_id, status=None):
        """Create or update DocumentData with consistent defaults."""
        status = status or DocumentDataStatus.VERIFIED.name
        doc_data, created = DocumentData.objects.get_or_create(
            document=document,
            data_pool=self.dp,
            defaults={
                'source_file_id': source_file_id,
                'source_file_name': document.label,
                'status': status
            }
        )
        if not created:
            doc_data.source_file_id = source_file_id
            doc_data.source_file_name = document.label
            doc_data.status = status
            doc_data.save()
        return doc_data

    def _setup_content_hash(self, doc_data, deduplication_fields, mock_transformed_data):
        """Calculate and set content hash for DocumentData."""
        with patch.object(Extraction, 'get_transformed_results', return_value=mock_transformed_data):
            doc_data.content_hash = doc_data.calculate_content_hash(
                deduplication_fields=deduplication_fields)
            doc_data.is_content_check_completed = True
            doc_data.save()
        return doc_data.content_hash

    def _setup_duplicate_relationship(self, canonical_doc_data, duplicate_doc_data, rel_type_label='CONTENT_DUPLICATE'):
        """Set up duplicate relationship between two DocumentData objects."""
        rel_type, _ = DocumentRelationshipType.objects.get_or_create(
            label=rel_type_label, data_pool=self.dp)

        # Set canonical status
        canonical_doc_data.canonical_document_data = None
        canonical_doc_data.is_content_check_completed = True
        canonical_doc_data.save()

        duplicate_doc_data.canonical_document_data = canonical_doc_data
        duplicate_doc_data.parent_file_id = canonical_doc_data.source_file_id
        duplicate_doc_data.relationship_to_parent = rel_type_label
        duplicate_doc_data.is_content_check_completed = True
        duplicate_doc_data.save()

        # Clean up existing relationships
        DocumentRelationship.objects.filter(
            Q(source_document_data=canonical_doc_data) | Q(target_document_data=canonical_doc_data) |
            Q(source_document_data=duplicate_doc_data) | Q(target_document_data=duplicate_doc_data)
        ).delete()

        # Create the relationship
        DocumentRelationship.objects.create(
            source_document_data=canonical_doc_data,
            target_document_data=duplicate_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )
        return rel_type

    def _create_test_documents_with_extractions(self, doc_labels, extraction_data_list, training_revision):
        """Create multiple documents with extractions in one call."""
        documents = []
        doc_data_list = []

        for i, (label, data) in enumerate(zip(doc_labels, extraction_data_list)):
            doc, _ = create_document(data_pool=self.dp, label=label,
                                   status=DocumentDataStatus.VERIFIED.name)
            self._create_extraction_with_data(doc, training_revision, data)
            doc_data = self._create_document_data(doc, f'short_id_{i}_{label.split(".")[0]}')
            documents.append(doc)
            doc_data_list.append(doc_data)

        return documents, doc_data_list

    def _reset_content_hash_state(self, doc_data_list):
        """Reset content hash state for re-evaluation."""
        for doc_data in doc_data_list:
            doc_data.is_content_check_completed = False
            doc_data.content_hash = None
            doc_data.canonical_document_data = None
            doc_data.parent_file_id = None
            doc_data.relationship_to_parent = None
            doc_data.save(update_fields=[
                'is_content_check_completed', 'content_hash', 'canonical_document_data',
                'parent_file_id', 'relationship_to_parent'
            ])

    def _setup_mock_extraction_results(self, documents, extraction_data_list):
        """Setup mock for get_transformed_results with document-specific data."""
        def side_effect_for_get_transformed_results(*args, **kwargs):
            extraction_instance = args[0] if args else None
            if extraction_instance:
                for doc, data in zip(documents, extraction_data_list):
                    if extraction_instance.document_id == doc.id:
                        return [data]
            return []
        return side_effect_for_get_transformed_results



    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    @patch('packager.tasks.trigger_document_data_checks_task')
    @patch('extract.models.Extraction.get_transformed_results', autospec=True)
    def test_prepare_packages_includes_content_duplicates(self, mock_get_transformed_results, mock_trigger_checks_task,
                                                          mock_dds_run_checks):
        """Test that prepare_packages includes content duplicates when configured."""
        # Setup mocks
        mock_dds_run_checks.return_value = None
        mock_trigger_checks_task.return_value = None

        # Configure packager to include duplicates
        self.packager.exclude_duplicates_from_delivery = False
        self.packager.deduplication_content_fields = ["field1", "field2"]
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.save()

        # Setup test data
        training_set, _ = create_training_set(data_pool=self.dp, label="Test Training Set")
        training_revision, _ = create_training_revision(data_pool=self.dp, training_set=training_set)

        extraction_data = {'field1': 'abc', 'field2': 'xyz', 'field3': 'ignore'}
        docs, doc_data_list = self._create_test_documents_with_extractions(
            ["doc1.pdf", "doc2.pdf"], [extraction_data, extraction_data], training_revision)
        doc_data1, doc_data2 = doc_data_list

        # Setup content hashes
        mock_transformed_data = [{'field1': 'abc', 'field2': 'xyz'}]
        hash1 = self._setup_content_hash(doc_data1, self.packager.deduplication_content_fields, mock_transformed_data)
        hash2 = self._setup_content_hash(doc_data2, self.packager.deduplication_content_fields, mock_transformed_data)

        self.assertEqual(hash1, hash2)
        self.assertIsNotNone(hash1)

        # Setup duplicate relationship
        self._setup_duplicate_relationship(doc_data1, doc_data2)

        # Verify relationship setup
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()
        self.assertIsNone(doc_data1.canonical_document_data)
        self.assertEqual(doc_data2.canonical_document_data, doc_data1)

        # Test prepare_packages
        prepare_packages(self.packager.id)
        self.packager.refresh_from_db()
        package = self.packager.packages.order_by('-created_at').first()
        self.assertIsNotNone(package)

        # Verify package entries - both should be included
        entry1 = PackageEntry.objects.get(package=package, document_data=doc_data1)
        entry2 = PackageEntry.objects.get(package=package, document_data=doc_data2)

        self.assertEqual(entry1.status_in_package, PackageEntryStatus.INCLUDED.name)
        self.assertEqual(entry2.status_in_package, PackageEntryStatus.INCLUDED.name)

        # Verify final state
        doc_data2.refresh_from_db()
        self.assertEqual(doc_data2.canonical_document_data, doc_data1)
        self.assertEqual(doc_data2.parent_file_id, doc_data1.source_file_id)

    @patch('packager.services.duplicate_detection_service.DuplicateDetectionService.run_checks')
    @patch('packager.tasks.trigger_document_data_checks_task')
    @patch('extract.models.Extraction.get_transformed_results', autospec=True)
    def test_prepare_packages_varying_deduplication_fields(self, mock_get_transformed_results, mock_trigger_checks_task,
                                                           mock_dds_run_checks):
        """Test that changing deduplication fields affects which documents are considered duplicates."""
        # Setup mocks
        mock_dds_run_checks.return_value = None
        mock_trigger_checks_task.return_value = None
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.save()

        # Create documents with different field values
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )

        # Mock extraction data - same field1, different field2
        def side_effect_for_get_transformed_results(extraction_instance):
            if extraction_instance.document == doc1:
                return {'field1': 'same_value', 'field2': 'different1'}
            elif extraction_instance.document == doc2:
                return {'field1': 'same_value', 'field2': 'different2'}
            return {}

        mock_get_transformed_results.side_effect = side_effect_for_get_transformed_results

        # Test 1: With field1 only - should be duplicates
        self.packager.deduplication_content_fields = ['field1']
        self.packager.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Refresh and check - should have duplicates based on field1
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()

        # One should be marked as duplicate
        duplicate_count = sum([
            1 for dd in [doc_data1, doc_data2]
            if dd.relationship_to_parent == 'CONTENT_DUPLICATE'
        ])
        self.assertEqual(duplicate_count, 1)

        # Reset for next test
        doc_data1.relationship_to_parent = None
        doc_data1.parent_file_id = None
        doc_data1.content_hash = None
        doc_data1.save()
        doc_data2.relationship_to_parent = None
        doc_data2.parent_file_id = None
        doc_data2.content_hash = None
        doc_data2.save()
        self.packager.packages.all().delete()

        # Test 2: With both fields - should not be duplicates
        self.packager.deduplication_content_fields = ['field1', 'field2']
        self.packager.save()

        success = prepare_packages(self.packager.id)
        self.assertTrue(success)

        # Refresh and check - should not have duplicates based on both fields
        doc_data1.refresh_from_db()
        doc_data2.refresh_from_db()

        # Neither should be marked as duplicate
        self.assertIsNone(doc_data1.relationship_to_parent)
        self.assertIsNone(doc_data2.relationship_to_parent)


class TestSimulatePackageDuplicateAndHashFields(TransactionPackagerTestCase):
    """Tests for simulate package with duplicate and hash field functionality - from test_content_hash_and_duplicates.py"""

    @patch('packager.services.package_operations_service.PackageOperationsService.get_documents_for_simulation')
    @patch('extract.models.Extraction.get_transformed_results')
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_simulate_package_include_relationship_details(self, mock_get_document_relationships,
                                                           mock_get_transformed_results,
                                                           mock_get_documents_for_simulation):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-simulate-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock the service to return our test documents
        mock_get_documents_for_simulation.return_value = [doc_data1, doc_data2]

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Mock relationship details
        mock_get_document_relationships.return_value = {
            str(doc2.obfuscated_id): {
                'duplicate_of': doc_data1.document.obfuscated_id,
                'relationship_type': 'CONTENT_DUPLICATE'
            }
        }

        # Make request with include_relationship_details=True
        response = client.post(url, {
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify relationship details are included
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Find the duplicate document item
        doc2_item = next((item for item in items if item['document_id'] == doc2.obfuscated_id), None)
        self.assertIsNotNone(doc2_item)
        self.assertEqual(doc2_item.get('duplicate_of'), doc_data1.document.obfuscated_id)

    @patch('packager.services.package_operations_service.PackageOperationsService.get_documents_for_simulation')
    @patch('extract.models.Extraction.get_transformed_results')
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_simulate_package_exclude_relationship_details(self, mock_get_document_relationships,
                                                           mock_get_transformed_results,
                                                           mock_get_documents_for_simulation):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-simulate-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock the service to return our test documents
        mock_get_documents_for_simulation.return_value = [doc_data1, doc_data2]

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Make request with include_relationship_details=False
        response = client.post(url, {
            'include_relationship_details': False
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify relationship details are not included
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Find the duplicate document item
        doc2_item = next((item for item in items if item['document_id'] == doc2.obfuscated_id), None)
        self.assertIsNotNone(doc2_item)
        self.assertNotIn('duplicate_of', doc2_item)

        # Verify get_document_relationships was not called
        mock_get_document_relationships.assert_not_called()

    @patch('packager.services.package_operations_service.PackageOperationsService.get_documents_for_simulation')
    @patch('extract.models.Extraction.get_transformed_results')
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_simulate_package_exclude_duplicates_override(self, mock_get_document_relationships,
                                                          mock_get_transformed_results,
                                                          mock_get_documents_for_simulation):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-simulate-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Set packager to include duplicates by default
        self.packager.exclude_duplicates_from_delivery = False
        self.packager.save()

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock the service to return our test documents
        mock_get_documents_for_simulation.return_value = [doc_data1, doc_data2]

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Mock relationship details
        mock_get_document_relationships.return_value = {
            str(doc2.obfuscated_id): {
                'duplicate_of': doc_data1.document.obfuscated_id,
                'relationship_type': 'CONTENT_DUPLICATE'
            }
        }

        # Make request with exclude_duplicates_from_delivery=True override
        response = client.post(url, {
            'exclude_duplicates_from_delivery': True,
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify duplicates are excluded despite packager setting
        self.assertIn('data', data)
        items = data['data']

        # Should only include the parent document
        included_docs = [item for item in items if not item.get('is_duplicate', False)]
        duplicate_docs = [item for item in items if item.get('is_duplicate', False)]

        self.assertEqual(len(included_docs), 1)
        self.assertEqual(len(duplicate_docs), 1)

        # Verify the duplicate document has proper metadata
        doc_ids = [item['document_id'] for item in duplicate_docs]
        found = any(doc_id == doc2.obfuscated_id for doc_id in doc_ids)
        self.assertTrue(found, f"Expected to find {doc2.obfuscated_id} in duplicate_doc_ids: {doc_ids}")

    @patch('packager.services.package_operations_service.PackageOperationsService.get_documents_for_simulation')
    @patch('extract.models.Extraction.get_transformed_results')
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_simulate_package_include_duplicates_override(self, mock_get_document_relationships,
                                                          mock_get_transformed_results,
                                                          mock_get_documents_for_simulation):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-simulate-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Set packager to exclude duplicates by default
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock the service to return our test documents
        mock_get_documents_for_simulation.return_value = [doc_data1, doc_data2]

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Mock relationship details
        mock_get_document_relationships.return_value = {
            str(doc2.obfuscated_id): {
                'duplicate_of': doc_data1.document.obfuscated_id,
                'relationship_type': 'CONTENT_DUPLICATE'
            }
        }

        # Make request with exclude_duplicates_from_delivery=False override
        response = client.post(url, {
            'exclude_duplicates_from_delivery': False,
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify duplicates are included despite packager setting
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Both documents should be included
        doc_ids = [item['document_id'] for item in items]
        self.assertIn(doc1.obfuscated_id, doc_ids)
        self.assertIn(doc2.obfuscated_id, doc_ids)

        # Verify relationship details are included
        mock_get_document_relationships.assert_called_once()

    @patch('packager.services.package_operations_service.PackageOperationsService.get_documents_for_simulation')
    @patch('extract.models.Extraction.get_transformed_results', autospec=True)
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_simulate_package_varying_deduplication_fields_override(self, mock_get_document_relationships,
                                                                    mock_get_transformed_results,
                                                                    mock_get_documents_for_simulation):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-simulate-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Set packager deduplication fields
        self.packager.deduplication_content_fields = ['field1']
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.save()

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')
        doc3, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc3.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data3 = DocumentData.objects.create(
            document=doc3,
            source_file_id=str(doc3.obfuscated_id),
            source_file_md5='md5_3',
            source_file_name='doc3.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )

        # Mock the service to return our test documents
        mock_get_documents_for_simulation.return_value = [doc_data1, doc_data2, doc_data3]

        # Mock extraction results with different field values
        def side_effect_for_get_transformed_results(extraction_instance):
            if extraction_instance.document == doc1:
                return {'field1': 'same_value', 'field2': 'different1'}
            elif extraction_instance.document == doc2:
                return {'field1': 'same_value', 'field2': 'different2'}  # Same field1, different field2
            elif extraction_instance.document == doc3:
                return {'field1': 'different_value', 'field2': 'different3'}
            return {}

        mock_get_transformed_results.side_effect = side_effect_for_get_transformed_results

        # Mock relationship details - doc1 and doc2 are duplicates based on field1
        mock_get_document_relationships.return_value = {
            str(doc2.obfuscated_id): {
                'duplicate_of': doc_data1.document.obfuscated_id,
                'relationship_type': 'CONTENT_DUPLICATE'
            }
        }

        # Test 1: Override with different deduplication fields (field2)
        response = client.post(url, {
            'deduplication_content_fields': ['field2'],
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # With field2, no documents should be duplicates (all have different field2 values)
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 3)

        # All documents should be included (no duplicates based on field2)
        for item in items:
            self.assertFalse(item.get('is_duplicate', False))

        # Test 2: Override with both fields (field1 and field2)
        response = client.post(url, {
            'deduplication_content_fields': ['field1', 'field2'],
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # With both fields, no documents should be duplicates
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 3)

        # All documents should be included (no duplicates based on both fields)
        for item in items:
            self.assertFalse(item.get('is_duplicate', False))


class TestPreviewPackageDuplicateAndHashFields(TransactionPackagerTestCase):
    """Tests for preview package with duplicate and hash field functionality - from test_content_hash_and_duplicates.py"""

    @patch('extract.models.Extraction.get_transformed_results')
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_preview_package_include_relationship_details_override(self, mock_get_document_relationships,
                                                                   mock_get_transformed_results):
        client = APIClient()
        client.force_authenticate(user=self.user)
        url = get_api_path('v6:packager-preview-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Mock relationship details
        mock_get_document_relationships.return_value = {
            str(doc2.obfuscated_id): {
                'duplicate_of': doc_data1.document.obfuscated_id,
                'relationship_type': 'CONTENT_DUPLICATE'
            }
        }

        # Make request with include_relationship_details=True
        response = client.post(url, {
            'include_relationship_details': True
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify relationship details are included
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Find the duplicate document item
        item1 = next((item for item in items if item['document_id'] == doc1.obfuscated_id), None)
        item2 = next((item for item in items if item['document_id'] == doc2.obfuscated_id), None)

        self.assertIsNotNone(item1)
        self.assertIsNotNone(item2)

        # Verify relationship details
        self.assertNotIn('duplicate_of', item1)  # Parent document shouldn't have duplicate_of
        self.assertEqual(item2.get('duplicate_of'), doc_data1.document.obfuscated_id)
        mock_get_document_relationships.assert_called_once()

    @patch('packager.utils.duplicate_utils.get_document_relationships')
    @patch('extract.models.Extraction.get_transformed_results')
    def test_preview_package_exclude_relationship_details_override(self, mock_get_transformed_results,
                                                                   mock_get_document_relationships):
        client = APIClient()
        client.force_authenticate(user=self.user)
        url = get_api_path('v6:packager-preview-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Make request with include_relationship_details=False
        response = client.post(url, {
            'include_relationship_details': False
        }, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify relationship details are not included
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Find the duplicate document item
        item1 = next((item for item in items if item['document_id'] == doc1.obfuscated_id), None)
        item2 = next((item for item in items if item['document_id'] == doc2.obfuscated_id), None)

        self.assertIsNotNone(item1)
        self.assertIsNotNone(item2)

        # Verify no relationship details
        self.assertNotIn('duplicate_of', item1)
        self.assertNotIn('duplicate_of', item2)
        mock_get_document_relationships.assert_not_called()

    @patch('extract.models.Extraction.get_transformed_results', autospec=True)
    @patch('packager.utils.duplicate_utils.get_document_relationships')
    def test_preview_package_default_behavior(self, mock_get_document_relationships, mock_get_transformed_results):
        client = Client()
        client.login(username=self.user.username, password='12345')
        url = get_api_path('v6:packager-preview-package', self.dp.obfuscated_id,
                           obfuscated_id=self.packager.obfuscated_id)

        # Create test documents
        doc1, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc1.pdf')
        doc2, _ = create_document(data_pool=self.dp, status='VERIFIED', label='doc2.pdf')

        # Create DocumentData instances
        doc_data1 = DocumentData.objects.create(
            document=doc1,
            source_file_id=str(doc1.obfuscated_id),
            source_file_md5='md5_1',
            source_file_name='doc1.pdf',
            status=DocumentDataStatus.VERIFIED.name
        )
        doc_data2 = DocumentData.objects.create(
            document=doc2,
            source_file_id=str(doc2.obfuscated_id),
            source_file_md5='md5_2',
            source_file_name='doc2.pdf',
            status=DocumentDataStatus.VERIFIED.name,
            relationship_to_parent='CONTENT_DUPLICATE',
            parent_file_id=str(doc1.obfuscated_id)
        )

        # Mock extraction results
        mock_get_transformed_results.return_value = {'field1': 'value1', 'field2': 'value2'}

        # Make request without any overrides (default behavior)
        response = client.post(url, {}, content_type='application/json')

        self.assertEqual(response.status_code, 200)
        data = response.json()

        # Verify default behavior
        self.assertIn('data', data)
        items = data['data']
        self.assertEqual(len(items), 2)

        # Default should not include relationship details
        for item in items:
            self.assertNotIn('duplicate_of', item)

        mock_get_document_relationships.assert_not_called()
