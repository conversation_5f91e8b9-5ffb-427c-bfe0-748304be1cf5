from datetime import date

from django.test import TransactionTestCase

from organizations.tests.util import create_data_pool, create_organization
from packager.models import get_default_coc_report_fields
from packager.tests.util import create_packager


class PackagerCoCFieldsTests(TransactionTestCase):

    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)
        self.packager = create_packager(data_pool=self.dp)

    def test_packager_coc_report_date_fields(self):
        """Test the CoC report date fields on Packager."""
        # Set dates
        start_date = date(2023, 1, 1)
        end_date = date(2023, 12, 31)

        self.packager.coc_report_start_date = start_date
        self.packager.coc_report_end_date = end_date
        self.packager.save()

        # Refresh from DB and verify
        self.packager.refresh_from_db()
        self.assertEqual(self.packager.coc_report_start_date, start_date)
        self.assertEqual(self.packager.coc_report_end_date, end_date)

    def test_packager_exclude_duplicates_from_delivery(self):
        """Test the exclude_duplicates_from_delivery flag on Packager."""
        # Default should be True
        self.assertTrue(self.packager.exclude_duplicates_from_delivery)

        # Disable flag
        self.packager.exclude_duplicates_from_delivery = False
        self.packager.save()

        # Refresh from DB and verify
        self.packager.refresh_from_db()
        self.assertFalse(self.packager.exclude_duplicates_from_delivery)

    def test_packager_coc_report_fields_config(self):
        """Test the coc_report_fields_config list on Packager."""
        # Default should be the full list of available CoC fields
        expected_default_fields = get_default_coc_report_fields()
        self.assertEqual(self.packager.coc_report_fields_config, expected_default_fields)

        # Set fields to use in CoC report
        fields = ["source_file_name", "source_file_md5", "received_at"]
        self.packager.coc_report_fields_config = fields
        self.packager.save()

        # Refresh from DB and verify
        self.packager.refresh_from_db()
        self.assertEqual(self.packager.coc_report_fields_config, fields)

    def test_packager_with_all_coc_fields(self):
        """Test setting all Chain of Custody fields on Packager."""
        # Set all fields
        start_date = date(2023, 1, 1)
        end_date = date(2023, 12, 31)
        fields = ["source_file_name", "source_file_md5", "received_at"]

        self.packager.coc_report_start_date = start_date
        self.packager.coc_report_end_date = end_date
        self.packager.exclude_duplicates_from_delivery = True
        self.packager.coc_report_fields_config = fields
        self.packager.save()

        # Refresh from DB and verify
        self.packager.refresh_from_db()
        self.assertEqual(self.packager.coc_report_start_date, start_date)
        self.assertEqual(self.packager.coc_report_end_date, end_date)
        self.assertTrue(self.packager.exclude_duplicates_from_delivery)
        self.assertEqual(self.packager.coc_report_fields_config, fields)
