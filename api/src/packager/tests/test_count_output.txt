Total test files: 23
test_additive_filtering.py: 10 tests
test_append_coc_fields.py: 3 tests
test_coc_report_service.py: 9 tests
test_coc_utils.py: 2 tests
test_content_hash_and_duplicates.py: 11 tests
test_content_hashing.py: 8 tests
test_document_data_content_hash.py: 5 tests
test_duplicate_detection_content_hash.py: 3 tests
test_duplicate_detection_service.py: 10 tests
test_duplicate_utils.py: 3 tests
test_formatting_utils.py: 9 tests
test_models.py: 58 tests
test_naming_utils.py: 27 tests
test_package_creation_service.py: 17 tests
test_package_tasks.py: 19 tests
test_packager_coc_fields.py: 4 tests
test_performance_optimizations.py: 5 tests
test_pydantic_classes.py: 5 tests
test_relationship.py: 3 tests
test_signals.py: 7 tests
test_tasks.py: 26 tests
test_utils.py: 26 tests
test_views.py: 59 tests
Total test methods: 329
