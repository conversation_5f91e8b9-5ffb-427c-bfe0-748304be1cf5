from unittest.mock import patch, MagicMock

from django.db.models.signals import post_save
from django.test import TransactionTestCase
from glynt_schemas.document.document_attributes import DocumentDataStatus  # Already imported but good to ensure

from documents.models import Document
from documents.tests.util import create_document
from extract.models import Extraction
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData, DocumentRelationship, DocumentRelationshipType
from packager.signals import (
    handle_document_creation_for_coc,
    handle_document_update_for_coc,
    update_document_data_on_document_verification,
    update_document_data_on_extraction_change
)


class DocumentSignalTests(TransactionTestCase):

    def setUp(self):
        self.org, _ = create_organization()
        self.dp, _ = create_data_pool(organization=self.org)

    @patch('packager.services.stream_package_service.trigger_document_data_checks_task')
    def test_handle_document_creation_for_coc(self, mock_task):
        """Test DocumentData creation when a Document is created."""
        # Reset mock before document creation
        mock_task.delay.reset_mock()

        # Create a document
        doc = create_document(data_pool=self.dp, label="TestDoc", content_md5="md5_test")[0]

        # Check that DocumentData was created
        doc_data = DocumentData.objects.filter(document=doc).first()
        self.assertIsNotNone(doc_data)

        # Check initial values
        self.assertEqual(doc_data.data_pool, self.dp)

        # Since the signal uses transaction.on_commit(), we need to trigger pending commit hooks
        from django.db import transaction
        transaction.get_connection().run_and_clear_commit_hooks()

        # Check that task was enqueued
        mock_task.delay.assert_called_once_with(
            document_data_pk=doc_data.pk,
            check_types=["MD5", "LABEL"]
        )

    @patch('packager.signals.trigger_document_data_checks_task')
    def test_handle_document_update_for_coc_label(self, mock_task):
        """Test DocumentData update when Document label changes."""
        # Create document and document data
        doc = create_document(data_pool=self.dp, label="OldLabel", content_md5="md5_test")[0]
        doc_data = DocumentData.objects.get(document=doc)

        # Create a relationship (this part is not strictly necessary for testing the signal handler call,
        # but it's part of the original test setup, so keep it for now)
        rel_type = DocumentRelationshipType.objects.create(
            label="LABEL_DUPLICATE", data_pool=self.dp
        )
        other_doc = create_document(data_pool=self.dp)[0]
        other_doc_data = DocumentData.objects.get(document=other_doc)
        DocumentRelationship.objects.create(
            source_document_data=doc_data,
            target_document_data=other_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        mock_task.delay.reset_mock()  # Reset mock calls from document/relationship creation

        # Change the document label and save - this should trigger the signal
        doc.label = "NewLabel"
        doc.save(update_fields=['label'])  # Specify update_fields

        # Check that the task was enqueued by the signal handler
        mock_task.delay.assert_called_once()
        call_args = mock_task.delay.call_args[1]
        self.assertEqual(call_args['document_data_pk'], doc_data.pk)
        self.assertCountEqual(call_args['check_types'], ["CONTENT", "LABEL"])

        # Verify document data was updated properly by the signal handler
        doc_data.refresh_from_db()
        self.assertFalse(doc_data.is_label_check_completed)

    @patch('packager.signals.trigger_document_data_checks_task')
    def test_handle_document_update_for_coc_md5(self, mock_task):
        """Test DocumentData update when Document MD5 changes."""
        # Create document and document data
        doc = create_document(data_pool=self.dp, label="TestDoc", content_md5="old_md5")[0]
        doc_data = DocumentData.objects.get(document=doc)

        # Create a relationship
        rel_type = DocumentRelationshipType.objects.create(
            label="MD5_DUPLICATE", data_pool=self.dp
        )
        other_doc = create_document(data_pool=self.dp)[0]  # Replaced self.create_document
        other_doc_data = DocumentData.objects.get(document=other_doc)
        DocumentRelationship.objects.create(
            source_document_data=doc_data,
            target_document_data=other_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        mock_task.delay.reset_mock()  # Reset mock calls from document/relationship creation

        # Change the document MD5
        with patch('packager.signals.handle_document_update_for_coc'):
            doc.content_md5 = "new_md5"

            # Check that the signal handler was called
            # mock_handler.assert_called_once() # Removing this assertion as the signal might not trigger in this
            # test context

        # Now actually test the handler directly with document fields
        handle_document_update_for_coc(sender=Document, instance=doc, created=False,
                                       update_fields=['content_md5'])

        # Check that task was enqueued for MD5 check
        # Order doesn't matter for check_types since they are processed independently
        mock_task.delay.assert_called_once()
        call_args = mock_task.delay.call_args[1]
        self.assertEqual(call_args['document_data_pk'], doc_data.pk)
        self.assertCountEqual(call_args['check_types'], ["MD5", "CONTENT"])

        # Relationships should be deleted
        self.assertEqual(DocumentRelationship.objects.filter(
            source_document_data=doc_data,
            relationship_type=rel_type
        ).count(), 0)

    @patch('packager.signals.generate_document_content_hash')
    @patch('packager.signals.trigger_document_data_checks_task')
    def test_update_document_data_on_document_verification(self, mock_task, mock_hash_func):
        """Test DocumentData update when Document is verified."""
        # Disconnect the signal that creates DocumentData on Document creation
        post_save.disconnect(handle_document_creation_for_coc, sender=Document)

        # Create document
        doc = create_document(data_pool=self.dp, status="VERIFIED")[0]  # Ensure document is verified

        # Reconnect the signal
        post_save.connect(handle_document_creation_for_coc, sender=Document)

        # Manually create DocumentData as the signal was disconnected
        doc_data = DocumentData.objects.create(
            document=doc,
            data_pool=doc.data_pool,
            status="CREATED",  # Or appropriate initial status
            source_file_id=doc.obfuscated_id,
            source_file_name=doc.label,
            source_file_md5=doc.content_md5,
            received_at=doc.created_at,
            source_file_num_pages=doc.page_count,
            source_file_size=doc.filesize,
            source_file_detected_language=doc.language,
            is_md5_check_completed=False,
            is_label_check_completed=False,
            is_content_check_completed=False,
        )

        # Create a mock extraction
        extraction = MagicMock(spec=Extraction)
        extraction.data = {"field1": "value1"}
        extraction.document = doc
        extraction.test_content_hash = "test_content_hash"

        # Mock the hash function
        mock_hash_func.return_value = "test_content_hash"

        # Call the signal handler directly
        update_document_data_on_document_verification(
            sender=Document, instance=doc, verified_extraction=extraction
        )

        # Check that content hash was generated and saved
        doc_data.refresh_from_db()
        self.assertEqual(doc_data.content_hash, "test_content_hash")

        # Check that task was enqueued for content check
        mock_task.delay.assert_called_once_with(
            document_data_pk=doc_data.pk,
            check_types=["CONTENT"]
        )

    @patch('packager.signals.handle_document_creation_for_coc')
    @patch('packager.signals.generate_document_content_hash')
    @patch('packager.signals.trigger_document_data_checks_task')
    def test_update_document_data_on_extraction_change(self, mock_task, mock_hash_func, mock_creation_handler):
        """Test DocumentData update when Extraction changes."""
        # Create document and document data
        doc = create_document(data_pool=self.dp, status="VERIFIED")[0]
        doc_data = DocumentData.objects.get(document=doc)
        doc_data.content_hash = "old_hash"
        doc_data.is_md5_check_completed = True
        doc_data.is_label_check_completed = True
        doc_data.save()

        # Reset mock after all initial setup is complete

        # Create a relationship
        rel_type = DocumentRelationshipType.objects.create(
            label="CONTENT_DUPLICATE", data_pool=self.dp
        )
        other_doc = create_document(data_pool=self.dp)[0]  # Replaced self.create_document
        other_doc_data = DocumentData.objects.get(document=other_doc)
        DocumentRelationship.objects.create(
            source_document_data=doc_data,
            target_document_data=other_doc_data,
            relationship_type=rel_type,
            data_pool=self.dp
        )

        mock_task.delay.reset_mock()
        # Create a mock extraction
        extraction = MagicMock(spec=Extraction)
        extraction.document = doc
        extraction.status = "VERIFIED"

        # Mock the hash function
        mock_hash_func.return_value = "new_content_hash"

        # Call the signal handler directly
        update_document_data_on_extraction_change(
            sender=Extraction, instance=extraction
        )

        # Check that content hash was updated
        doc_data.refresh_from_db()
        self.assertEqual(doc_data.content_hash, "new_content_hash")

        # Check that relationships were deleted
        self.assertEqual(DocumentRelationship.objects.filter(
            source_document_data=doc_data,
            relationship_type=rel_type
        ).count(), 0)

        # Check that task was enqueued for content check
        mock_task.delay.assert_called_once_with(
            document_data_pk=doc_data.pk,
            check_types=["CONTENT"]
        )

    @patch('packager.signals.generate_document_content_hash')
    @patch('packager.signals.trigger_document_data_checks_task')
    def test_update_document_data_on_extraction_change_not_verified(self, mock_task, mock_hash_func):
        """Test no update when Extraction changes but document is not verified."""
        # Create document and document data
        doc = create_document(data_pool=self.dp, status="PENDING_REVIEW")[0]
        doc_data = DocumentData.objects.get(document=doc)
        doc_data.is_md5_check_completed = True
        doc_data.is_label_check_completed = True
        doc_data.save()
        doc_data.refresh_from_db()  # Ensure we have the latest state
        mock_task.delay.reset_mock()  # Reset mock calls from document creation

        # Create a mock extraction
        extraction = MagicMock(spec=Extraction)
        extraction.document = doc
        extraction.status = "PENDING_REVIEW"

        # Call the signal handler directly
        update_document_data_on_extraction_change(
            sender=Extraction, instance=extraction
        )

        # Check that no task was enqueued
        mock_task.delay.assert_not_called()
        mock_hash_func.assert_not_called()

    def test_handle_document_update_syncs_status_to_document_data(self):
        """Test DocumentData.status is updated when Document.status changes."""
        initial_doc_status = DocumentDataStatus.PENDING_REVIEW.name
        doc = create_document(data_pool=self.dp, label="StatusSyncDoc", status=initial_doc_status)[0]

        doc_data = DocumentData.objects.get(document=doc)

        different_status_for_dd = DocumentDataStatus.PENDING_CREATION.name
        if doc_data.status == initial_doc_status:  # If already in sync from creation signal
            doc_data.status = different_status_for_dd
            doc_data.save(update_fields=['status'])
            doc_data.refresh_from_db()

        self.assertNotEqual(doc_data.status, initial_doc_status,
                            f"Pre-condition: DocumentData status ({doc_data.status}) should be different from "
                            f"Document status ({initial_doc_status}) for this specific test part.")

        new_doc_status = DocumentDataStatus.VERIFIED.name
        self.assertNotEqual(initial_doc_status, new_doc_status, "Test setup: new status must be different.")

        doc.status = new_doc_status
        doc.save(update_fields=['status'])

        doc_data.refresh_from_db()

        self.assertEqual(doc_data.status, new_doc_status,
                         f"DocumentData status ({doc_data.status}) should have been updated to {new_doc_status} to "
                         f"match Document status.")
        self.assertEqual(doc.status, new_doc_status)  # Confirm document status is as expected
