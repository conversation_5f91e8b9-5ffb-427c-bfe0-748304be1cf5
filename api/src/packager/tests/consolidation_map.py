"""
Consolidation mapping for packager test optimization.

This module defines which test files should be consolidated together
and provides the roadmap for the test migration process.
"""

# Mapping of new consolidated files to the original files they will contain
CONSOLIDATION_PLAN = {
    'test_content_and_duplicates.py': [
        'test_content_hashing.py',
        'test_content_hash_and_duplicates.py', 
        'test_document_data_content_hash.py',
        'test_duplicate_detection_content_hash.py',
        'test_duplicate_detection_service.py'
    ],
    
    'test_utilities.py': [
        'test_formatting_utils.py',
        'test_naming_utils.py',
        'test_duplicate_utils.py',
        'test_utils.py'
    ],
    
    'test_coc_features.py': [
        'test_coc_report_service.py',
        'test_coc_utils.py',
        'test_append_coc_fields.py'
    ],
    
    'test_services.py': [
        'test_package_creation_service.py',
        'test_package_tasks.py'
    ]
}

# Files that will remain separate (not consolidated)
STANDALONE_FILES = [
    'test_models.py',           # 58 tests - too large, optimize in place
    'test_views.py',            # 59 tests - too large, optimize in place  
    'test_tasks.py',            # 26 tests - optimize in place
    'test_additive_filtering.py',    # 10 tests - specialized functionality
    'test_signals.py',          # 7 tests - specialized signal testing
    'test_relationship.py',     # 3 tests - specialized relationship testing
    'test_packager_coc_fields.py',  # 4 tests - specialized CoC configuration
    'test_performance_optimizations.py',  # 5 tests - performance testing
    'test_pydantic_classes.py'  # 5 tests - simple validation tests
]

# Expected test counts for validation
EXPECTED_TEST_COUNTS = {
    'test_content_and_duplicates.py': 37,  # 8+11+5+3+10
    'test_utilities.py': 65,               # 9+27+3+26
    'test_coc_features.py': 14,            # 9+2+3
    'test_services.py': 36                 # 17+19
}

# Base class recommendations for each consolidated file
BASE_CLASS_MAPPING = {
    'test_content_and_duplicates.py': 'TransactionPackagerTestCase',
    'test_utilities.py': 'FastPackagerTestCase',  # Most utility tests are fast
    'test_coc_features.py': 'TransactionPackagerTestCase',  # CoC needs transactions
    'test_services.py': 'TransactionPackagerTestCase'  # Services need transactions
}

# Special considerations for each consolidation
CONSOLIDATION_NOTES = {
    'test_content_and_duplicates.py': {
        'notes': 'All files deal with content hashing and duplicate detection',
        'signals_needed': True,
        'filesystem_needed': True,
        'special_setup': 'Requires signal disconnection for content hash tests'
    },
    
    'test_utilities.py': {
        'notes': 'Utility function tests, mostly pure functions',
        'signals_needed': False,
        'filesystem_needed': False,
        'special_setup': 'test_naming_utils may need BulkDataPackagerTestCase'
    },
    
    'test_coc_features.py': {
        'notes': 'Chain of Custody functionality',
        'signals_needed': True,
        'filesystem_needed': False,
        'special_setup': 'CoC event creation and reporting'
    },
    
    'test_services.py': {
        'notes': 'Business logic services',
        'signals_needed': True,
        'filesystem_needed': True,
        'special_setup': 'Package creation and task processing'
    }
}

# Class organization within consolidated files
CLASS_ORGANIZATION = {
    'test_content_and_duplicates.py': [
        'ContentHashingTests',           # from test_content_hashing.py
        'DocumentDataContentHashTests',  # from test_document_data_content_hash.py
        'DuplicateDetectionContentHashTests',  # from test_duplicate_detection_content_hash.py
        'DuplicateDetectionServiceTests',      # from test_duplicate_detection_service.py
        'ContentHashAndDuplicatesIntegrationTests'  # from test_content_hash_and_duplicates.py
    ],
    
    'test_utilities.py': [
        'FormattingUtilsTests',     # from test_formatting_utils.py
        'NamingUtilsTests',         # from test_naming_utils.py (may use BulkDataPackagerTestCase)
        'DuplicateUtilsTests',      # from test_duplicate_utils.py
        'GeneralUtilsTests'         # from test_utils.py
    ],
    
    'test_coc_features.py': [
        'CoCReportServiceTests',    # from test_coc_report_service.py
        'CoCUtilsTests',           # from test_coc_utils.py
        'CoCFieldAppendingTests'   # from test_append_coc_fields.py
    ],
    
    'test_services.py': [
        'PackageCreationServiceTests',  # from test_package_creation_service.py
        'PackageTasksTests'            # from test_package_tasks.py
    ]
}

# Import consolidation - common imports that will be needed
COMMON_IMPORTS = {
    'test_content_and_duplicates.py': [
        'from packager.tests.base import TransactionPackagerTestCase',
        'from packager.services.duplicate_detection_service import DuplicateDetectionService',
        'from packager.utils.content_hashing import generate_document_content_hash',
        'from packager.models import DocumentData, DocumentRelationship'
    ],
    
    'test_utilities.py': [
        'from packager.tests.base import FastPackagerTestCase, BulkDataPackagerTestCase',
        'from packager.utils.formatting_utils import format_data, format_json, format_csv',
        'from packager.utils.naming_utils import generate_filename',
        'from packager.utils.duplicate_utils import get_document_relationships'
    ],
    
    'test_coc_features.py': [
        'from packager.tests.base import TransactionPackagerTestCase',
        'from packager.services.coc_report_service import CoCReportService',
        'from packager.utils.coc_utils import create_coc_event',
        'from packager.models import CoCEvent, CoCEventType'
    ],
    
    'test_services.py': [
        'from packager.tests.base import TransactionPackagerTestCase',
        'from packager.services.package_creation_service import PackageCreationService',
        'from packager.tasks import prepare_packages, process_documents'
    ]
}

def get_consolidation_plan():
    """Return the complete consolidation plan."""
    return CONSOLIDATION_PLAN

def get_standalone_files():
    """Return list of files that should remain standalone."""
    return STANDALONE_FILES

def get_expected_test_count(new_file):
    """Get expected test count for a consolidated file."""
    return EXPECTED_TEST_COUNTS.get(new_file, 0)

def get_base_class_for_file(new_file):
    """Get recommended base class for a consolidated file."""
    return BASE_CLASS_MAPPING.get(new_file, 'FastPackagerTestCase')

def get_consolidation_notes(new_file):
    """Get special notes and requirements for a consolidated file."""
    return CONSOLIDATION_NOTES.get(new_file, {})

def get_class_organization(new_file):
    """Get the recommended class organization for a consolidated file."""
    return CLASS_ORGANIZATION.get(new_file, [])

def get_common_imports(new_file):
    """Get common imports needed for a consolidated file."""
    return COMMON_IMPORTS.get(new_file, [])

def validate_consolidation_plan():
    """Validate that the consolidation plan covers all files and has no duplicates."""
    all_original_files = []
    for new_file, original_files in CONSOLIDATION_PLAN.items():
        all_original_files.extend(original_files)
    
    # Check for duplicates
    duplicates = []
    seen = set()
    for file in all_original_files:
        if file in seen:
            duplicates.append(file)
        seen.add(file)
    
    return {
        'has_duplicates': len(duplicates) > 0,
        'duplicate_files': duplicates,
        'total_files_to_consolidate': len(all_original_files),
        'total_new_files': len(CONSOLIDATION_PLAN),
        'standalone_files_count': len(STANDALONE_FILES)
    }
