from packager.models import DocumentRelationshipType


def ensure_duplicate_relationship_type(data_pool):
    """
    Ensures the DUPLICATE relationship type exists for the given data pool.
    
    Args:
        data_pool: DataPool instance to create relationship type for
        
    Returns:
        DocumentRelationshipType instance
    """
    relationship_type, created = DocumentRelationshipType.objects.get_or_create(
            label = 'DUPLICATE',
            data_pool = data_pool,
            defaults = {
                'description': 'Indicates that documents are duplicates of each other'
            }
    )
    return relationship_type
