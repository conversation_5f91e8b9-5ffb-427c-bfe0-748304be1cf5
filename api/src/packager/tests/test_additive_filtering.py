from datetime import date, timed<PERSON><PERSON>
from unittest.mock import patch

from django.test import TransactionTestCase
from django.utils import timezone
from glynt_schemas.document.document_attributes import DocumentDataStatus
from glynt_schemas.extraction_batch.extraction_batch_attributes import ExtractionBatchVerificationStatus
from pyfakefs.fake_filesystem_unittest import TestCaseMixin

from documents.tests.util import create_document
from extract.tests.util import create_extraction_batch
from organizations.tests.util import create_data_pool, create_organization
from packager.models import DocumentData
from packager.tests.util import create_packager
from training.tests.util import create_training_ready_training_set, create_training_revision


class TestAdditiveFilteringLogic(TransactionTestCase, TestCaseMixin):
    """Test suite for the new additive filtering logic in packagers."""

    def setUp(self):
        """Set up test data."""
        self.org, _ = create_organization(label="coc_org")
        self.dp, _ = create_data_pool(organization=self.org)

        # Create test dates
        self.begin_date = date(2025, 1, 1)
        self.end_date = date(2025, 1, 31)
        self.outside_date = date(2024, 12, 31)

        # Create packager with date range
        self.packager = create_packager(
            data_pool=self.dp,
            packaging_begin_date=self.begin_date,
            packaging_end_date=self.end_date,
            document_status_filter=[DocumentDataStatus.VERIFIED.name],
            eb_status_filter=[ExtractionBatchVerificationStatus.VERIFIED.name],
            document_id_filter=[],
            eb_id_filter=[]
        )

        # Create test documents
        self.doc1, _ = create_document(data_pool=self.dp, label="Doc1")
        self.doc2, _ = create_document(data_pool=self.dp, label="Doc2")
        self.doc3, _ = create_document(data_pool=self.dp, label="Doc3")
        self.ts, self.ts_path = create_training_ready_training_set(data_pool=self.dp)
        self.tr, self.tr_path = create_training_revision(data_pool=self.dp, training_set=self.ts)
        self.eb, self.eb_path = create_extraction_batch(data_pool=self.dp, documents=[self.doc1],
                                                        training_revision=self.tr)

        self.eb.verification_status = ExtractionBatchVerificationStatus.VERIFIED.name
        self.eb.save()
        self.eb2, self.eb2_path = create_extraction_batch(data_pool=self.dp,
                                                          documents=[self.doc1, self.doc2, self.doc3],
                                                          training_revision=self.tr)
        self.eb2.verification_status = ExtractionBatchVerificationStatus.VERIFIED.name
        self.eb2.save()

        self.eb1, _ = create_extraction_batch(
            data_pool=self.dp,
            training_revision=self.tr,
            verification_status=ExtractionBatchVerificationStatus.VERIFIED.name
        )

        self.eb1.documents.add(self.doc2)
        self.eb1.save()

        with patch('django.utils.timezone.now', return_value=timezone.make_aware(
                timezone.datetime.combine(self.begin_date, timezone.datetime.min.time()))):
            self.doc_data1, _ = DocumentData.objects.get_or_create(
                document=self.doc1,
                data_pool=self.dp,
                defaults={
                    'source_file_id': self.doc1.obfuscated_id,
                    'source_file_name': self.doc1.label,
                    'status': DocumentDataStatus.VERIFIED.name
                }
            )

        with patch('django.utils.timezone.now', return_value=timezone.make_aware(
                timezone.datetime.combine(self.begin_date + timedelta(days=1), timezone.datetime.min.time()))):
            self.doc_data2, _ = DocumentData.objects.get_or_create(
                document=self.doc2,
                data_pool=self.dp,
                defaults={
                    'source_file_id': self.doc2.obfuscated_id,
                    'source_file_name': self.doc2.label,
                    'status': DocumentDataStatus.VERIFIED.name
                }
            )

        with patch('django.utils.timezone.now', return_value=timezone.make_aware(
                timezone.datetime.combine(self.outside_date, timezone.datetime.min.time()))):
            self.doc_data3, _ = DocumentData.objects.get_or_create(
                document=self.doc3,
                data_pool=self.dp,
                defaults={
                    'source_file_id': self.doc3.obfuscated_id,
                    'source_file_name': self.doc3.label,
                    'status': DocumentDataStatus.VERIFIED.name
                }
            )

    def test_date_range_filtering(self):
        """Test that only DocumentData within the date range are returned."""
        # Test with no other filters - should return empty since no specific filters are set
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()
        self.assertEqual(result.count(), 0, "Should return empty when no filters are specified")

    def test_document_status_filter_additive(self):
        """Test filtering by DocumentData status."""
        # Only set document status filter
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()

        # Should return doc_data1 and doc_data2 (both VERIFIED and within date range)
        # Should NOT return doc_data3 (VERIFIED but outside date range)
        self.assertEqual(result.count(), 2)
        self.assertIn(self.doc_data1, result)
        self.assertIn(self.doc_data2, result)
        self.assertNotIn(self.doc_data3, result)

    def test_extraction_batch_status_filter_additive(self):
        """Test filtering by ExtractionBatch status."""
        # Only set EB status filter
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = [ExtractionBatchVerificationStatus.VERIFIED.name]
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()

        # Should return doc_data2 (in VERIFIED EB and within date range)
        self.assertEqual(result.count(), 2)
        self.assertIn(self.doc_data2, result)
        # Verify the extraction batch has correct status
        self.assertEqual(self.eb1.verification_status, ExtractionBatchVerificationStatus.VERIFIED.name)

    def test_document_id_filter_additive(self):
        """Test filtering by specific document IDs."""
        # Only set document ID filter
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = [str(self.doc1.obfuscated_id)]
        self.packager.eb_id_filter = []
        self.packager.save()

        with patch('documents.models.Document.get_unobfuscated_id', return_value=self.doc1.id):
            result = self.packager.get_filtered_document_data_additive()

            # Should return doc_data1 (matches ID and within date range)
            self.assertEqual(result.count(), 1)
            self.assertIn(self.doc_data1, result)

    def test_extraction_batch_id_filter_additive(self):
        """Test filtering by specific extraction batch IDs."""
        # Only set EB ID filter
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = [str(self.eb1.obfuscated_id)]
        self.packager.save()

        with patch('extract.models.ExtractionBatch.get_unobfuscated_id', return_value=self.eb1.id):
            result = self.packager.get_filtered_document_data_additive()

            # Should return doc_data2 (in specified EB and within date range)
            self.assertEqual(result.count(), 1)
            self.assertIn(self.doc_data2, result)

    def test_multiple_filters_additive(self):
        """Test that multiple filters work additively (OR logic)."""
        # Set multiple filters
        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.eb_status_filter = [ExtractionBatchVerificationStatus.VERIFIED.name]
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()

        # Should return both doc_data1 (VERIFIED status) and doc_data2 (VERIFIED EB)
        self.assertEqual(result.count(), 2)
        self.assertIn(self.doc_data1, result)
        self.assertIn(self.doc_data2, result)

    def test_date_range_boundary_conditions(self):
        """Test that date range filtering works correctly at boundaries."""
        # Create new test documents specifically for boundary testing
        boundary_doc1, _ = create_document(data_pool=self.dp, label="BoundaryDoc1")
        boundary_doc2, _ = create_document(data_pool=self.dp, label="BoundaryDoc2")

        # Create DocumentData exactly on begin_date
        with patch('django.utils.timezone.now', return_value=timezone.make_aware(
                timezone.datetime.combine(self.begin_date, timezone.datetime.min.time()))):
            DocumentData.objects.create(
                document=boundary_doc1,
                data_pool=self.dp,
                source_file_id="begin_date_doc",
                source_file_name="Begin Date Doc",
                status=DocumentDataStatus.VERIFIED.name
            )

        # Create DocumentData exactly on end_date
        with patch('django.utils.timezone.now', return_value=timezone.make_aware(
                timezone.datetime.combine(self.end_date, timezone.datetime.min.time()))):
            DocumentData.objects.create(
                document=boundary_doc2,
                data_pool=self.dp,
                source_file_id="end_date_doc",
                source_file_name="End Date Doc",
                status=DocumentDataStatus.VERIFIED.name
            )

        self.packager.document_status_filter = [DocumentDataStatus.VERIFIED.name]
        self.packager.eb_status_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()

        # Should include both boundary dates
        result_ids = [dd.source_file_id for dd in result]
        self.assertIn("begin_date_doc", result_ids)
        self.assertIn("end_date_doc", result_ids)

    def test_no_filters_returns_empty(self):
        """Test that when no filters are specified, empty queryset is returned."""
        # Clear all filters
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = []
        self.packager.save()

        result = self.packager.get_filtered_document_data_additive()
        self.assertEqual(result.count(), 0)

    def test_invalid_document_id_handling(self):
        """Test that invalid document IDs are handled gracefully."""
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = ["invalid_id"]
        self.packager.eb_id_filter = []
        self.packager.save()

        # Mock get_unobfuscated_id to raise an exception
        with patch('documents.models.Document.get_unobfuscated_id', side_effect=Exception("Invalid ID")):
            result = self.packager.get_filtered_document_data_additive()
            # Should return empty due to no valid filters
            self.assertEqual(result.count(), 0)

    def test_invalid_eb_id_handling(self):
        """Test that invalid extraction batch IDs are handled gracefully."""
        self.packager.document_status_filter = []
        self.packager.eb_status_filter = []
        self.packager.document_id_filter = []
        self.packager.eb_id_filter = ["invalid_eb_id"]
        self.packager.save()

        # Mock get_unobfuscated_id to raise an exception
        with patch('extract.models.ExtractionBatch.get_unobfuscated_id', side_effect=Exception("Invalid EB ID")):
            result = self.packager.get_filtered_document_data_additive()
            # Should return empty due to no valid filters
            self.assertEqual(result.count(), 0)
