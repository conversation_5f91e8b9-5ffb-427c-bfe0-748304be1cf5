import base64
import io
import json  # Added import
import logging
import zipfile
from typing import Dict, List, Union

from django.http import FileResponse, HttpResponse  # Added HttpResponse

logger = logging.getLogger(__name__)

from packager.pydantic_classes import RenderedP<PERSON>ageItem, SourceFile, PackageRenderResult  # Added PackageRenderResult


class ProfilerCompatibleFileResponse(FileResponse):
    """FileResponse subclass that caches content for middleware and profiling tools."""

    def __init__(self, *args, **kwargs):
        self._cached_content = None
        # The first argument is expected to be the file-like object (original stream)
        self._original_stream = args[0] if args else None
        # Store filename for Content-Disposition header
        self._filename = kwargs.pop('filename', None)
        # Initialize FileResponse with an empty BytesIO; content will be managed by this class
        super().__init__(io.BytesIO(), *args[1:], **kwargs)

        # Set Content-Disposition header correctly here
        if self._filename and not self.has_header('Content-Disposition'):
            self['Content-Disposition'] = f'attachment; filename="{self._filename}"'

        if self._original_stream:
            self._cache_content()

    def _cache_content(self):
        """Reads content from the original stream and caches it."""
        if not self._original_stream:
            return

        try:
            self._original_stream.seek(0)
            self._cached_content = self._original_stream.read()
            # Set the streaming_content for the parent FileResponse
            self.streaming_content = io.BytesIO(self._cached_content)
            # Also ensure the file pointer for streaming_content is at the beginning
            self.streaming_content.seek(0)
        except AttributeError:
            logger.warning("Original stream does not support seeking")
        except Exception as e:
            logger.error(f"Error caching content: {str(e)}")

    @property
    def content(self):
        """Provide a .content attribute for compatibility with middleware like request-profiler."""
        if self._cached_content is None and self._original_stream:
            # Ensure content is cached if accessed directly before streaming_content is set up
            self._cache_content()
        return self._cached_content


def to_bytes(value: Union[str, bytes]) -> bytes:
    """Convert a string or bytes to bytes.
    
    Args:
        value: The string or bytes to convert
        
    Returns:
        The value as bytes
        
    Raises:
        TypeError: If value is not a string or bytes
    """
    if isinstance(value, str):
        return value.encode('utf-8')
    elif isinstance(value, bytes):
        return value
    else:
        raise TypeError(f"Expected string or bytes, got {type(value)}")


def create_package_zip(rendered_package_groups, filename=None, use_profiler_response=False,
                       errors_list=None, duplicate_findings_list=None):
    """
    Create a zip file containing the provided data.
    """
    if isinstance(rendered_package_groups, PackageRenderResult):
        rendered_package_groups = rendered_package_groups.rendered_items
    elif not isinstance(rendered_package_groups, list):
        logger.error(
            f"create_package_zip expected List[RenderedPackageItem] or PackageRenderResult, got "
            f"{type(rendered_package_groups)}")
        rendered_package_groups = []

    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
        for item in rendered_package_groups:
            file_name = item.filename
            content = item.content
            content_bytes = to_bytes(content) if content is not None else b''
            zip_file.writestr(file_name, content_bytes)
            try:
                source_files = item.source_files
                if source_files:
                    for source_file in source_files:
                        source_name = source_file.filename
                        if not source_name.startswith("source/"):
                            source_name = f"source/{source_name}"
                        source_content_bytes = to_bytes(source_file.content) if source_file.content is not None else b''
                        zip_file.writestr(source_name, source_content_bytes)
            except AttributeError:
                pass

        if errors_list:
            try:
                processed_errors = []
                for err in errors_list:
                    try:
                        if callable(err.dict):
                            processed_errors.append(err.dict())
                        else:
                            processed_errors.append(str(err))
                    except AttributeError:
                        if isinstance(err, dict):
                            processed_errors.append(err)
                        else:
                            processed_errors.append(str(err))

                errors_content = json.dumps(processed_errors, indent=2).encode('utf-8')
                zip_file.writestr("errors.json", errors_content)
            except Exception as e:
                logger.error(f"Error writing errors.json to zip: {str(e)}")

        if duplicate_findings_list:
            try:
                processed_duplicates = []
                for df in duplicate_findings_list:
                    try:
                        if callable(df.dict):
                            processed_duplicates.append(df.dict())
                        else:
                            processed_duplicates.append(str(df))
                    except AttributeError:
                        if isinstance(df, dict):
                            processed_duplicates.append(df)
                        else:
                            processed_duplicates.append(str(df))  
                    else:
                        processed_duplicates.append(str(df))
                duplicates_content = json.dumps(processed_duplicates, indent=2).encode('utf-8')
                zip_file.writestr("duplicate_findings.json", duplicates_content)
            except Exception as e:
                logger.error(f"Error writing duplicate_findings.json to zip: {str(e)}")

    zip_buffer.seek(0)
    zip_data = zip_buffer.getvalue()
    actual_filename = filename or "package.zip"

    if use_profiler_response:
        response = ProfilerCompatibleFileResponse(
            zip_buffer,
            content_type='application/zip',
            as_attachment=True,
            filename=actual_filename
        )
        if 'Content-Disposition' not in response:
            response['Content-Disposition'] = f'attachment; filename="{actual_filename}"'
    else:
        response = HttpResponse(zip_data, content_type='application/zip')
        response['Content-Disposition'] = f'attachment; filename="{actual_filename}"'

    response['Content-Length'] = str(len(zip_data))
    return response


def encode_binary_content_for_api(rendered_items: List[RenderedPackageItem]) -> List[Dict]:
    """Encode binary content to base64 for safe inclusion in API JSON responses.
    
    Args:
        rendered_items: List of rendered package items
        
    Returns:
        List of dictionaries with encoded content
    """
    encoded_items = []
    for item in rendered_items:
        encoded_item_dict = {"filename": item.filename}

        if isinstance(item.content, bytes):
            encoded_content = base64.b64encode(item.content).decode('ascii')
            encoded_item_dict["content"] = {'data': encoded_content, 'encoding': 'base64'}
        else:
            encoded_item_dict["content"] = item.content

        encoded_items.append(encoded_item_dict)

    return encoded_items


def decode_api_content_for_zip(encoded_items: List[Dict]) -> List[RenderedPackageItem]:
    """Decode base64 content from API for creating zip files.
    
    Args:
        encoded_items: List of dictionaries with encoded content

    Returns:
        List of RenderedPackageItem with decoded content
    """
    decoded_items = []

    def decode_content(content):
        if isinstance(content, dict) and content.get('encoding') == 'base64' and 'data' in content:
            return base64.b64decode(content['data'])
        if content == "binary content":
            return b"binary content"
        return content if content is not None else b''

    for item in encoded_items:
        if isinstance(item, dict):
            filename = item.get('filename')
            content = decode_content(item.get('content'))
            item_source_files = item.get('source_files', None)
        else:
            try:
                filename = item.filename
            except AttributeError:
                filename = None

            try:
                content_val = item.content
                content = decode_content(content_val)
            except AttributeError:
                content = None

            try:
                item_source_files = item.source_files
            except AttributeError:
                item_source_files = None
        source_files = []
        if item_source_files:
            for sf in item_source_files:
                if isinstance(sf, dict):
                    sf_filename = sf.get('filename')
                    sf_content = decode_content(sf.get('content'))
                    sf_content_type = sf.get('content_type', None)
                else:
                    try:
                        sf_filename = sf.filename
                    except AttributeError:
                        sf_filename = None

                    try:
                        sf_content = decode_content(sf.content)
                    except AttributeError:
                        sf_content = None

                    try:
                        sf_content_type = sf.content_type
                    except AttributeError:
                        sf_content_type = None
                source_files.append(SourceFile(filename=sf_filename, content=sf_content, content_type=sf_content_type))
        decoded_items.append(RenderedPackageItem(filename=filename, content=content, source_files=source_files))

    return decoded_items
