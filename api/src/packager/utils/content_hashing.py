import hashlib
import json
import logging
from typing import Optional

from extract.models import Extraction

logger = logging.getLogger(__name__)


def generate_document_content_hash(extraction: Optional[Extraction], fields: list) -> Optional[str]:
    """
    Generates a content hash for document data based on specified extraction fields.
    Args:
        extraction: The Extraction instance
        fields: List of field names to include in the hash.

    Returns:
        The SHA256 hash as a hexadecimal string (always 64 chars), or None if hashing is not possible.
    """
    try:
        if extraction is None or not fields:
            logger.warning(
                "generate_document_content_hash: Extraction is None or no fields provided. Returning hash of empty string.")
            return hashlib.sha256(b"").hexdigest()

        try:
            transformed_data = extraction.get_transformed_results()
        except Exception as e:
            logger.error(f"Error getting transformed results: {str(e)}. Returning hash of empty string.")
            transformed_data = None

        if not transformed_data or not isinstance(transformed_data, list) or len(transformed_data) == 0:
            logger.warning(
                f"No valid transformed data found for extraction {getattr(extraction, 'id', None)}. Falling back to extraction.data for hashing.")
            current_doc_data = getattr(extraction, 'data', {})
        else:
            current_doc_data = transformed_data[0]

        # Extract the field values from transformed data or .data
        field_values = {}
        for field in fields:
            if field in current_doc_data:
                value = current_doc_data[field]
                field_values[field] = value

        if not field_values:
            logger.warning(
                "generate_document_content_hash: No fields found in document data for hashing. Returning hash of empty string.")
            return hashlib.sha256(b"").hexdigest()

        try:
            sorted_field_values = json.dumps(field_values, sort_keys=True)
        except TypeError as te:
            logger.error(
                f"Error serializing field_values to JSON: {str(te)}. Field values: {field_values}. Returning None.")
            return None

        sha256_hash = hashlib.sha256(sorted_field_values.encode('utf-8')).hexdigest()
        return sha256_hash

    except Exception as e:
        logger.error(f"Error generating content hash: {str(e)}. Returning None.")
        return None
