# Re-export for patching in tests
import logging
from collections import defaultdict
from typing import Dict, List, Any, Optional

from glynt_schemas.packager.packager_attributes import PackagerGroupingOptions

logger = logging.getLogger(__name__)


def group_documents(
        document_data_list: List[Any],
        grouping_strategy: str,
        metadata_key: Optional[str] = None
) -> Dict[str, List[Any]]:
    """Group documents based on specified strategy.

    Args:
        document_data_list: List of document data objects
        grouping_strategy: Grouping strategy (schedule, metadata, document)
        metadata_key: Optional metadata key for metadata grouping

    Returns:
        Dictionary mapping group keys to lists of document data objects
    """
    grouped_data = defaultdict(list)

    if grouping_strategy == PackagerGroupingOptions.ALL_IN_ONE.name:
        grouped_data['all'] = document_data_list

    elif grouping_strategy == PackagerGroupingOptions.METADATA_MATCH_AND_MERGE.name and metadata_key:
        for doc_data in document_data_list:
            try:
                from packager.utils.naming_utils import \
                    get_single_metadata_value  # Local import to avoid circular dependency
                metadata_value = get_single_metadata_value(metadata_key, doc_data)
                group_key = str(metadata_value) if metadata_value is not None else 'default'
                grouped_data[group_key].append(doc_data)
            except Exception as e:
                logger.error(
                    f"{metadata_key}: {str(e)}")
                grouped_data['default'].append(doc_data)

    elif grouping_strategy == PackagerGroupingOptions.SPLIT_BY_DOC.name:
        for doc_data in document_data_list:
            document_obfuscated_id = None
            try:
                if doc_data.document:
                    document_obfuscated_id = doc_data.document.obfuscated_id
            except AttributeError:
                pass

            if document_obfuscated_id:
                grouped_data[document_obfuscated_id].append(doc_data)
            else:
                grouped_data['default'].append(doc_data)

    else:
        grouped_data['all'] = document_data_list

    group_counts = {key: len(docs) for key, docs in grouped_data.items()}
    return dict(grouped_data)


def perform_grouping(document_data_list: List[Any], packager_config) -> Dict[str, List[Any]]:
    """Perform document grouping based on packager configuration.

    Args:
        document_data_list: List of document data objects
        packager_config: Packager configuration

    Returns:
        Dictionary mapping group keys to lists of document data objects
    """
    grouping_strategy = packager_config.delivery_data_grouping_strategy
    metadata_key = packager_config.delivery_data_grouping_metadata_key

    return group_documents(document_data_list, grouping_strategy, metadata_key)
