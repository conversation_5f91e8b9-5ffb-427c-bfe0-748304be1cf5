import logging
import re
from typing import Optional, List, Dict, Any

from packager.pydantic_classes import RenderedPackageItem, DocumentError, PackageRenderResult, SourceFile
from packager.utils.duplicate_utils import apply_duplicate_flags
from packager.utils.formatting_utils import format_data

logger = logging.getLogger(__name__)

from django.apps import apps
from glynt_schemas.packager.packager_attributes import (PackagerFileNamingElementTypes,
                                                        PackagerDataOutputFormats, PackagerGroupingOptions)
from packager.pydantic_classes import (NamingConfig, PackagerConfig)
from packager.utils.id_utils import _generate_random_id


def get_package_model():
    """Get the Package model dynamically to avoid circular imports."""
    return apps.get_model('packager', 'Package')


def get_available_metadata_attributes() -> Dict[str, List[str]]:
    """Get available metadata attributes for filename generation.
    
    Returns:
        Dictionary mapping model names to lists of attributes
    """

    # TODO enhance glynt-schema with this format 
    model_map = {
        'document': 'documents.models.Document',
        'extraction_batch': 'extract.models.ExtractionBatch',
        'data_pool': 'organizations.models.DataPool',
        'organization': 'organizations.models.Organization'
    }

    attributes = {}
    for key, model_path in model_map.items():
        model_attrs = []
        model_parts = model_path.split('.')
        module_path, model_name = '.'.join(model_parts[:-1]), model_parts[-1]

        try:
            module = __import__(module_path, fromlist=[model_name])
            model_class = module.__dict__[model_name]

            for field in model_class._meta.get_fields():
                field_name = field.name
                if not field_name.startswith('_'):
                    model_attrs.append(f"{key}.{field_name}")

            if model_attrs:
                attributes[key] = model_attrs

        except (ImportError, KeyError) as e:
            logger.warning(f"Error getting attributes for {model_path}: {str(e)}")

    return attributes


def get_single_metadata_value(metadata_field: str, source_obj: Any, date_format: Optional[str] = None) -> str:
    """Get a single metadata value from a source object.
    
    Args:
        metadata_field: Metadata field in format 'model.field'
        source_obj: Source object to get value from
        date_format: Optional date format for datetime values
        
    Returns:
        String representation of the metadata value
        
    Raises:
        ValueError: If metadata_field format is invalid or field not found
    """
    if not metadata_field or not source_obj:
        raise ValueError(f"Invalid metadata field or source object: {metadata_field}")

    try:
        parts = metadata_field.split('.')
        if len(parts) < 2:
            raise ValueError(f"Invalid metadata field format: {metadata_field}. Expected format: 'model.field'")

        if parts[0] == 'document' and parts[1] == 'metadata' and len(parts) > 2:
            meta_key = parts[2]
            doc_meta = source_obj.metadata or {} if source_obj.metadata else {}
            value = doc_meta.get(meta_key, "")
            return str(value) if value is not None else ""

        is_document_instance = source_obj.__class__.__name__ == 'Document'

        if parts[0] == 'document' and len(parts) == 2 and is_document_instance:
            current_obj = source_obj
            final_field = parts[1]

        elif parts[0] == 'document_data' and is_document_instance:
            DocumentData = apps.get_model('packager', 'DocumentData')
            doc_data = DocumentData.objects.filter(document=source_obj).first()

            if not doc_data:
                logger.warning(f"No DocumentData found for {source_obj} when trying to get {metadata_field}")
                return ""

            current_obj = doc_data
            final_field = parts[1]
        else:
            current_obj = source_obj
            for part in parts[:-1]:
                try:
                    current_obj = getattr(current_obj, part)
                    if current_obj is None:
                        return ""
                except AttributeError:
                    raise ValueError(f"Could not find relationship {part} on {current_obj}")
            final_field = parts[-1]

        try:
            value = getattr(current_obj, final_field)
        except AttributeError:
            raise ValueError(f"Could not find field {final_field} on {current_obj}")

        from datetime import datetime
        if isinstance(value, datetime) and date_format:
            return value.strftime(date_format)
        if value is None:
            return ""
        if isinstance(value, (str, int, float, bool)) or value.__class__.__str__ != object.__str__:
            return str(value)
        return format_data(value, PackagerDataOutputFormats.JSON.name)

    except Exception as e:
        if isinstance(e, ValueError):
            raise
        logger.error(f"Error getting metadata value for {metadata_field}: {str(e)}")
        return ""


def _apply_fallback_with_extension(filename: str, fallback: str, extension: str) -> str:
    """Apply fallback value with extension if filename is empty or invalid.
    
    Args:
        filename: Original filename (may be empty)
        fallback: Fallback value to use if filename is empty
        extension: File extension to append (without dot)
        
    Returns:
        Final filename with extension
    """
    if not filename:
        filename = fallback

    filename = re.sub(r'[^\w\s.-]', '_', filename)
    filename = re.sub(r'\.+', '.', filename)

    common_extensions = ['.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.docx', '.xlsx', '.pptx', '.txt', '.json']
    for ext in common_extensions:
        if filename.lower().endswith(ext):
            filename = filename[:-len(ext)]
            break

    if extension:
        clean_extension = extension.lstrip('.')
        if not filename.lower().endswith(f".{clean_extension.lower()}"):
            filename = f"{filename}.{clean_extension}"

    return filename


def generate_filename(config: NamingConfig, extension: str, source_obj: Any,
                      fallback_name: str, is_processed_data_config: bool = False,
                      relationship_id: str = None, append_unique_id: bool = False) -> str:
    """Generate a filename based on configuration and source object.
    
    Args:
        config: NamingConfig instance with elements
        extension: File extension to append (without dot)
        source_obj: Source object to extract metadata from
        fallback_name: Fallback name to use if generation fails
        is_processed_data_config: Whether this is for processed data naming (affects extensions)
        relationship_id: Optional relationship ID to append
        append_unique_id: Whether to append a unique ID to ensure uniqueness
        
    Returns:
        Generated filename
    """
    if not config or not config.elements:
        return _apply_fallback_with_extension("", fallback_name, extension)

    filename_parts = []

    for element in config.elements:
        if element.type == PackagerFileNamingElementTypes.STRING_LITERAL.name:
            filename_parts.append(element.value or "")

        elif element.type == PackagerFileNamingElementTypes.METADATA.name:
            try:
                value = get_single_metadata_value(
                    element.value,
                    source_obj,
                    date_format=element.format if element.format else None
                )
                if value:
                    filename_parts.append(value)
            except ValueError as e:
                logger.warning(f"Error getting metadata value: {str(e)}")
                continue

        elif element.type == PackagerFileNamingElementTypes.DOCUMENT_DATA.name:
            if is_processed_data_config and source_obj:
                try:
                    from extract.models import Extraction
                    extractions = Extraction.objects.filter(document=source_obj)
                    if extractions.exists():
                        extraction = extractions.first()
                        results = extraction.get_transformed_results()
                        if results and isinstance(results, list) and len(results) > 0:
                            field_name = element.value
                            field_value = results[0].get(field_name, "")
                            if field_value:
                                filename_parts.append(str(field_value))
                        elif results and isinstance(results, dict):
                            field_name = element.value
                            field_value = results.get(field_name, "")
                            if field_value:
                                filename_parts.append(str(field_value))
                        elif results is not None:
                            logger.warning(
                                f"generate_filename: Unexpected transformed_data format for document "
                                f"{source_obj.obfuscated_id}: {type(results)}")

                except Exception as e:
                    logger.error(f"Error getting document data: {str(e)}")
                    continue

    separator = config.separator or "_"
    non_empty_parts = [part for part in filename_parts if part]

    if not non_empty_parts:
        logger.warning("No non-empty parts found for filename, using fallback")
        return _apply_fallback_with_extension("", fallback_name, extension)

    filename = separator.join(non_empty_parts)

    if config.max_length and len(filename) > config.max_length:
        logger.warning(f"Filename exceeds max length ({len(filename)} > {config.max_length}), using fallback")
        return _apply_fallback_with_extension("", fallback_name, extension)

    if is_processed_data_config and source_obj:
        try:
            label = source_obj.label
            if label:
                common_extensions = ['.pdf', '.png', '.jpg', '.jpeg', '.tiff', '.tif', '.docx', '.xlsx', '.pptx',
                                     '.txt',
                                     '.json']

                for i, part in enumerate(filename_parts):
                    for ext in common_extensions:
                        if ext in part.lower():
                            filename_parts[i] = part.replace(ext, '')

                label_part = label if label else ""
                if label_part:
                    for ext in common_extensions:
                        if label_part.lower().endswith(ext):
                            label_part = label_part[:-len(ext)]
                            break
        except AttributeError:
            # source_obj doesn't have a label attribute, skip processing
            pass

    if relationship_id:
        filename = f"{filename}_{relationship_id}"

    if append_unique_id:
        unique_id = _generate_random_id(6)
        filename = f"{filename}_{unique_id}"

    final_filename = _apply_fallback_with_extension(filename, fallback_name, extension)
    return final_filename


from datetime import datetime, date


def render_grouped_documents(
        grouped_docs: Dict[str, List[Any]],
        package_instance: Any,
        include_document_content: bool = False
) -> PackageRenderResult:
    """
    Render grouped documents according to packager config, optionally including document content.
    Includes configurable Chain of Custody fields in the output data.
    Args:
        grouped_docs: Dictionary of grouped documents (DocumentData instances).
        package_instance: The Package model instance or a mock object with a get_config method.
        include_document_content: Whether to include document content in the rendered output.
    Returns:
        PackageRenderResult containing rendered items and any errors.
    """
    result = []
    errors = []

    DocumentData = apps.get_model('packager', 'DocumentData')

    try:
        packager_config = package_instance.get_config()
        append_coc_fields = packager_config.append_coc_fields_to_data or False
        coc_fields_to_append = packager_config.coc_field_config or []

    except Exception as e:
        logger.error(f"Error getting packager config for rendering: {str(e)}")
        packager_config = PackagerConfig(
            output_format=PackagerDataOutputFormats.JSON.name,
            delivery_data_grouping_strategy=PackagerGroupingOptions.ALL_IN_ONE.name,
            processed_data_naming_config=NamingConfig(
                elements=[
                    {'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'error_fallback_group'}],
                separator="_",
                max_length=255
            ),
            source_file_naming_config=NamingConfig(
                elements=[
                    {'type': PackagerFileNamingElementTypes.STRING_LITERAL.name, 'value': 'error_fallback_source'}],
                separator="_",
                max_length=255
            )
        )
        append_coc_fields = packager_config.append_coc_fields_to_data or False
        coc_fields_to_append = packager_config.coc_field_config or []

    from django.db import models

    include_relationship_details = packager_config.include_relationship_details_in_data_export or False
    exclude_duplicates_config = packager_config.exclude_duplicates_from_delivery or False

    filtered_grouped_docs = {}
    for group_key, doc_datas in grouped_docs.items():
        if exclude_duplicates_config:
            temp_filtered_docs = []
            for doc_data_item in doc_datas:
                DocumentRelationship = apps.get_model('packager', 'DocumentRelationship')
                DocumentRelationshipType = apps.get_model('packager', 'DocumentRelationshipType')
                duplicate_types = DocumentRelationshipType.get_duplicate_types()
                is_duplicate = DocumentRelationship.objects.filter(
                    target_document_data_id=doc_data_item.id,
                    relationship_type__in=duplicate_types
                ).exists()
                if not is_duplicate:
                    temp_filtered_docs.append(doc_data_item)
                else:
                    doc_id_for_log = doc_data_item.document.obfuscated_id if doc_data_item.document else 'Unknown'
                    logger.debug(
                        f"Filtering out duplicate document {doc_id_for_log} (target in duplicate relationship)")
            filtered_grouped_docs[group_key] = temp_filtered_docs
        else:
            filtered_grouped_docs[group_key] = doc_datas
    grouped_docs = filtered_grouped_docs

    for group_name, docs in grouped_docs.items():
        # todo Ensure group_name is always a string or scalar for downstream usage
        if isinstance(group_name, list):
            group_name_str = "_".join(str(x) for x in group_name)
        else:
            group_name_str = str(group_name)
        group_data = {
            "group_name": group_name_str,
            "documents": []
        }
        source_files_for_group: List[SourceFile] = []

        current_docs_for_group = docs
        if exclude_duplicates_config:
            temp_filtered_docs = []
            for doc_data_item in docs:
                DocumentRelationship = apps.get_model('packager', 'DocumentRelationship')
                DocumentRelationshipType = apps.get_model('packager', 'DocumentRelationshipType')
                duplicate_types = DocumentRelationshipType.get_duplicate_types()
                is_duplicate = DocumentRelationship.objects.filter(
                    target_document_data_id=doc_data_item.id,
                    relationship_type__in=duplicate_types
                ).exists()
                if not is_duplicate:
                    temp_filtered_docs.append(doc_data_item)
                else:
                    try:
                        doc_id_for_log = doc_data_item.document.obfuscated_id
                    except AttributeError:
                        doc_id_for_log = 'Unknown'
                    logger.debug(
                        f"Filtering out duplicate document {doc_id_for_log} (target in duplicate relationship)")
            current_docs_for_group = temp_filtered_docs

        for doc_data in current_docs_for_group:  # Iterate over potentially filtered list
            # Refresh doc_data from DB to ensure latest values
            if isinstance(doc_data, models.Model) and doc_data.pk is not None:
                try:
                    doc_data = doc_data.__class__.objects.get(pk=doc_data.pk)
                except doc_data.__class__.DoesNotExist:
                    logger.error(
                        f"Failed to refresh DocumentData with pk: {doc_data.pk}, it no longer exists. Continuing with "
                        f"current instance.")
                except Exception as e:
                    logger.error(f"Error refreshing DocumentData with pk: {doc_data.pk}: {str(e)}")

            document = doc_data.document

            if not document:
                try:
                    doc_data_id_for_log = doc_data.obfuscated_id
                except AttributeError:
                    try:
                        doc_data_id_for_log = str(doc_data.id)
                    except AttributeError:
                        doc_data_id_for_log = 'Unknown ID'
                logger.error(
                    f"DocumentData object {doc_data_id_for_log} has no associated document.")
                errors.append(DocumentError(
                    document_id=doc_data_id_for_log,
                    error="Document not found for DocumentData"
                ))
                continue

            doc_entry = {
                "id": document.obfuscated_id,
                "label": document.label,
                "status": document.status,
                "created_at": document.created_at.isoformat() if document.created_at else None,
                "updated_at": document.updated_at.isoformat() if document.updated_at else None,
            }

            if include_document_content:
                try:
                    if document.file_content is not None:
                        source_file_naming_cfg = packager_config.source_file_naming_config
                        doc_extension = getattr(document, 'file_extension', '')

                        source_filename = generate_filename(
                            config=source_file_naming_cfg,
                            extension=doc_extension,
                            source_obj=document,
                            fallback_name=document.label or f"source_{document.obfuscated_id}",
                            is_processed_data_config=False,
                            append_unique_id=False
                        )

                        source_path = f"source/{source_filename}"
                        source_files_for_group.append(
                            SourceFile(
                                filename=source_path,
                                content=document.file_content,
                                content_type=getattr(document, 'content_type', 'application/octet-stream')
                            )
                        )
                    else:
                        logger.warning(
                            f"Document {document.obfuscated_id} has no file_content for source file inclusion.")
                except AttributeError:
                    logger.warning(f"Document {document.obfuscated_id} has no file_content attribute.")
                except Exception as e:
                    logger.exception(f"Error preparing source file for doc {document.obfuscated_id}: {str(e)}")
                    errors.append(DocumentError(document_id=document.obfuscated_id,
                                                error=f"Error preparing source file: {str(e)}"))

            if append_coc_fields:
                if document.obfuscated_id:
                    doc_entry['DocumentID'] = document.obfuscated_id

                if coc_fields_to_append:
                    coc_data_to_append = {}
                    for field_name in coc_fields_to_append:
                        try:
                            value = getattr(doc_data, field_name)
                            if isinstance(value, (datetime, date)):
                                coc_data_to_append[field_name] = value.isoformat()
                            else:
                                coc_data_to_append[field_name] = value
                        except AttributeError:
                            coc_data_to_append[field_name] = None
                    doc_entry.update(coc_data_to_append)

            try:
                if not isinstance(doc_data.id, (int, str)):
                    logger.error(
                        f"Invalid doc_data.id type: {type(doc_data.id)} for document "
                        f"{document.obfuscated_id}")
                    temp_duplicate_info = {
                        'is_duplicate': False,
                        'has_duplicates': False,
                        'duplicate_of': None
                    }
                else:
                    temp_duplicate_info = apply_duplicate_flags({}, doc_data, for_json=True)
            except Exception as e:
                logger.error(
                    f"Error applying duplicate flags for document {document.obfuscated_id}: "
                    f"{str(e)}")
                temp_duplicate_info = {
                    'is_duplicate': False,
                    'has_duplicates': False,
                    'duplicate_of': None
                }

            if include_relationship_details:
                doc_entry.update(temp_duplicate_info)
            elif exclude_duplicates_config and temp_duplicate_info.get('has_duplicates'):
                doc_entry['has_duplicates'] = temp_duplicate_info['has_duplicates']

            if include_document_content and doc_data.document:
                try:
                    from extract.models import Extraction
                    extractions = Extraction.objects.filter(document=doc_data.document)
                    if extractions.exists():
                        extraction = extractions.first()
                        doc_data_from_extraction = extraction.get_transformed_results()
                        if doc_data_from_extraction and isinstance(doc_data_from_extraction, list):
                            for item in doc_data_from_extraction:
                                if isinstance(item, dict):
                                    doc_entry.update(item)
                                else:
                                    logger.warning(
                                        f"render_grouped_documents: Invalid item type in transformed_data for "
                                        f"document {doc_data.document.obfuscated_id}: {type(item)}")
                        elif doc_data_from_extraction and isinstance(doc_data_from_extraction, dict):
                            doc_entry.update(doc_data_from_extraction)
                        elif doc_data_from_extraction is not None:
                            logger.warning(
                                f"render_grouped_documents: Unexpected transformed_data format for document "
                                f"{doc_data.document.obfuscated_id}: {type(doc_data_from_extraction)}")
                except Exception as e:
                    logger.error(
                        f"Error getting extraction data for document {doc_data.document.obfuscated_id}: {str(e)}")

            group_data["documents"].append(doc_entry)

        content = ""
        output_format = getattr(packager_config, 'output_format', PackagerDataOutputFormats.JSON.name)
        try:
            # Use 'json_data' key for CSV output, 'data' for others (like JSON)
            data_key = "json_data" if output_format == PackagerDataOutputFormats.CSV.name else "data"
            formatted_data = {data_key: group_data["documents"]}
            content = format_data(formatted_data, output_format)

            first_doc_data_in_group = current_docs_for_group[0] if current_docs_for_group else None
            document_context_for_filename = None
            if first_doc_data_in_group and first_doc_data_in_group.document:
                document_context_for_filename = first_doc_data_in_group.document

            generated_filename = "default_group_filename.json"
            try:
                generated_filename = package_instance.generate_processed_data_filename(
                    group_key=group_name_str,
                    document=document_context_for_filename
                )
            except AttributeError:
                logger.warning(f"render_grouped_documents: package_instance of type {type(package_instance)} "
                               f"does not have generate_processed_data_filename method. Using fallback filename for "
                               f"group {group_name}.")
                safe_group_name = re.sub(r'[^\w\s.-]', '_', group_name)
                generated_filename = f"{safe_group_name}.{output_format.lower()}"

            if not isinstance(generated_filename, str) or not generated_filename:
                logger.error(f"Generated filename for group {group_name} is not a string or is empty. Using fallback.")
                generated_filename = f"group_{group_name_str}.{output_format.lower()}"

            result.append(RenderedPackageItem(
                filename=str(generated_filename),
                content=content,
                source_files=source_files_for_group,
                format=output_format
            ))
        except Exception as e:
            current_group_name_for_error = group_name if 'group_name' in locals() else "UnknownGroup"
            logger.error(f"Error formatting data for group {current_group_name_for_error}: {str(e)}")
            errors.append(DocumentError(
                document_id=current_group_name_for_error,
                error=f"Error formatting data: {str(e)}"
            ))

    return PackageRenderResult(rendered_items=result, errors=errors)
