import csv
import io
import json
import logging
from typing import Dict, List, Optional, Union, Any

from glynt_schemas.packager.packager_attributes import PackagerDataOutputFormats

logger = logging.getLogger(__name__)


class CustomJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle special objects."""

    def default(self, obj):
        import uuid
        from datetime import datetime, date, time

        # Handle UUID objects first
        if isinstance(obj, uuid.UUID):
            return str(obj)

        try:
            # Handle datetime objects
            if isinstance(obj, (datetime, date, time)):
                return obj.isoformat()

            # Handle objects with to_dict/dict methods
            try:
                return obj.to_dict()
            except AttributeError:
                try:
                    return obj.dict()
                except AttributeError:
                    try:
                        return obj.__dict__
                    except AttributeError:
                        pass
            # Handle date/time objects
            from datetime import datetime, date, time
            if isinstance(obj, (datetime, date, time)):
                return obj.isoformat()
        except Exception:
            pass
        return super().default(obj)


def parse_date_with_formats(value, formats=None):
    """
    Attempt to parse a date string using a list of formats.
    Returns a date object if successful, otherwise raises ValueError.
    """
    from datetime import datetime, date
    if value is None or isinstance(value, date):
        return value
    if not isinstance(value, str):
        raise ValueError(f"Value must be a string or None, got {type(value)}")
    if formats is None:
        formats = ['%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d %H:%M:%S']
    for fmt in formats:
        try:
            return datetime.strptime(value, fmt).date()
        except ValueError:
            continue
    raise ValueError(f"Invalid date format: {value}. Use one of: {', '.join(formats)}")


class DataFormattingError(Exception):
    """Custom exception for data formatting errors."""
    pass


def flatten_dict(d: Dict, parent_key: str = '', sep: str = '__', preserve_arrays: bool = False) -> Dict:
    """Flattens a nested dictionary.

    Args:
        d: Dictionary to flatten
        parent_key: Parent key for nested dictionaries
        sep: Separator for keys in flattened dictionary
        preserve_arrays: If True, arrays are preserved as-is

    Returns:
        Flattened dictionary
    """
    items = []

    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k

        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep, preserve_arrays).items())
        elif isinstance(v, list) and preserve_arrays:
            items.append((new_key, v))
        elif isinstance(v, list):
            items.append((new_key, str(v)))
        else:
            items.append((new_key, v))

    return dict(items)


def format_json(data: Any) -> str:
    """Format data as JSON, adding a 'timestamp' field if missing and data is a dict.

    Args:
        data: Data to format as JSON

    Returns:
        JSON-formatted string
    """
    import datetime
    # Only add timestamp if dict is not empty and does not already have one
    if isinstance(data, dict) and data and 'timestamp' not in data:
        data = dict(data)  # avoid mutating input
        data['timestamp'] = datetime.datetime.utcnow().isoformat() + 'Z'
    return json.dumps(data, indent=2, cls=CustomJSONEncoder)


def format_csv(
        data: Dict,
        original_field_keys: Optional[List[str]] = None,
        force_string_values: bool = True
) -> str:
    """Format data as CSV.

    Args:
        data: Data to format as CSV
        original_field_keys: Optional list of field keys to maintain order
        force_string_values: Whether to force all values to strings

    Returns:
        CSV-formatted string

    Raises:
        DataFormattingError: If data doesn't contain 'json_data' key or it's empty
    """
    if 'json_data' not in data or not isinstance(data['json_data'], list):
        raise DataFormattingError("Data must contain 'json_data' key with a list value")

    if not data['json_data']:
        return ""

    flattened_rows = [flatten_dict(row, sep='.') for row in data['json_data']]

    all_field_names = set()
    for row in flattened_rows:
        all_field_names.update(str(key) for key in row.keys())

    if original_field_keys:
        field_names = [str(key) for key in original_field_keys]
        if not all_field_names.issubset(set(field_names)):
            for field in sorted(all_field_names - set(field_names)):
                field_names.append(field)
    else:
        field_names = sorted(all_field_names)

    output = io.StringIO()
    writer = csv.DictWriter(output, fieldnames=field_names, quoting=csv.QUOTE_NONNUMERIC)
    writer.writeheader()

    for row in flattened_rows:
        string_row = {}
        for field in field_names:
            string_row[field] = ""
        for k, v in row.items():
            string_key = str(k)
            if string_key in field_names:
                string_row[string_key] = str(v) if force_string_values and v is not None else v
        writer.writerow(string_row)

    return output.getvalue()


def format_data(
        data: Union[Dict, List],
        format_type: str,
        original_field_keys: Optional[List[str]] = None
) -> str:
    """Format data according to specified format type.

    Args:
        data: Data to format
        format_type: Format type (JSON or CSV)
        original_field_keys: Optional list of field keys for CSV

    Returns:
        Formatted data as string

    Raises:
        DataFormattingError: If format_type is invalid or formatting fails
    """
    try:
        if format_type == PackagerDataOutputFormats.JSON.name:
            return format_json(data)
        elif format_type == PackagerDataOutputFormats.CSV.name:
            if isinstance(data, list):
                data = {'json_data': data}
            return format_csv(
                data,
                original_field_keys=original_field_keys
            )
        else:
            raise DataFormattingError(f"Unsupported format: {format_type}")
    except Exception as e:
        if isinstance(e, DataFormattingError):
            logger.error(f"packager.formatting.data_error format_type={format_type} error={str(e)}")
            raise
        logger.error(f"packager.formatting.unexpected_error format_type={format_type} error={str(e)}")
        raise DataFormattingError(f"Error formatting data as {format_type}: {str(e)}") from e
