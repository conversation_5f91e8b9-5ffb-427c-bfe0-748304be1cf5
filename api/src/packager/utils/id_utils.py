import logging
import random
import string
from typing import Dict, List, Union

logger = logging.getLogger(__name__)


def _generate_random_id(size: int = 6, no_invalid_digits: bool = False) -> str:
    """Generate a random ID string.
    
    Args:
        size: Length of the ID
        no_invalid_digits: Whether to exclude special characters
        
    Returns:
        Random ID string
    """
    if no_invalid_digits:
        # Only alphanumeric characters
        chars = string.ascii_letters + string.digits
    else:
        # Include hyphen and underscore
        chars = string.ascii_letters + string.digits + "-_"

    generated_id = ''.join(random.choice(chars) for _ in range(size))
    return generated_id


def unobfuscate_ids(
        idmap: Dict[str, str],
        values: Union[Dict, List],
        key_prefix: str = '',
        key_suffix: str = '_id'
) -> Union[Dict, List]:
    """Convert obfuscated IDs to internal IDs in a nested structure.
    
    Args:
        idmap: Mapping from obfuscated IDs to internal IDs
        values: Dictionary or list containing obfuscated IDs
        key_prefix: Prefix for keys containing obfuscated IDs
        key_suffix: Suffix for keys containing obfuscated IDs
        
    Returns:
        Structure with obfuscated IDs replaced by internal IDs
    """
    if isinstance(values, dict):
        result = {}
        for key, value in values.items():
            # Check if key matches pattern and value is in idmap
            if key.startswith(key_prefix) and key.endswith(key_suffix) and value in idmap:
                result[key] = idmap[value]
            elif isinstance(value, (dict, list)):
                # Recursively process nested structures
                result[key] = unobfuscate_ids(idmap, value, key_prefix, key_suffix)
            else:
                result[key] = value
        return result

    elif isinstance(values, list):
        processed_list = [
            unobfuscate_ids(idmap, item, key_prefix, key_suffix) if isinstance(item, (dict, list)) else item
            for item in values]
        return processed_list

    return values
