"""
Utility functions for Chain of Custody (CoC) functionality.
"""
import logging
from typing import Optional, Dict, Any, Union, List

from django.contrib.auth import get_user_model
from django.db.models import Q

from documents.models import Document
from packager.models import (  # noqa
    CoCEvent, CoCEventType, DocumentRelationship, DocumentRelationshipType, Package
)
from packager.models import DocumentData  # Local import

User = get_user_model()
logger = logging.getLogger(__name__)


def create_coc_event(
        event_type_name: str,
        data_pool_id: int,
        document: Optional[Document] = None,
        package: Optional[Package] = None,
        user=None,
        details: Optional[Dict[str, Any]] = None
) -> CoCEvent:
    """
    Create a Chain of Custody event.

    Args:
        event_type_name: Name of the event type
        data_pool_id: ID of the data pool
        document: Optional related document
        package: Optional related package
        user: Optional user who triggered the event
        details: Optional details about the event

    Returns:
        The created CoCEvent instance
    """
    try:
        event_type, created = CoCEventType.objects.get_or_create(
            label=event_type_name,
            data_pool_id=data_pool_id
        )
    except Exception as e:
        logger.exception(
            f"Error during CoCEventType get_or_create for label='{event_type_name}', data_pool_id={data_pool_id}: {e}")
        raise

    return CoCEvent.objects.create(
        event_type=event_type,
        data_pool_id=data_pool_id,
        document=document,
        package=package,
        user=user,
        details=details or {}
    )


def create_document_relationship(
        source_doc_data: 'DocumentData',  # type: ignore # noqa
        target_doc_data: 'DocumentData',  # type: ignore # noqa
        relationship_type_name: str,
        data_pool_id: int,
        created_by=None,
        metadata: Optional[Dict[str, Any]] = None
) -> DocumentRelationship:
    """
    Create a relationship between two DocumentData instances.

    Args:
        source_doc_data: The source DocumentData instance
        target_doc_data: The target DocumentData instance
        relationship_type_name: Name of the relationship type
        data_pool_id: ID of the data pool
        created_by: Optional user who created the relationship
        metadata: Optional additional metadata

    Returns:
        The created DocumentRelationship instance.
    """
    relationship_type, _ = DocumentRelationshipType.objects.get_or_create(label=relationship_type_name,
                                                                          data_pool_id=data_pool_id)

    relationship, created = DocumentRelationship.objects.get_or_create(
        source_document_data=source_doc_data,
        target_document_data=target_doc_data,
        relationship_type=relationship_type,
        data_pool_id=data_pool_id,
        defaults={
            'created_by': created_by,
            'metadata': metadata or {}
        }
    )

    # If relationship already existed but we have new metadata, update it
    if not created and metadata:
        existing_metadata = relationship.metadata or {}
        existing_metadata.update(metadata)
        relationship.metadata = existing_metadata
        relationship.save(update_fields=['metadata', 'updated_at'])

    return relationship


def get_document_relationships(
        doc_data: 'DocumentData',  # type: ignore # noqa
        relationship_type_name: Optional[str] = None,
        as_source: bool = True,
        as_target: bool = True
) -> Union[Dict[str, list], list]:
    """
    Get all relationships for a DocumentData instance.
    This function is used for audit purposes to get explicit relationships.
    For deduplication and delivery logic, use the canonical_document_data pointer.
    
    Args:
        doc_data: The DocumentData instance to get relationships for
        relationship_type_name: Optional relationship type to filter by
        as_source: Include relationships where doc_data is the source
        as_target: Include relationships where doc_data is the target

    Returns:
        List or dict of DocumentRelationship instances
    """
    filters = {}
    if relationship_type_name:
        relationship_type = DocumentRelationshipType.objects.filter(label=relationship_type_name,
                                                                    data_pool_id=doc_data.data_pool_id).first()
        if relationship_type:
            filters['relationship_type'] = relationship_type

    source_relationships = []
    target_relationships = []

    if as_source:
        source_query = DocumentRelationship.objects.filter(source_document_data=doc_data, **filters)
        source_relationships = list(source_query)
        logger.debug(f"Found {len(source_relationships)} source relationships.")

    if as_target:
        target_query = DocumentRelationship.objects.filter(target_document_data=doc_data, **filters)
        target_relationships = list(target_query)
        logger.debug(f"Found {len(target_relationships)} target relationships.")

    if as_source and as_target:
        return {
            'source': source_relationships,
            'target': target_relationships
        }

    return source_relationships if as_source else target_relationships


def get_canonical_document_data(doc_data: 'DocumentData') -> 'DocumentData':  # type: ignore # noqa
    """
    Get the canonical DocumentData for a given DocumentData instance.

    Args:
        doc_data: The DocumentData instance to get the canonical for

    Returns:
        The canonical DocumentData (returns self if is the canonical or has no canonical)
    """
    return doc_data.canonical_document_data or doc_data


def get_duplicate_family(doc_data: 'DocumentData') -> List['DocumentData']:  # type: ignore # noqa
    """
    Get all DocumentData instances in the same duplicate family.

    Args:
        doc_data: The DocumentData instance to get the family for

    Returns:
        List of DocumentData instances in the same family (including self)
    """
    # Get the canonical document
    canonical = get_canonical_document_data(doc_data)

    # Get all duplicates of the canonical
    family = list(DocumentData.objects.filter(
        Q(canonical_document_data=canonical) | Q(id=canonical.id)
    ))

    return family
