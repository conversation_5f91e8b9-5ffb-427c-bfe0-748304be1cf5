#!/usr/bin/env python3
"""
Script to analyze duplicate tests in the packager tests directory.
"""

import os
import re
from collections import defaultdict, Counter
from pathlib import Path

def find_test_functions(file_path):
    """Extract test function names from a Python test file."""
    test_functions = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            # Find all test function definitions
            pattern = r'def\s+(test_[a-zA-Z0-9_]+)\s*\('
            matches = re.findall(pattern, content)
            test_functions.extend(matches)
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    return test_functions

def analyze_test_duplicates(tests_dir):
    """Analyze duplicate tests in the tests directory."""
    tests_dir = Path(tests_dir)
    
    # Dictionary to track which files contain which tests
    test_to_files = defaultdict(list)
    file_to_tests = defaultdict(list)
    
    # Find all test files
    test_files = list(tests_dir.glob('test_*.py'))
    
    print(f"Found {len(test_files)} test files:")
    for test_file in sorted(test_files):
        print(f"  - {test_file.name}")
    print()
    
    # Analyze each test file
    for test_file in test_files:
        test_functions = find_test_functions(test_file)
        file_to_tests[test_file.name] = test_functions
        
        for test_func in test_functions:
            test_to_files[test_func].append(test_file.name)
    
    # Count total tests
    all_tests = []
    for tests in file_to_tests.values():
        all_tests.extend(tests)
    
    test_counts = Counter(all_tests)
    
    print("=== TEST SUMMARY ===")
    print(f"Total test functions found: {len(all_tests)}")
    print(f"Unique test functions: {len(test_counts)}")
    print(f"Duplicate test functions: {len(all_tests) - len(test_counts)}")
    print()
    
    # Find duplicates
    duplicates = {test: files for test, files in test_to_files.items() if len(files) > 1}
    
    if duplicates:
        print("=== DUPLICATE TESTS ===")
        for test_name, files in sorted(duplicates.items()):
            print(f"{test_name}:")
            for file_name in sorted(files):
                print(f"  - {file_name}")
            print()
    else:
        print("=== NO DUPLICATES FOUND ===")
    
    # Show test counts per file
    print("=== TESTS PER FILE ===")
    for file_name, tests in sorted(file_to_tests.items()):
        print(f"{file_name}: {len(tests)} tests")
    print()
    
    return {
        'total_tests': len(all_tests),
        'unique_tests': len(test_counts),
        'duplicates': duplicates,
        'file_to_tests': dict(file_to_tests),
        'test_to_files': dict(test_to_files)
    }

def analyze_backup_comparison(tests_dir):
    """Compare current tests with backup tests."""
    tests_dir = Path(tests_dir)
    backup_dir = tests_dir.parent / 'backup_original'

    if not backup_dir.exists():
        print(f"Backup directory not found: {backup_dir}")
        return None

    print(f"=== BACKUP COMPARISON ===")
    print(f"Comparing current tests with backup in: {backup_dir}")

    # Get current tests (unique only)
    current_results = analyze_test_duplicates(tests_dir)
    current_unique_tests = set(current_results['test_to_files'].keys())

    # Get backup tests
    backup_files = list(backup_dir.glob('test_*.py'))
    backup_tests = set()
    backup_file_to_tests = defaultdict(list)
    total_backup_tests = 0

    print(f"\nFound {len(backup_files)} backup test files:")
    for backup_file in sorted(backup_files):
        print(f"  - {backup_file.name}")
        test_functions = find_test_functions(backup_file)
        backup_file_to_tests[backup_file.name] = test_functions
        backup_tests.update(test_functions)
        total_backup_tests += len(test_functions)

    print(f"\nBackup tests summary:")
    print(f"  Total backup tests (including duplicates): {total_backup_tests}")
    print(f"  Unique backup tests: {len(backup_tests)}")
    print(f"  Current unique tests: {len(current_unique_tests)}")

    # Find missing and extra tests
    missing_tests = backup_tests - current_unique_tests
    extra_tests = current_unique_tests - backup_tests

    print(f"\n=== MISSING TESTS (in backup but not current) ===")
    print(f"Count: {len(missing_tests)}")
    if missing_tests:
        for test in sorted(missing_tests):
            # Find which backup file contains this test
            for file_name, tests in backup_file_to_tests.items():
                if test in tests:
                    print(f"  - {test} (from {file_name})")
                    break
    else:
        print("  No missing tests!")

    print(f"\n=== EXTRA TESTS (in current but not backup) ===")
    print(f"Count: {len(extra_tests)}")
    if extra_tests:
        for test in sorted(extra_tests):
            # Find which current file contains this test
            for file_name, tests in current_results['file_to_tests'].items():
                if test in tests:
                    print(f"  - {test} (in {file_name})")
                    break
    else:
        print("  No extra tests!")

    # Calculate what we need to reach target
    # Use the total backup tests (329) as our target, not unique backup tests
    target_tests = total_backup_tests  # This should be 329
    current_after_dedup = len(current_unique_tests) - len(current_results['duplicates'])
    tests_to_add = target_tests - current_after_dedup

    print(f"\n=== TRANSFER CALCULATION ===")
    print(f"Target (backup) tests: {target_tests}")
    print(f"Current unique tests: {len(current_unique_tests)}")
    print(f"Current duplicates to remove: {len(current_results['duplicates'])}")
    print(f"Current after deduplication: {current_after_dedup}")
    print(f"Tests to transfer: {tests_to_add}")

    return {
        'backup_tests': backup_tests,
        'current_tests': current_unique_tests,
        'missing_tests': missing_tests,
        'extra_tests': extra_tests,
        'target_count': target_tests,
        'current_after_dedup': current_after_dedup,
        'tests_to_add': tests_to_add
    }

def main():
    """Main function."""
    # Get the tests directory
    script_dir = Path(__file__).parent
    tests_dir = script_dir / 'tests'

    if not tests_dir.exists():
        print(f"Tests directory not found: {tests_dir}")
        return

    print(f"Analyzing tests in: {tests_dir}")
    print("=" * 50)

    results = analyze_test_duplicates(tests_dir)

    # Summary
    print("=== CURRENT TESTS SUMMARY ===")
    print(f"Total test functions: {results['total_tests']}")
    print(f"Unique test functions: {results['unique_tests']}")
    print(f"Duplicate count: {results['total_tests'] - results['unique_tests']}")
    print(f"Number of duplicate test names: {len(results['duplicates'])}")

    if results['duplicates']:
        print(f"\nDuplicate test names:")
        for test_name in sorted(results['duplicates'].keys()):
            print(f"  - {test_name}")

    print("\n" + "=" * 50)

    # Compare with backup
    backup_comparison = analyze_backup_comparison(tests_dir)

    if backup_comparison:
        print(f"\n=== FINAL SUMMARY ===")
        print(f"Current total: {results['total_tests']}")
        print(f"Current unique: {results['unique_tests']}")
        print(f"Duplicates to remove: {len(results['duplicates'])}")
        print(f"After deduplication: {backup_comparison['current_after_dedup']}")
        print(f"Target (backup): {backup_comparison['target_count']}")
        print(f"Tests to transfer: {backup_comparison['tests_to_add']}")

if __name__ == '__main__':
    main()
