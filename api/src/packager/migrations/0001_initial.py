# Generated by Django 2.2.28 on 2025-06-01 06:27

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import glynt_api.util
import jsonfield.fields
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('documents', '0038_auto_20250422_2132'),
        ('extract', '0066_eb_buble_up_status'),
        ('organizations', '0019_choices_sync'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('status', models.CharField(choices=[('PENDING_CREATION', 'PENDING_CREATION'), ('CREATED', 'CREATED'), ('PENDING_REVIEW', 'PENDING_REVIEW'), ('REVIEWED', 'REVIEWED'), ('VERIFIED', 'VERIFIED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('TRANSMISSION_FAILED', 'TRANSMISSION_FAILED')], db_index=True, default='CREATED', max_length=64)),
                ('exclusion_reason', models.CharField(blank=True, max_length=255, null=True)),
                ('is_md5_check_completed', models.BooleanField(default=False)),
                ('is_label_check_completed', models.BooleanField(default=False)),
                ('is_content_check_completed', models.BooleanField(default=False)),
                ('last_check_triggered_at', models.DateTimeField(blank=True, null=True)),
                ('source_file_id', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('source_file_name', models.CharField(blank=True, db_index=True, max_length=255, null=True)),
                ('source_file_md5', models.CharField(blank=True, db_index=True, max_length=32, null=True)),
                ('source_file_num_pages', models.IntegerField(blank=True, null=True)),
                ('source_file_size', models.BigIntegerField(blank=True, null=True)),
                ('content_hash', models.CharField(blank=True, db_index=True, max_length=32, null=True)),
                ('received_at', models.DateTimeField(blank=True, db_index=True, null=True)),
                ('relationship_to_parent', models.CharField(blank=True, max_length=50, null=True)),
                ('parent_file_id', models.CharField(blank=True, help_text='Stores the obfuscated_id of the original document', max_length=255, null=True)),
                ('source_file_detected_language', models.CharField(blank=True, max_length=10, null=True)),
                ('location_id', models.CharField(blank=True, max_length=255, null=True)),
                ('open_issue_ticket_id', models.CharField(blank=True, max_length=255, null=True)),
                ('open_issue_subject', models.TextField(blank=True, null=True)),
                ('canonical_document_data', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='dupe_family', to='packager.DocumentData')),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_documentdata_related', related_query_name='packager_documentdatas', to='organizations.DataPool')),
                ('document', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='document_data', to='documents.Document')),
            ],
        ),
        migrations.CreateModel(
            name='Package',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('status', models.CharField(choices=[('CREATED', 'CREATED'), ('PROCESSING', 'PROCESSING'), ('IDLE', 'IDLE'), ('VERIFIED', 'VERIFIED'), ('FROZEN', 'FROZEN'), ('RENDER_SUCCESSFUL', 'RENDER_SUCCESSFUL'), ('RENDER_FAILED', 'RENDER_FAILED'), ('PROCESSING_SUCCESSFUL', 'PROCESSING_SUCCESSFUL'), ('PROCESSING_FAILED', 'PROCESSING_FAILED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('TRANSMISSION_FAILED', 'TRANSMISSION_FAILED'), ('DOCUMENT_ADDED', 'DOCUMENT_ADDED'), ('DOCUMENT_REMOVED', 'DOCUMENT_REMOVED'), ('EXTRACTION_BATCH_ADDED', 'EXTRACTION_BATCH_ADDED'), ('EXTRACTION_BATCH_REMOVED', 'EXTRACTION_BATCH_REMOVED'), ('TRANSFORMED_URL_ADDED', 'TRANSFORMED_URL_ADDED'), ('TRANSFORMED_URL_REMOVED', 'TRANSFORMED_URL_REMOVED')], default='CREATED', max_length=64)),
                ('last_viewed', models.DateTimeField(blank=True, help_text='Timestamp of last preview/delivery', null=True)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_package_related', related_query_name='packager_packages', to='organizations.DataPool')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Packager',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('label', models.CharField(blank=True, default=uuid.uuid4, max_length=255, validators=[glynt_api.util.validate_non_printable_characters, glynt_api.util.validate_whitespaces])),
                ('packager_status', models.CharField(choices=[('CREATED', 'CREATED'), ('PACKAGE_CREATED', 'NEW_PACKAGE_CREATED'), ('IDLE', 'IDLE'), ('PROCESSING', 'PROCESSING'), ('PROCESSING_SUCCESSFUL', 'PROCESSING_SUCCESSFUL'), ('PROCESSING_FAILED', 'PROCESSING_FAILED'), ('UNVERIFIED', 'UNVERIFIED'), ('VERIFIED', 'VERIFIED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('TRANSMISSION_FAILED', 'TRANSMISSION_FAILED')], default='CREATED', max_length=64)),
                ('packager_schedule_type', models.CharField(choices=[('CRON_SCHEDULE', 'CRON_SCHEDULE'), ('MANUAL', 'MANUAL'), ('STREAM', 'STREAM')], default='MANUAL', max_length=64)),
                ('schedule_cron_expression', models.CharField(blank=True, default='0 * * * *', max_length=20)),
                ('last_run', models.DateTimeField(blank=True, default=django.utils.timezone.now, null=True)),
                ('last_package_path', models.CharField(blank=True, max_length=64, null=True)),
                ('document_status_filter', jsonfield.fields.JSONField(blank=True, default=['VERIFIED'], null=True)),
                ('eb_status_filter', jsonfield.fields.JSONField(blank=True, default=['VERIFIED'], null=True)),
                ('document_id_filter', jsonfield.fields.JSONField(blank=True, default=list, null=True)),
                ('eb_id_filter', jsonfield.fields.JSONField(blank=True, default=list, null=True)),
                ('coc_report_start_date', models.DateField(blank=True, null=True)),
                ('coc_report_end_date', models.DateField(blank=True, null=True)),
                ('coc_report_fields_config', jsonfield.fields.JSONField(blank=True, default=list, null=True)),
                ('exclude_duplicates_from_delivery', models.BooleanField(default=False, help_text='When enabled, documents identified as duplicates will be excluded from package delivery')),
                ('append_coc_fields_to_data', models.BooleanField(default=False)),
                ('coc_field_config', jsonfield.fields.JSONField(default=list, help_text='List of CoC fields to append to output data')),
                ('deduplication_content_fields', jsonfield.fields.JSONField(blank=True, default=list, help_text='List of fields to consider when checking for content duplicates', null=True)),
                ('include_source_files_in_delivery', models.BooleanField(default=False)),
                ('include_relationship_details_in_data_export', models.BooleanField(default=False, help_text='Controls inclusion of relationship information (including duplicates) in data exports')),
                ('output_format', models.CharField(choices=[('JSON', 'json'), ('CSV', 'csv')], default='JSON', max_length=64)),
                ('delivery_schedule_type', models.CharField(choices=[('AUTOMATIC', 'Automatic'), ('MANUAL', 'Manual')], default='MANUAL', max_length=64)),
                ('delivery_data_grouping_strategy', models.CharField(choices=[('ALL_IN_ONE', 'ALL_IN_ONE'), ('METADATA_MATCH_AND_MERGE', 'METADATA_MATCH_AND_MERGE'), ('SPLIT_BY_DOC', 'SPLIT_BY_DOC')], default='ALL_IN_ONE', max_length=64)),
                ('delivery_data_grouping_metadata_key', models.CharField(default='document.target_training_set', max_length=64)),
                ('s3_bucket', models.CharField(default='default bucket', max_length=64)),
                ('s3_prefix', models.CharField(default='s3_prefix', max_length=64)),
                ('prefix', models.CharField(default='default_router_prefix', max_length=64)),
                ('region', models.CharField(default='us-west-1', max_length=64)),
                ('source_file_naming_config', jsonfield.fields.JSONField(default={'elements': [{'type': 'STRING_LITERAL', 'value': 'GLYNT'}, {'type': 'METADATA', 'value': 'document.id'}, {'format': '%Y-%m-%d', 'type': 'METADATA', 'value': 'document.created_at'}], 'max_length': 100, 'separator': '-'})),
                ('processed_data_naming_config', jsonfield.fields.JSONField(default={'elements': [{'type': 'STRING_LITERAL', 'value': 'DOCDATA'}, {'type': 'METADATA', 'value': 'data_pool.id'}], 'max_length': 100, 'separator': '_'})),
                ('packaging_begin_date', models.DateField(default=datetime.date(2025, 1, 1), help_text='Start date for packaging document data filter')),
                ('packaging_end_date', models.DateField(default=django.utils.timezone.now, help_text='End date for packaging document data filter (defaults to current date)')),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_packager_related', related_query_name='packager_packagers', to='organizations.DataPool')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='PackageEntry',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('status_in_package', models.CharField(choices=[('INCLUDED', 'INCLUDED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('EXCLUDED', 'EXCLUDED'), ('EXCLUDED_AS_DUPLICATE', 'EXCLUDED_AS_DUPLICATE'), ('EXCLUDED_BY_STATUS', 'EXCLUDED_BY_STATUS'), ('EXCLUDED_BY_CONFIG', 'EXCLUDED_BY_CONFIG'), ('PENDING', 'PENDING')], default='INCLUDED', max_length=64)),
                ('filename_in_package', models.CharField(blank=True, max_length=64, null=True)),
                ('exclusion_reason', models.CharField(blank=True, max_length=64, null=True)),
                ('delivered_at', models.DateTimeField(blank=True, null=True)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_packageentry_related', related_query_name='packager_packageentrys', to='organizations.DataPool')),
                ('document_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='package_entries', to='packager.DocumentData')),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='package_entries', to='packager.Package')),
            ],
        ),
        migrations.AddField(
            model_name='package',
            name='packager',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='packages', to='packager.Packager'),
        ),
        migrations.CreateModel(
            name='ExtractionBatchData',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('status', models.CharField(choices=[('NO_DOCUMENTS', 'NO_DOCUMENTS'), ('UNVERIFIED', 'UNVERIFIED'), ('PARTIALLY_VERIFIED', 'PARTIALLY_VERIFIED'), ('VERIFIED', 'VERIFIED'), ('TRANSMISSION_SUCCESSFUL', 'TRANSMISSION_SUCCESSFUL'), ('TRANSMISSION_FAILED', 'TRANSMISSION_FAILED')], default='UNVERIFIED', max_length=64)),
                ('error_message', models.TextField(blank=True, null=True)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_extractionbatchdata_related', related_query_name='packager_extractionbatchdatas', to='organizations.DataPool')),
                ('extraction_batch', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='extraction_batch_data', to='extract.ExtractionBatch')),
                ('package', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='extraction_batch_data', to='packager.Package')),
            ],
        ),
        migrations.CreateModel(
            name='DocumentRelationshipType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('label', models.CharField(max_length=64)),
                ('description', models.TextField(blank=True, null=True)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_documentrelationshiptype_related', related_query_name='packager_documentrelationshiptypes', to='organizations.DataPool')),
            ],
        ),
        migrations.CreateModel(
            name='DocumentRelationship',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('metadata', jsonfield.fields.JSONField(blank=True, default=dict)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_documentrelationship_related', related_query_name='packager_documentrelationships', to='organizations.DataPool')),
                ('relationship_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packager.DocumentRelationshipType')),
                ('source_document_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='source_of_relationships', to='packager.DocumentData')),
                ('target_document_data', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_of_relationships', to='packager.DocumentData')),
            ],
        ),
        migrations.CreateModel(
            name='CoCEventType',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('label', models.CharField(max_length=64)),
                ('description', models.TextField(blank=True, null=True)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_coceventtype_related', related_query_name='packager_coceventtypes', to='organizations.DataPool')),
            ],
        ),
        migrations.CreateModel(
            name='CoCEvent',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('_persistent_id_map', jsonfield.fields.JSONField(default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False)),
                ('timestamp', models.DateTimeField(auto_now_add=True, help_text='When the event was recorded')),
                ('details', jsonfield.fields.JSONField(blank=True, default=dict)),
                ('data_pool', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='packager_cocevent_related', related_query_name='packager_cocevents', to='organizations.DataPool')),
                ('document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coc_events', to='documents.Document')),
                ('event_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='packager.CoCEventType')),
                ('package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coc_events', to='packager.Package')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.AddIndex(
            model_name='packageentry',
            index=models.Index(fields=['package'], name='packager_pa_package_baefb4_idx'),
        ),
        migrations.AddIndex(
            model_name='packageentry',
            index=models.Index(fields=['document_data'], name='packager_pa_documen_d959e4_idx'),
        ),
        migrations.AddIndex(
            model_name='packageentry',
            index=models.Index(fields=['status_in_package'], name='packager_pa_status__ba2153_idx'),
        ),
        migrations.AddIndex(
            model_name='packageentry',
            index=models.Index(fields=['delivered_at'], name='packager_pa_deliver_3f7008_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='packageentry',
            unique_together={('package', 'document_data')},
        ),
        migrations.AlterUniqueTogether(
            name='extractionbatchdata',
            unique_together={('package', 'extraction_batch')},
        ),
        migrations.AlterUniqueTogether(
            name='documentrelationshiptype',
            unique_together={('label', 'data_pool')},
        ),
        migrations.AlterUniqueTogether(
            name='documentrelationship',
            unique_together={('source_document_data', 'target_document_data', 'relationship_type', 'data_pool')},
        ),
        migrations.AlterUniqueTogether(
            name='documentdata',
            unique_together={('document', 'data_pool')},
        ),
        migrations.AlterUniqueTogether(
            name='coceventtype',
            unique_together={('label', 'data_pool')},
        ),
    ]
