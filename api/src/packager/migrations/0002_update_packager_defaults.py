# Generated by Django 2.2.28 on 2025-06-03 06:16

from django.db import migrations, models
import jsonfield.fields
import packager.models


class Migration(migrations.Migration):

    dependencies = [
        ('packager', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='packager',
            name='coc_report_end_date',
            field=models.DateField(blank=True, default=packager.models.get_current_date, help_text='End date for CoC reports (defaults to current date)', null=True),
        ),
        migrations.AlterField(
            model_name='packager',
            name='coc_report_fields_config',
            field=jsonfield.fields.JSONField(blank=True, default=packager.models.get_default_coc_report_fields, help_text='List of fields to include in CoC reports', null=True),
        ),
        migrations.AlterField(
            model_name='packager',
            name='coc_report_start_date',
            field=models.DateField(blank=True, default=packager.models.get_current_year_start, help_text='Start date for CoC reports (defaults to beginning of current year)', null=True),
        ),
        migrations.AlterField(
            model_name='packager',
            name='deduplication_content_fields',
            field=jsonfield.fields.JSONField(blank=True, default=packager.models.get_default_deduplication_content_fields, help_text='List of fields to consider when checking for content duplicates', null=True),
        ),
        migrations.AlterField(
            model_name='packager',
            name='exclude_duplicates_from_delivery',
            field=models.BooleanField(default=True, help_text='When enabled, documents identified as duplicates will be excluded from package delivery'),
        ),
        migrations.AlterField(
            model_name='packager',
            name='include_relationship_details_in_data_export',
            field=models.BooleanField(default=True, help_text='Controls inclusion of relationship information (including duplicates) in data exports'),
        ),
        migrations.AlterField(
            model_name='packager',
            name='include_source_files_in_delivery',
            field=models.BooleanField(default=True, help_text='Include original source files in delivery packages'),
        ),
    ]
