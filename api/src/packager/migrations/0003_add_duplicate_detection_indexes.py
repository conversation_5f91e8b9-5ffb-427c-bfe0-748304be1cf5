from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('packager', '0002_update_packager_defaults'),
    ]

    operations = [
        migrations.RunSQL(
            "CREATE INDEX packager_documentdata_md5_duplicate_idx "
            "ON packager_documentdata (data_pool_id, source_file_md5, received_at, id);",
            reverse_sql="DROP INDEX packager_documentdata_md5_duplicate_idx ON packager_documentdata;"
        ),
        migrations.RunSQL(
            "CREATE INDEX packager_documentdata_label_duplicate_idx "
            "ON packager_documentdata (data_pool_id, source_file_name, received_at, id);",
            reverse_sql="DROP INDEX packager_documentdata_label_duplicate_idx ON packager_documentdata;"
        ),
        migrations.RunSQL(
            "CREATE INDEX packager_documentdata_content_duplicate_idx "
            "ON packager_documentdata (data_pool_id, content_hash, received_at, id);",
            reverse_sql="DROP INDEX packager_documentdata_content_duplicate_idx ON packager_documentdata;"
        ),

        migrations.RunSQL(
            "CREATE INDEX packager_documentrelationship_target_lookup_idx "
            "ON packager_documentrelationship (target_document_data_id, relationship_type_id);",
            reverse_sql="DROP INDEX packager_documentrelationship_target_lookup_idx ON packager_documentrelationship;"
        ),

        migrations.RunSQL(
            "CREATE INDEX packager_packageentry_document_lookup_idx "
            "ON packager_packageentry (document_data_id, package_id);",
            reverse_sql="DROP INDEX packager_packageentry_document_lookup_idx ON packager_packageentry;"
        ),
    ]
